

#user  nobody;
worker_processes 4;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
     worker_connections 40960;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
								   
    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';
		
    #access_log  logs/access.log  main;
     sendfile  on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
     keepalive_timeout 65;

    #gzip  on;



    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}
	#include vhosts.conf;
    map $time_iso8601 $logdate {
        '~^(?<ymd>\\d{4}-\\d{2}-\\d{2})' $ymd;
        default                       'date-not-found';
    }
	include vhosts/*.conf;
    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

     client_max_body_size 50m;
     client_body_buffer_size 60k;
     client_body_timeout 60;
     client_header_buffer_size 64k;
     client_header_timeout 60;
     error_page 400 /error/400.html;
     error_page 403 /error/403.html;
     error_page 404 /error/404.html;
     error_page 500 /error/500.html;
     error_page 501 /error/501.html;
     error_page 502 /error/502.html;
     error_page 503 /error/503.html;
     error_page 504 /error/504.html;
     error_page 505 /error/505.html;
     error_page 506 /error/506.html;
     error_page 507 /error/507.html;
     error_page 509 /error/509.html;
     error_page 510 /error/510.html;
     
     keepalive_requests 100;
     large_client_header_buffers 4 64k;
     reset_timedout_connection on;
     send_timeout 60;
     sendfile_max_chunk 512k;
     server_names_hash_bucket_size 256;
}
     worker_rlimit_nofile 100000;
