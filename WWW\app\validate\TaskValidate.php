<?php

namespace app\validate;

use think\Validate;
use app\service\TaskService;

/**
 * 任务验证器
 */
class TaskValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'type' => 'require|in:1,2',
        'keyword' => 'require|length:1,50|checkSensitive',
        'url' => 'url|length:1,500',
        'dropdown_words' => 'array|max:10',
        'search_engine' => 'require|in:baidu,google,sogou,360',
        'online_days' => 'require|integer|between:1,30',
        'daily_clicks' => 'integer|between:10,1000',
        'daily_searches' => 'integer|between:10,1000',
        'click_time_range' => 'in:morning,afternoon,evening,all_day',
        'region' => 'length:1,50'
    ];

    /**
     * 错误消息
     */
    protected $message = [
        'type.require' => '任务类型不能为空',
        'type.in' => '任务类型无效',
        'keyword.require' => '关键词不能为空',
        'keyword.length' => '关键词长度必须在1-50字符之间',
        'keyword.checkSensitive' => '关键词包含敏感词',
        'url.url' => 'URL格式不正确',
        'url.length' => 'URL长度不能超过500字符',
        'dropdown_words.array' => '下拉词必须是数组格式',
        'dropdown_words.max' => '下拉词最多10个',
        'search_engine.require' => '搜索引擎不能为空',
        'search_engine.in' => '搜索引擎类型无效',
        'online_days.require' => '上线天数不能为空',
        'online_days.integer' => '上线天数必须是整数',
        'online_days.between' => '上线天数必须在1-30天之间',
        'daily_clicks.integer' => '每日点击数必须是整数',
        'daily_clicks.between' => '每日点击数必须在10-1000之间',
        'daily_searches.integer' => '每日搜索数必须是整数',
        'daily_searches.between' => '每日搜索数必须在10-1000之间',
        'click_time_range.in' => '点击时间段无效',
        'region.length' => '地区长度不能超过50字符'
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'create' => ['type', 'keyword', 'url', 'dropdown_words', 'search_engine', 'online_days', 'daily_clicks', 'daily_searches', 'click_time_range', 'region'],
        'update' => ['keyword', 'url', 'dropdown_words', 'daily_clicks', 'daily_searches', 'click_time_range', 'region'],
        'review' => ['status', 'remark']
    ];

    /**
     * 创建任务场景
     */
    public function sceneCreate()
    {
        return $this->only(['type', 'keyword', 'url', 'dropdown_words', 'search_engine', 'online_days', 'daily_clicks', 'daily_searches', 'click_time_range', 'region'])
                   ->append('type', 'checkTaskType')
                   ->append('keyword', 'checkKeywordUnique');
    }

    /**
     * 更新任务场景
     */
    public function sceneUpdate()
    {
        return $this->only(['keyword', 'url', 'dropdown_words', 'daily_clicks', 'daily_searches', 'click_time_range', 'region'])
                   ->remove('keyword', 'require'); // 更新时关键词可选
    }

    /**
     * 审核任务场景
     */
    public function sceneReview()
    {
        return $this->only(['status', 'remark'])
                   ->rule([
                       'status' => 'require|in:2,5', // 2=通过，5=拒绝
                       'remark' => 'length:0,200'
                   ])
                   ->message([
                       'status.require' => '审核状态不能为空',
                       'status.in' => '审核状态无效',
                       'remark.length' => '审核备注长度不能超过200字符'
                   ]);
    }

    /**
     * 自定义验证：检查任务类型相关字段
     */
    protected function checkTaskType($value, $rule, $data = [])
    {
        // 关键词排名任务必须有URL
        if ($value == TaskService::TYPE_KEYWORD_RANKING) {
            if (empty($data['url'])) {
                return '关键词排名任务必须提供目标URL';
            }
            if (empty($data['daily_clicks'])) {
                return '关键词排名任务必须设置每日点击数';
            }
        }
        
        // 下拉词任务必须有下拉词列表
        if ($value == TaskService::TYPE_DROPDOWN_WORDS) {
            if (empty($data['dropdown_words']) || !is_array($data['dropdown_words'])) {
                return '下拉词任务必须提供下拉词列表';
            }
            if (count($data['dropdown_words']) == 0) {
                return '下拉词列表不能为空';
            }
            if (empty($data['daily_searches'])) {
                return '下拉词任务必须设置每日搜索数';
            }
        }
        
        return true;
    }

    /**
     * 自定义验证：检查关键词唯一性（同一用户同一搜索引擎）
     */
    protected function checkKeywordUnique($value, $rule, $data = [])
    {
        $userId = $data['user_id'] ?? 0;
        $searchEngine = $data['search_engine'] ?? '';
        $taskId = $data['task_id'] ?? 0;
        
        if (empty($userId) || empty($searchEngine)) {
            return true; // 如果没有用户ID或搜索引擎，跳过检查
        }
        
        $count = \think\facade\Db::name('tasks')
            ->where('user_id', $userId)
            ->where('keyword', $value)
            ->where('search_engine', $searchEngine)
            ->where('status', 'in', [TaskService::STATUS_PENDING, TaskService::STATUS_RUNNING])
            ->where('id', '<>', $taskId)
            ->count();
            
        return $count == 0 ? true : '该关键词在此搜索引擎上已有进行中的任务';
    }

    /**
     * 自定义验证：检查敏感词
     */
    protected function checkSensitive($value, $rule, $data = [])
    {
        if (!config('system.sensitive_word_check', true)) {
            return true; // 如果未开启敏感词检查，直接通过
        }
        
        $result = \app\service\SensitiveWordService::check($value);
        
        if (!$result['passed']) {
            return '关键词包含敏感词：' . implode('、', $result['words']);
        }
        
        return true;
    }

    /**
     * 自定义验证：检查下拉词格式
     */
    protected function checkDropdownWords($value, $rule, $data = [])
    {
        if (!is_array($value)) {
            return '下拉词必须是数组格式';
        }
        
        if (count($value) > 10) {
            return '下拉词最多10个';
        }
        
        foreach ($value as $word) {
            if (!is_string($word) || mb_strlen($word) > 30) {
                return '每个下拉词长度不能超过30字符';
            }
            
            // 检查敏感词
            if (config('system.sensitive_word_check', true)) {
                $result = \app\service\SensitiveWordService::check($word);
                if (!$result['passed']) {
                    return "下拉词「{$word}」包含敏感词";
                }
            }
        }
        
        return true;
    }

    /**
     * 自定义验证：检查URL可访问性
     */
    protected function checkUrlAccessible($value, $rule, $data = [])
    {
        if (empty($value)) {
            return true;
        }
        
        // 简单的URL格式检查
        if (!filter_var($value, FILTER_VALIDATE_URL)) {
            return 'URL格式不正确';
        }
        
        // 检查是否是HTTPS
        if (strpos($value, 'https://') !== 0 && strpos($value, 'http://') !== 0) {
            return 'URL必须以http://或https://开头';
        }
        
        // 可以添加更多URL检查逻辑，如域名黑名单等
        
        return true;
    }

    /**
     * 自定义验证：检查用户权限
     */
    protected function checkUserPermission($value, $rule, $data = [])
    {
        $userId = $data['user_id'] ?? 0;
        
        if (empty($userId)) {
            return '用户ID不能为空';
        }
        
        $user = \app\service\UserService::getUserById($userId);
        
        if (!$user) {
            return '用户不存在';
        }
        
        if ($user['status'] != 1) {
            return '用户状态异常';
        }
        
        // 检查今日任务限制
        $todayCount = \app\service\TaskService::getTodayTaskCount($userId);
        if ($todayCount >= $user['max_daily_tasks']) {
            return '今日任务数量已达上限';
        }
        
        return true;
    }
}
