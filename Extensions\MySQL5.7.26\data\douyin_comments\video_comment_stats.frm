TYPE=VIEW
query=select `douyin_comments`.`comments`.`video_id` AS `video_id`,count(0) AS `total_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'primary\') then 1 else 0 end)) AS `primary_comments_count`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'reply\') then 1 else 0 end)) AS `reply_comments_count`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes`,round(avg(`douyin_comments`.`comments`.`digg_count`),2) AS `avg_likes_per_comment`,min(`douyin_comments`.`comments`.`collection_time`) AS `first_collection_time`,max(`douyin_comments`.`comments`.`collection_time`) AS `last_collection_time`,count(distinct `douyin_comments`.`comments`.`uid`) AS `unique_users`,count(distinct cast(`douyin_comments`.`comments`.`collection_time` as date)) AS `collection_days`,sum((case when (`douyin_comments`.`comments`.`digg_count` >= 100) then 1 else 0 end)) AS `hot_comments_count`,max(`douyin_comments`.`comments`.`digg_count`) AS `max_likes`,round(avg(`douyin_comments`.`comments`.`reply_comment_total`),2) AS `avg_replies_per_primary` from `douyin_comments`.`comments` where ((`douyin_comments`.`comments`.`video_id` is not null) and (`douyin_comments`.`comments`.`video_id` <> \'\')) group by `douyin_comments`.`comments`.`video_id`
md5=306beab50b62cffb2f04a0ab53f28188
updatable=0
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=2025-06-23 12:49:47
create-version=1
source=SELECT 
\n    video_id,
\n    COUNT(*) as total_comments,
\n    SUM(CASE WHEN comment_type = \'primary\' THEN 1 ELSE 0 END) as primary_comments_count,
\n    SUM(CASE WHEN comment_type = \'reply\' THEN 1 ELSE 0 END) as reply_comments_count,
\n    SUM(digg_count) as total_likes,
\n    ROUND(AVG(digg_count), 2) as avg_likes_per_comment,
\n    MIN(collection_time) as first_collection_time,
\n    MAX(collection_time) as last_collection_time,
\n    COUNT(DISTINCT uid) as unique_users,
\n    COUNT(DISTINCT DATE(collection_time)) as collection_days,
\n    -- 热门评论统计
\n    SUM(CASE WHEN digg_count >= 100 THEN 1 ELSE 0 END) as hot_comments_count,
\n    MAX(digg_count) as max_likes,
\n    -- 回复活跃度
\n    ROUND(AVG(reply_comment_total), 2) as avg_replies_per_primary
\nFROM comments 
\nWHERE video_id IS NOT NULL AND video_id != \'\'
\nGROUP BY video_id
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_general_ci
view_body_utf8=select `douyin_comments`.`comments`.`video_id` AS `video_id`,count(0) AS `total_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'primary\') then 1 else 0 end)) AS `primary_comments_count`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'reply\') then 1 else 0 end)) AS `reply_comments_count`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes`,round(avg(`douyin_comments`.`comments`.`digg_count`),2) AS `avg_likes_per_comment`,min(`douyin_comments`.`comments`.`collection_time`) AS `first_collection_time`,max(`douyin_comments`.`comments`.`collection_time`) AS `last_collection_time`,count(distinct `douyin_comments`.`comments`.`uid`) AS `unique_users`,count(distinct cast(`douyin_comments`.`comments`.`collection_time` as date)) AS `collection_days`,sum((case when (`douyin_comments`.`comments`.`digg_count` >= 100) then 1 else 0 end)) AS `hot_comments_count`,max(`douyin_comments`.`comments`.`digg_count`) AS `max_likes`,round(avg(`douyin_comments`.`comments`.`reply_comment_total`),2) AS `avg_replies_per_primary` from `douyin_comments`.`comments` where ((`douyin_comments`.`comments`.`video_id` is not null) and (`douyin_comments`.`comments`.`video_id` <> \'\')) group by `douyin_comments`.`comments`.`video_id`
