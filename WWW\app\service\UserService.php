<?php

namespace app\service;

use think\facade\Db;
use think\facade\Cache;
use think\exception\ValidateException;
use app\service\SecurityService;
use app\service\JwtService;

/**
 * 用户服务类
 */
class UserService
{
    /**
     * 用户注册
     */
    public static function register($mobile, $password, $nickname = null)
    {
        // 验证手机号格式
        if (!self::validateMobile($mobile)) {
            throw new ValidateException('手机号格式不正确');
        }
        
        // 检查手机号是否已存在
        $mobileHash = self::hashMobile($mobile);
        if (self::getUserByMobileHash($mobileHash)) {
            throw new ValidateException('手机号已被注册');
        }
        
        // 加密手机号和密码
        $encryptedMobile = self::encryptMobile($mobile);
        $hashedPassword = password_hash($password, PASSWORD_BCRYPT);
        
        // 开启事务
        Db::startTrans();
        try {
            $userId = Db::name('users')->insertGetId([
                'mobile' => $encryptedMobile,
                'mobile_hash' => $mobileHash,
                'password' => $hashedPassword,
                'nickname' => $nickname ?: self::generateNickname(),
                'group_id' => 1, // 默认新手用户组
                'score' => 0,
                'status' => 1,
                'create_time' => time(),
                'update_time' => time()
            ]);
            
            // 记录注册日志
            SecurityService::logUserAction($userId, 'register', [
                'mobile_hash' => $mobileHash,
                'ip' => request()->ip()
            ]);
            
            Db::commit();
            
            // 清除相关缓存
            self::clearUserCache($userId);
            
            return $userId;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 用户登录
     */
    public static function login($mobile, $password, $remember = false)
    {
        $mobileHash = self::hashMobile($mobile);
        $user = self::getUserByMobileHash($mobileHash);
        
        if (!$user) {
            throw new ValidateException('用户不存在');
        }
        
        if ($user['status'] != 1) {
            throw new ValidateException('账户已被禁用');
        }
        
        // 验证密码
        $decryptedMobile = self::decryptMobile($user['mobile']);
        if ($decryptedMobile !== $mobile || !password_verify($password, $user['password'])) {
            // 记录登录失败日志
            SecurityService::logLoginAttempt($user['id'], request()->ip(), false);
            throw new ValidateException('手机号或密码错误');
        }
        
        // 更新登录信息
        Db::name('users')->where('id', $user['id'])->update([
            'last_login_time' => time(),
            'last_login_ip' => request()->ip(),
            'update_time' => time()
        ]);
        
        // 记录登录成功日志
        SecurityService::logLoginAttempt($user['id'], request()->ip(), true);
        
        // 生成JWT Token
        $token = JwtService::generateToken([
            'user_id' => $user['id'],
            'mobile_hash' => $user['mobile_hash'],
            'group_id' => $user['group_id']
        ], $remember ? 30 * 24 * 3600 : 7200); // 记住我30天，否则2小时
        
        // 清除用户缓存
        self::clearUserCache($user['id']);
        
        return [
            'token' => $token,
            'user_info' => self::formatUserInfo($user)
        ];
    }
    
    /**
     * 根据用户ID获取用户信息
     */
    public static function getUserById($userId)
    {
        $cacheKey = "user:info:{$userId}";
        
        return Cache::remember($cacheKey, function() use ($userId) {
            $user = Db::name('users')
                ->alias('u')
                ->leftJoin('user_groups g', 'u.group_id = g.id')
                ->field('u.*, g.name as group_name, g.score_cost, g.max_daily_tasks, g.benefits')
                ->where('u.id', $userId)
                ->find();
                
            if ($user) {
                $user['benefits'] = json_decode($user['benefits'] ?: '[]', true);
            }
            
            return $user;
        }, 300); // 缓存5分钟
    }
    
    /**
     * 根据手机号哈希获取用户
     */
    public static function getUserByMobileHash($mobileHash)
    {
        return Db::name('users')->where('mobile_hash', $mobileHash)->find();
    }
    
    /**
     * 更新用户信息
     */
    public static function updateUser($userId, $data)
    {
        $allowedFields = ['nickname', 'avatar'];
        $updateData = [];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }
        
        if (empty($updateData)) {
            return false;
        }
        
        $updateData['update_time'] = time();
        
        $result = Db::name('users')->where('id', $userId)->update($updateData);
        
        if ($result) {
            self::clearUserCache($userId);
        }
        
        return $result;
    }
    
    /**
     * 修改密码
     */
    public static function changePassword($userId, $oldPassword, $newPassword)
    {
        $user = Db::name('users')->where('id', $userId)->find();
        
        if (!$user) {
            throw new ValidateException('用户不存在');
        }
        
        if (!password_verify($oldPassword, $user['password'])) {
            throw new ValidateException('原密码错误');
        }
        
        $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT);
        
        $result = Db::name('users')->where('id', $userId)->update([
            'password' => $hashedPassword,
            'update_time' => time()
        ]);
        
        if ($result) {
            // 记录密码修改日志
            SecurityService::logUserAction($userId, 'change_password', [
                'ip' => request()->ip()
            ]);
        }
        
        return $result;
    }
    
    /**
     * 检查用户组升级
     */
    public static function checkGroupUpgrade($userId)
    {
        $user = self::getUserById($userId);
        if (!$user) {
            return false;
        }
        
        // 获取所有用户组
        $groups = Db::name('user_groups')
            ->where('status', 1)
            ->order('promotion_condition', 'asc')
            ->select();
        
        $targetGroupId = $user['group_id'];
        
        foreach ($groups as $group) {
            if ($group['id'] > $user['group_id'] && 
                $user['total_consumption'] >= $group['promotion_condition']) {
                $targetGroupId = $group['id'];
            }
        }
        
        // 如果需要升级
        if ($targetGroupId != $user['group_id']) {
            Db::name('users')->where('id', $userId)->update([
                'group_id' => $targetGroupId,
                'update_time' => time()
            ]);
            
            // 记录升级日志
            SecurityService::logUserAction($userId, 'group_upgrade', [
                'old_group_id' => $user['group_id'],
                'new_group_id' => $targetGroupId
            ]);
            
            self::clearUserCache($userId);
            
            return $targetGroupId;
        }
        
        return false;
    }
    
    /**
     * 验证手机号格式
     */
    private static function validateMobile($mobile)
    {
        return preg_match('/^1[3-9]\d{9}$/', $mobile);
    }
    
    /**
     * 手机号哈希
     */
    private static function hashMobile($mobile)
    {
        return hash('sha256', $mobile . config('app.salt', ''));
    }
    
    /**
     * 加密手机号
     */
    private static function encryptMobile($mobile)
    {
        $key = config('app.encrypt_key', '');
        return openssl_encrypt($mobile, 'AES-256-CBC', $key, 0, substr(md5($key), 0, 16));
    }
    
    /**
     * 解密手机号
     */
    private static function decryptMobile($encryptedMobile)
    {
        $key = config('app.encrypt_key', '');
        return openssl_decrypt($encryptedMobile, 'AES-256-CBC', $key, 0, substr(md5($key), 0, 16));
    }
    
    /**
     * 生成随机昵称
     */
    private static function generateNickname()
    {
        $prefixes = ['用户', '会员', '客户'];
        $suffix = mt_rand(100000, 999999);
        return $prefixes[array_rand($prefixes)] . $suffix;
    }
    
    /**
     * 格式化用户信息
     */
    private static function formatUserInfo($user)
    {
        return [
            'id' => $user['id'],
            'nickname' => $user['nickname'],
            'avatar' => $user['avatar'],
            'group_id' => $user['group_id'],
            'group_name' => $user['group_name'] ?? '',
            'score' => $user['score'],
            'total_recharge' => $user['total_recharge'],
            'total_consumption' => $user['total_consumption'],
            'today_consumption' => $user['today_consumption'],
            'score_cost' => $user['score_cost'] ?? 1.0,
            'max_daily_tasks' => $user['max_daily_tasks'] ?? 50,
            'benefits' => $user['benefits'] ?? [],
            'last_login_time' => $user['last_login_time'],
            'create_time' => $user['create_time']
        ];
    }
    
    /**
     * 清除用户缓存
     */
    private static function clearUserCache($userId)
    {
        Cache::delete("user:info:{$userId}");
        Cache::delete("user:score:{$userId}");
        Cache::delete("user:daily_tasks:{$userId}");
    }
}
