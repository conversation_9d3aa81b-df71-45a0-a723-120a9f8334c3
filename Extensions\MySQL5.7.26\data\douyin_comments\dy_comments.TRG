TYPE=TRIGGERS
triggers='CREATE DEFINER=`root`@`localhost` TRIGGER tr_comments_insert_stats
\nAFTER INSERT ON dy_comments
\nFOR EACH ROW
\nBEGIN
\n    -- 更新任务统计
\n    INSERT INTO dy_task_statistics (task_id, total_comments, primary_comments, reply_comments, high_intent_comments)
\n    VALUES (NEW.task_id, 1,
\n            CASE WHEN NEW.comment_type = \'primary\' THEN 1 ELSE 0 END,
\n            CASE WHEN NEW.comment_type = \'reply\' THEN 1 ELSE 0 END,
\n            CASE WHEN NEW.is_high_intent = TRUE THEN 1 ELSE 0 END)
\n    ON DUPLICATE KEY UPDATE
\n        total_comments = total_comments + 1,
\n        primary_comments = primary_comments + CASE WHEN NEW.comment_type = \'primary\' THEN 1 ELSE 0 END,
\n        reply_comments = reply_comments + CASE WHEN NEW.comment_type = \'reply\' THEN 1 ELSE 0 END,
\n        high_intent_comments = high_intent_comments + CASE WHEN NEW.is_high_intent = TRUE THEN 1 ELSE 0 END;
\n
\n    -- 更新账号统计
\n    INSERT INTO dy_account_statistics (account_id, total_comments, high_intent_comments)
\n    VALUES (NEW.account_id, 1, CASE WHEN NEW.is_high_intent = TRUE THEN 1 ELSE 0 END)
\n    ON DUPLICATE KEY UPDATE
\n        total_comments = total_comments + 1,
\n        high_intent_comments = high_intent_comments + CASE WHEN NEW.is_high_intent = TRUE THEN 1 ELSE 0 END;
\nEND'
sql_modes=**********
definers='root@localhost'
client_cs_names='utf8mb4'
connection_cl_names='utf8mb4_general_ci'
db_cl_names='utf8mb4_unicode_ci'
created=************
