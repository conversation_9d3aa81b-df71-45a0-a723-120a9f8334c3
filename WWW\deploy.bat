@echo off
chcp 65001 >nul
echo ========================================
echo SEO积分系统 - 快速部署脚本
echo ========================================
echo.

:: 检查PHP是否可用
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] PHP未安装或未添加到PATH环境变量
    echo 请先安装PHP并添加到系统PATH
    pause
    exit /b 1
)

:: 检查Composer是否可用
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Composer未安装或未添加到PATH环境变量
    echo 请先安装Composer
    pause
    exit /b 1
)

echo [信息] 开始部署SEO积分系统...
echo.

:: 1. 安装PHP依赖
echo [步骤1] 安装PHP依赖包...
call composer install --no-dev --optimize-autoloader
if %errorlevel% neq 0 (
    echo [错误] Composer依赖安装失败
    pause
    exit /b 1
)

:: 安装Redis扩展
echo [步骤2] 安装Redis扩展...
call composer require predis/predis
if %errorlevel% neq 0 (
    echo [警告] Redis扩展安装失败，请手动安装
)

:: 2. 创建环境配置文件
echo [步骤3] 创建环境配置文件...
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo [信息] 已创建.env配置文件，请手动修改数据库和Redis配置
    ) else (
        echo [信息] 创建默认.env配置文件...
        (
            echo APP_DEBUG=true
            echo APP_TRACE=false
            echo.
            echo [APP]
            echo DEFAULT_TIMEZONE=Asia/Shanghai
            echo.
            echo [DATABASE]
            echo TYPE=mysql
            echo HOSTNAME=127.0.0.1
            echo DATABASE=seo_points_system
            echo USERNAME=root
            echo PASSWORD=root
            echo HOSTPORT=3306
            echo CHARSET=utf8mb4
            echo DEBUG=true
            echo.
            echo [REDIS]
            echo HOST=127.0.0.1
            echo PORT=6379
            echo PASSWORD=
            echo SELECT=0
            echo.
            echo [JWT]
            echo SECRET=your-secret-key-here
            echo EXPIRE=7200
        ) > .env
        echo [信息] 已创建默认.env配置文件
    )
) else (
    echo [信息] .env配置文件已存在
)

:: 3. 创建必要目录
echo [步骤4] 创建必要目录...
if not exist "runtime" mkdir runtime
if not exist "runtime\cache" mkdir runtime\cache
if not exist "runtime\log" mkdir runtime\log
if not exist "runtime\temp" mkdir runtime\temp
if not exist "public\uploads" mkdir public\uploads
echo [信息] 目录创建完成

:: 4. 设置目录权限（Windows下通过attrib命令）
echo [步骤5] 设置目录权限...
attrib -r runtime /s /d >nul 2>&1
attrib -r public\uploads /s /d >nul 2>&1
echo [信息] 权限设置完成

:: 5. 检查数据库连接
echo [步骤6] 检查系统状态...
echo [信息] 请确保以下服务已启动：
echo   - MySQL服务
echo   - Redis服务
echo   - Nginx服务
echo.

:: 6. 显示部署信息
echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 接下来请完成以下步骤：
echo.
echo 1. 配置数据库：
echo    - 创建数据库：seo_points_system
echo    - 导入数据库：mysql -u root -p seo_points_system ^< database/install.sql
echo.
echo 2. 修改.env配置文件：
echo    - 数据库连接信息
echo    - Redis连接信息
echo    - JWT密钥设置
echo.
echo 3. 配置Nginx：
echo    - 复制vhosts配置文件到Nginx配置目录
echo    - 重启Nginx服务
echo.
echo 4. 访问地址：
echo    - 首页：http://localhost/
echo    - 用户中心：http://localhost/user/
echo    - 管理后台：http://localhost/admin/
echo    - 健康检查：http://localhost/health
echo.
echo 5. 默认账号：
echo    - 管理员：admin / admin123456
echo    - 测试用户：testuser / password123
echo.
echo 6. 设置定时任务（可选）：
echo    - 任务执行：* * * * * cd %cd% ^&^& php think task:execute
echo    - 积分结算：0 * * * * cd %cd% ^&^& php think score:settlement
echo    - 数据清理：0 2 * * * cd %cd% ^&^& php think data:cleanup
echo    - 系统优化：0 3 * * 0 cd %cd% ^&^& php think system:optimize
echo.
echo ========================================
echo 详细说明请查看：安装命令.txt
echo ========================================
echo.

:: 询问是否立即测试
set /p test_now="是否立即测试系统健康状态？(y/n): "
if /i "%test_now%"=="y" (
    echo.
    echo [测试] 启动内置服务器进行测试...
    echo [信息] 请在浏览器中访问：http://localhost:8000/health
    echo [信息] 按Ctrl+C停止测试服务器
    echo.
    php think run -p 8000
)

pause
