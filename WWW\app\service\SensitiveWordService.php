<?php

namespace app\service;

use think\facade\Db;
use think\facade\Cache;

/**
 * 敏感词服务类
 */
class SensitiveWordService
{
    // 敏感词级别
    const LEVEL_NORMAL = 1;   // 一般
    const LEVEL_SERIOUS = 2;  // 严重
    const LEVEL_FORBIDDEN = 3; // 禁止
    
    // 处理方式
    const ACTION_WARNING = 1;    // 警告
    const ACTION_REJECT = 2;     // 拒绝
    const ACTION_REPLACE = 3;    // 替换
    
    /**
     * 检查敏感词
     */
    public static function check($text, $category = null)
    {
        if (empty($text)) {
            return [
                'passed' => true,
                'words' => [],
                'level' => 0,
                'action' => 0
            ];
        }
        
        $sensitiveWords = self::getSensitiveWords($category);
        $foundWords = [];
        $maxLevel = 0;
        $maxAction = 0;
        
        foreach ($sensitiveWords as $wordInfo) {
            $word = $wordInfo['word'];
            
            // 检查是否包含敏感词（支持通配符）
            if (self::containsSensitiveWord($text, $word)) {
                $foundWords[] = [
                    'word' => $word,
                    'level' => $wordInfo['level'],
                    'action' => $wordInfo['action'],
                    'replacement' => $wordInfo['replacement']
                ];
                
                $maxLevel = max($maxLevel, $wordInfo['level']);
                $maxAction = max($maxAction, $wordInfo['action']);
            }
        }
        
        return [
            'passed' => empty($foundWords) || $maxAction == self::ACTION_WARNING,
            'words' => array_column($foundWords, 'word'),
            'details' => $foundWords,
            'level' => $maxLevel,
            'action' => $maxAction,
            'filtered_text' => self::filterText($text, $foundWords)
        ];
    }
    
    /**
     * 过滤敏感词
     */
    public static function filter($text, $category = null)
    {
        $checkResult = self::check($text, $category);
        
        if ($checkResult['action'] == self::ACTION_REJECT) {
            return false; // 拒绝处理
        }
        
        return $checkResult['filtered_text'];
    }
    
    /**
     * 添加敏感词
     */
    public static function addSensitiveWord($word, $category = null, $level = self::LEVEL_NORMAL, $action = self::ACTION_WARNING, $replacement = null)
    {
        $data = [
            'word' => trim($word),
            'category' => $category,
            'level' => $level,
            'action' => $action,
            'replacement' => $replacement,
            'status' => 1,
            'create_time' => time(),
            'update_time' => time()
        ];
        
        $result = Db::name('sensitive_words')->insert($data);
        
        if ($result) {
            self::clearSensitiveWordsCache();
        }
        
        return $result;
    }
    
    /**
     * 批量添加敏感词
     */
    public static function batchAddSensitiveWords($words, $category = null, $level = self::LEVEL_NORMAL, $action = self::ACTION_WARNING)
    {
        if (empty($words)) {
            return 0;
        }
        
        $data = [];
        $now = time();
        
        foreach ($words as $word) {
            $word = trim($word);
            if (!empty($word)) {
                $data[] = [
                    'word' => $word,
                    'category' => $category,
                    'level' => $level,
                    'action' => $action,
                    'replacement' => null,
                    'status' => 1,
                    'create_time' => $now,
                    'update_time' => $now
                ];
            }
        }
        
        if (empty($data)) {
            return 0;
        }
        
        $result = Db::name('sensitive_words')->insertAll($data);
        
        if ($result) {
            self::clearSensitiveWordsCache();
        }
        
        return count($data);
    }
    
    /**
     * 更新敏感词
     */
    public static function updateSensitiveWord($id, $data)
    {
        $allowedFields = ['word', 'category', 'level', 'action', 'replacement', 'status'];
        $updateData = [];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }
        
        if (empty($updateData)) {
            return false;
        }
        
        $updateData['update_time'] = time();
        
        $result = Db::name('sensitive_words')->where('id', $id)->update($updateData);
        
        if ($result) {
            self::clearSensitiveWordsCache();
        }
        
        return $result;
    }
    
    /**
     * 删除敏感词
     */
    public static function deleteSensitiveWord($id)
    {
        $result = Db::name('sensitive_words')->where('id', $id)->delete();
        
        if ($result) {
            self::clearSensitiveWordsCache();
        }
        
        return $result;
    }
    
    /**
     * 获取敏感词列表
     */
    public static function getSensitiveWordsList($page = 1, $limit = 20, $filters = [])
    {
        $where = [['status', '=', 1]];
        
        if (isset($filters['category']) && $filters['category'] !== '') {
            $where[] = ['category', '=', $filters['category']];
        }
        
        if (isset($filters['level']) && $filters['level'] !== '') {
            $where[] = ['level', '=', $filters['level']];
        }
        
        if (isset($filters['word']) && $filters['word'] !== '') {
            $where[] = ['word', 'like', '%' . $filters['word'] . '%'];
        }
        
        $query = Db::name('sensitive_words')->where($where)->order('create_time', 'desc');
        
        $total = $query->count();
        $list = $query->page($page, $limit)->select();
        
        // 格式化数据
        foreach ($list as &$item) {
            $item['level_text'] = self::getLevelText($item['level']);
            $item['action_text'] = self::getActionText($item['action']);
            $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
        }
        
        return [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 获取敏感词（带缓存）
     */
    private static function getSensitiveWords($category = null)
    {
        $cacheKey = 'sensitive_words:' . ($category ?: 'all');
        
        return Cache::remember($cacheKey, function() use ($category) {
            $where = [['status', '=', 1]];
            
            if ($category) {
                $where[] = ['category', '=', $category];
            }
            
            return Db::name('sensitive_words')
                ->where($where)
                ->order('level', 'desc')
                ->select()
                ->toArray();
        }, 3600); // 缓存1小时
    }
    
    /**
     * 检查文本是否包含敏感词
     */
    private static function containsSensitiveWord($text, $sensitiveWord)
    {
        // 简单的包含检查
        if (stripos($text, $sensitiveWord) !== false) {
            return true;
        }
        
        // 支持通配符匹配
        if (strpos($sensitiveWord, '*') !== false) {
            $pattern = str_replace('*', '.*', preg_quote($sensitiveWord, '/'));
            return preg_match('/' . $pattern . '/i', $text);
        }
        
        return false;
    }
    
    /**
     * 过滤文本中的敏感词
     */
    private static function filterText($text, $foundWords)
    {
        $filteredText = $text;
        
        foreach ($foundWords as $wordInfo) {
            $word = $wordInfo['word'];
            $action = $wordInfo['action'];
            $replacement = $wordInfo['replacement'];
            
            if ($action == self::ACTION_REPLACE) {
                if ($replacement) {
                    $filteredText = str_ireplace($word, $replacement, $filteredText);
                } else {
                    // 默认用星号替换
                    $stars = str_repeat('*', mb_strlen($word));
                    $filteredText = str_ireplace($word, $stars, $filteredText);
                }
            }
        }
        
        return $filteredText;
    }
    
    /**
     * 获取级别文本
     */
    public static function getLevelText($level)
    {
        $levels = [
            self::LEVEL_NORMAL => '一般',
            self::LEVEL_SERIOUS => '严重',
            self::LEVEL_FORBIDDEN => '禁止'
        ];
        
        return $levels[$level] ?? '未知';
    }
    
    /**
     * 获取处理方式文本
     */
    public static function getActionText($action)
    {
        $actions = [
            self::ACTION_WARNING => '警告',
            self::ACTION_REJECT => '拒绝',
            self::ACTION_REPLACE => '替换'
        ];
        
        return $actions[$action] ?? '未知';
    }
    
    /**
     * 清除敏感词缓存
     */
    private static function clearSensitiveWordsCache()
    {
        Cache::delete('sensitive_words:all');
        
        // 清除所有分类的缓存
        $categories = Db::name('sensitive_words')
            ->where('status', 1)
            ->group('category')
            ->column('category');
        
        foreach ($categories as $category) {
            if ($category) {
                Cache::delete('sensitive_words:' . $category);
            }
        }
    }
    
    /**
     * 导入敏感词文件
     */
    public static function importFromFile($filePath, $category = null, $level = self::LEVEL_NORMAL, $action = self::ACTION_WARNING)
    {
        if (!file_exists($filePath)) {
            throw new \Exception('文件不存在');
        }
        
        $content = file_get_contents($filePath);
        $words = array_filter(array_map('trim', explode("\n", $content)));
        
        return self::batchAddSensitiveWords($words, $category, $level, $action);
    }
}
