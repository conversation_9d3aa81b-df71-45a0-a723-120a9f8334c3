<?php

namespace app\controller\admin;

use think\Request;
use think\exception\ValidateException;
use app\service\JwtService;
use app\service\SecurityService;
use app\controller\api\BaseController;
use app\validate\AdminValidate;

/**
 * 管理员认证控制器
 */
class AuthController extends BaseController
{
    /**
     * 管理员登录
     */
    public function login(Request $request)
    {
        try {
            $data = $request->post();
            
            // 验证数据
            $validate = new AdminValidate();
            if (!$validate->scene('login')->check($data)) {
                return $this->error($validate->getError());
            }
            
            $username = $data['username'];
            $password = $data['password'];
            $captcha = $data['captcha'] ?? '';
            
            // 检查验证码（如果需要）
            if (config('system.admin_captcha_enabled', true)) {
                if (empty($captcha)) {
                    return $this->error('验证码不能为空');
                }
                
                if (!captcha_check($captcha)) {
                    return $this->error('验证码错误');
                }
            }
            
            // 检查登录失败次数
            $ip = $request->ip();
            $failureKey = 'admin_login_failure:' . $ip;
            $failureCount = cache($failureKey) ?: 0;
            
            if ($failureCount >= 5) {
                return $this->error('登录失败次数过多，请稍后再试', 429);
            }
            
            // 查找管理员
            $admin = \think\facade\Db::name('admins')
                ->where('username', $username)
                ->find();
            
            if (!$admin) {
                // 记录失败次数
                cache($failureKey, $failureCount + 1, 1800); // 30分钟
                return $this->error('用户名或密码错误');
            }
            
            // 验证密码
            if (!password_verify($password, $admin['password'])) {
                // 记录失败次数
                cache($failureKey, $failureCount + 1, 1800);
                
                // 记录安全日志
                SecurityService::logUserAction($admin['id'], 'admin_login_failed', [
                    'ip' => $ip,
                    'user_agent' => $request->header('User-Agent'),
                    'reason' => 'password_error'
                ]);
                
                return $this->error('用户名或密码错误');
            }
            
            // 检查管理员状态
            if ($admin['status'] != 1) {
                return $this->error('账号已被禁用');
            }
            
            // 清除失败次数
            cache($failureKey, null);
            
            // 生成JWT令牌
            $payload = [
                'admin_id' => $admin['id'],
                'username' => $admin['username'],
                'role' => $admin['role'],
                'type' => 'admin'
            ];
            
            $token = JwtService::generateToken($payload, 'admin');
            
            // 更新最后登录信息
            \think\facade\Db::name('admins')
                ->where('id', $admin['id'])
                ->update([
                    'last_login_time' => time(),
                    'last_login_ip' => $ip,
                    'login_count' => $admin['login_count'] + 1
                ]);
            
            // 记录登录日志
            SecurityService::logUserAction($admin['id'], 'admin_login', [
                'ip' => $ip,
                'user_agent' => $request->header('User-Agent')
            ]);
            
            // 返回登录信息
            $adminInfo = [
                'id' => $admin['id'],
                'username' => $admin['username'],
                'nickname' => $admin['nickname'],
                'role' => $admin['role'],
                'permissions' => $this->getAdminPermissions($admin['role']),
                'avatar' => $admin['avatar'] ?: '',
                'last_login_time' => $admin['last_login_time'],
                'login_count' => $admin['login_count'] + 1
            ];
            
            return $this->success([
                'token' => $token,
                'admin' => $adminInfo
            ], '登录成功');
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('登录失败：' . $e->getMessage());
        }
    }
    
    /**
     * 管理员登出
     */
    public function logout(Request $request)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            // 获取token
            $token = $request->header('Authorization');
            if ($token && strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
                
                // 将token加入黑名单
                JwtService::revokeToken($token);
            }
            
            // 记录登出日志
            SecurityService::logUserAction($admin['id'], 'admin_logout', [
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);
            
            return $this->success(null, '登出成功');
            
        } catch (\Exception $e) {
            return $this->error('登出失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取管理员信息
     */
    public function profile(Request $request)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            $adminInfo = [
                'id' => $admin['id'],
                'username' => $admin['username'],
                'nickname' => $admin['nickname'],
                'role' => $admin['role'],
                'permissions' => $this->getAdminPermissions($admin['role']),
                'avatar' => $admin['avatar'] ?: '',
                'email' => $admin['email'] ?: '',
                'phone' => $admin['phone'] ?: '',
                'last_login_time' => $admin['last_login_time'],
                'login_count' => $admin['login_count'],
                'create_time' => $admin['create_time']
            ];
            
            return $this->success($adminInfo);
            
        } catch (\Exception $e) {
            return $this->error('获取管理员信息失败：' . $e->getMessage());
        }
    }
    
    /**
     * 修改密码
     */
    public function changePassword(Request $request)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            $data = $request->post();
            
            // 验证数据
            $validate = new AdminValidate();
            if (!$validate->scene('changePassword')->check($data)) {
                return $this->error($validate->getError());
            }
            
            $oldPassword = $data['old_password'];
            $newPassword = $data['new_password'];
            
            // 验证原密码
            if (!password_verify($oldPassword, $admin['password'])) {
                return $this->error('原密码错误');
            }
            
            // 更新密码
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            
            $result = \think\facade\Db::name('admins')
                ->where('id', $admin['id'])
                ->update([
                    'password' => $hashedPassword,
                    'update_time' => time()
                ]);
            
            if ($result) {
                // 记录操作日志
                SecurityService::logUserAction($admin['id'], 'admin_change_password', [
                    'ip' => $request->ip(),
                    'user_agent' => $request->header('User-Agent')
                ]);
                
                return $this->success(null, '密码修改成功');
            } else {
                return $this->error('密码修改失败');
            }
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('修改密码失败：' . $e->getMessage());
        }
    }
    
    /**
     * 刷新令牌
     */
    public function refresh(Request $request)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            // 获取当前token
            $token = $request->header('Authorization');
            if (!$token || strpos($token, 'Bearer ') !== 0) {
                return $this->error('无效的令牌格式', 401);
            }
            
            $token = substr($token, 7);
            
            // 刷新token
            $newToken = JwtService::refreshToken($token);
            
            if (!$newToken) {
                return $this->error('令牌刷新失败', 401);
            }
            
            return $this->success([
                'token' => $newToken
            ], '令牌刷新成功');
            
        } catch (\Exception $e) {
            return $this->error('刷新令牌失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取管理员权限
     */
    private function getAdminPermissions($role)
    {
        $permissions = [
            'super_admin' => [
                'dashboard', 'users', 'tasks', 'financial', 'settings', 'logs', 'admins'
            ],
            'admin' => [
                'dashboard', 'users', 'tasks', 'financial'
            ],
            'operator' => [
                'dashboard', 'tasks'
            ]
        ];
        
        return $permissions[$role] ?? [];
    }
}
