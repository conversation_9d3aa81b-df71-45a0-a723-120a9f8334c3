TYPE=TRIGGERS
triggers='CREATE DEFINER=`root`@`localhost` TRIGGER tr_message_logs_insert_stats
\nAFTER INSERT ON dy_message_logs
\nFOR EACH ROW
\nBEGIN
\n    -- 更新任务统计
\n    IF NEW.task_id IS NOT NULL THEN
\n        INSERT INTO dy_task_statistics (task_id, messages_sent_success, messages_sent_failed)
\n        VALUES (NEW.task_id,
\n                CASE WHEN NEW.send_status = \'success\' THEN 1 ELSE 0 END,
\n                CASE WHEN NEW.send_status = \'failed\' THEN 1 ELSE 0 END)
\n        ON DUPLICATE KEY UPDATE
\n            messages_sent_success = messages_sent_success + CASE WHEN NEW.send_status = \'success\' THEN 1 ELSE 0 END,
\n            messages_sent_failed = messages_sent_failed + CASE WHEN NEW.send_status = \'failed\' THEN 1 ELSE 0 END;
\n    END IF;
\n
\n    -- 更新账号统计
\n    INSERT INTO dy_account_statistics (account_id, total_messages_sent, messages_sent_today, last_message_time)
\n    VALUES (NEW.account_id, 1,
\n            CASE WHEN DATE(NEW.sent_time) = CURDATE() THEN 1 ELSE 0 END,
\n            NEW.sent_time)
\n    ON DUPLICATE KEY UPDATE
\n        total_messages_sent = total_messages_sent + 1,
\n        messages_sent_today = CASE WHEN DATE(NEW.sent_time) = CURDATE()
\n                                  THEN messages_sent_today + 1
\n                                  ELSE CASE WHEN DATE(last_message_time) != CURDATE() THEN 1 ELSE messages_sent_today + 1 END
\n                              END,
\n        last_message_time = NEW.sent_time;
\nEND'
sql_modes=**********
definers='root@localhost'
client_cs_names='utf8mb4'
connection_cl_names='utf8mb4_general_ci'
db_cl_names='utf8mb4_unicode_ci'
created=************
