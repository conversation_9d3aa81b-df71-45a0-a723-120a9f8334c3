<?php

namespace app\service;

use think\facade\Db;
use think\facade\Cache;

/**
 * 任务执行服务类
 */
class TaskExecutionService
{
    // 执行状态
    const STATUS_SUCCESS = 1;  // 成功
    const STATUS_FAILED = 2;   // 失败
    const STATUS_TIMEOUT = 3;  // 超时
    const STATUS_SKIPPED = 4;  // 跳过
    
    /**
     * 记录任务执行日志
     */
    public static function logExecution($taskId, $status, $details = [], $executionTime = null)
    {
        $logData = [
            'task_id' => $taskId,
            'status' => $status,
            'execution_time' => $executionTime ?: time(),
            'details' => json_encode($details),
            'ip' => self::getExecutorIp(),
            'user_agent' => self::getExecutorUserAgent(),
            'create_time' => time()
        ];
        
        return Db::name('task_execution_logs')->insert($logData);
    }
    
    /**
     * 获取任务执行统计
     */
    public static function getExecutionStats($taskId, $days = 7)
    {
        $startTime = time() - ($days * 24 * 3600);
        
        $stats = Db::name('task_execution_logs')
            ->where('task_id', $taskId)
            ->where('execution_time', '>=', $startTime)
            ->field([
                'status',
                'COUNT(*) as count',
                'DATE(FROM_UNIXTIME(execution_time)) as date'
            ])
            ->group('status, date')
            ->order('date', 'desc')
            ->select();
        
        $result = [
            'total_executions' => 0,
            'success_count' => 0,
            'failed_count' => 0,
            'timeout_count' => 0,
            'skipped_count' => 0,
            'success_rate' => 0,
            'daily_stats' => []
        ];
        
        $dailyStats = [];
        
        foreach ($stats as $stat) {
            $result['total_executions'] += $stat['count'];
            
            switch ($stat['status']) {
                case self::STATUS_SUCCESS:
                    $result['success_count'] += $stat['count'];
                    break;
                case self::STATUS_FAILED:
                    $result['failed_count'] += $stat['count'];
                    break;
                case self::STATUS_TIMEOUT:
                    $result['timeout_count'] += $stat['count'];
                    break;
                case self::STATUS_SKIPPED:
                    $result['skipped_count'] += $stat['count'];
                    break;
            }
            
            if (!isset($dailyStats[$stat['date']])) {
                $dailyStats[$stat['date']] = [
                    'date' => $stat['date'],
                    'total' => 0,
                    'success' => 0,
                    'failed' => 0,
                    'timeout' => 0,
                    'skipped' => 0
                ];
            }
            
            $dailyStats[$stat['date']]['total'] += $stat['count'];
            $dailyStats[$stat['date']][self::getStatusKey($stat['status'])] += $stat['count'];
        }
        
        // 计算成功率
        if ($result['total_executions'] > 0) {
            $result['success_rate'] = round(($result['success_count'] / $result['total_executions']) * 100, 2);
        }
        
        $result['daily_stats'] = array_values($dailyStats);
        
        return $result;
    }
    
    /**
     * 获取任务执行日志
     */
    public static function getExecutionLogs($taskId, $page = 1, $limit = 20, $status = null)
    {
        $where = [['task_id', '=', $taskId]];
        
        if ($status !== null) {
            $where[] = ['status', '=', $status];
        }
        
        $query = Db::name('task_execution_logs')
            ->where($where)
            ->order('execution_time', 'desc');
        
        $total = $query->count();
        $list = $query->page($page, $limit)->select();
        
        // 格式化数据
        foreach ($list as &$item) {
            $item['status_text'] = self::getStatusText($item['status']);
            $item['execution_time_text'] = date('Y-m-d H:i:s', $item['execution_time']);
            $item['details'] = json_decode($item['details'] ?: '{}', true);
        }
        
        return [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 批量记录执行日志
     */
    public static function batchLogExecution($logs)
    {
        if (empty($logs)) {
            return 0;
        }
        
        $data = [];
        $now = time();
        
        foreach ($logs as $log) {
            $data[] = [
                'task_id' => $log['task_id'],
                'status' => $log['status'],
                'execution_time' => $log['execution_time'] ?? $now,
                'details' => json_encode($log['details'] ?? []),
                'ip' => $log['ip'] ?? self::getExecutorIp(),
                'user_agent' => $log['user_agent'] ?? self::getExecutorUserAgent(),
                'create_time' => $now
            ];
        }
        
        return Db::name('task_execution_logs')->insertAll($data);
    }
    
    /**
     * 获取任务最近执行状态
     */
    public static function getLastExecutionStatus($taskId)
    {
        $log = Db::name('task_execution_logs')
            ->where('task_id', $taskId)
            ->order('execution_time', 'desc')
            ->find();
        
        if (!$log) {
            return null;
        }
        
        return [
            'status' => $log['status'],
            'status_text' => self::getStatusText($log['status']),
            'execution_time' => $log['execution_time'],
            'execution_time_text' => date('Y-m-d H:i:s', $log['execution_time']),
            'details' => json_decode($log['details'] ?: '{}', true)
        ];
    }
    
    /**
     * 清理过期的执行日志
     */
    public static function cleanExpiredLogs($days = 30)
    {
        $expireTime = time() - ($days * 24 * 3600);
        
        return Db::name('task_execution_logs')
            ->where('execution_time', '<', $expireTime)
            ->delete();
    }
    
    /**
     * 获取执行器IP
     */
    private static function getExecutorIp()
    {
        // 如果是定时任务执行，可能没有真实IP
        return request()->ip() ?: '127.0.0.1';
    }
    
    /**
     * 获取执行器User-Agent
     */
    private static function getExecutorUserAgent()
    {
        return request()->header('User-Agent') ?: 'Task Executor';
    }
    
    /**
     * 获取状态对应的键名
     */
    private static function getStatusKey($status)
    {
        $keys = [
            self::STATUS_SUCCESS => 'success',
            self::STATUS_FAILED => 'failed',
            self::STATUS_TIMEOUT => 'timeout',
            self::STATUS_SKIPPED => 'skipped'
        ];
        
        return $keys[$status] ?? 'unknown';
    }
    
    /**
     * 获取状态文本
     */
    public static function getStatusText($status)
    {
        $statuses = [
            self::STATUS_SUCCESS => '成功',
            self::STATUS_FAILED => '失败',
            self::STATUS_TIMEOUT => '超时',
            self::STATUS_SKIPPED => '跳过'
        ];
        
        return $statuses[$status] ?? '未知';
    }
    
    /**
     * 分析任务执行趋势
     */
    public static function analyzeExecutionTrend($taskId, $days = 30)
    {
        $startTime = time() - ($days * 24 * 3600);
        
        $trend = Db::name('task_execution_logs')
            ->where('task_id', $taskId)
            ->where('execution_time', '>=', $startTime)
            ->field([
                'DATE(FROM_UNIXTIME(execution_time)) as date',
                'COUNT(*) as total_count',
                'SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count',
                'AVG(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_rate'
            ])
            ->group('date')
            ->order('date', 'asc')
            ->select();
        
        $result = [
            'trend_data' => [],
            'avg_success_rate' => 0,
            'total_days' => count($trend),
            'best_day' => null,
            'worst_day' => null
        ];
        
        $totalSuccessRate = 0;
        $bestRate = -1;
        $worstRate = 101;
        
        foreach ($trend as $item) {
            $successRate = round($item['success_rate'] * 100, 2);
            $totalSuccessRate += $successRate;
            
            $trendItem = [
                'date' => $item['date'],
                'total_count' => $item['total_count'],
                'success_count' => $item['success_count'],
                'success_rate' => $successRate
            ];
            
            $result['trend_data'][] = $trendItem;
            
            if ($successRate > $bestRate) {
                $bestRate = $successRate;
                $result['best_day'] = $trendItem;
            }
            
            if ($successRate < $worstRate) {
                $worstRate = $successRate;
                $result['worst_day'] = $trendItem;
            }
        }
        
        if (count($trend) > 0) {
            $result['avg_success_rate'] = round($totalSuccessRate / count($trend), 2);
        }
        
        return $result;
    }
}
