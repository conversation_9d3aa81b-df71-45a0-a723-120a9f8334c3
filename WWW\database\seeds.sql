-- SEO积分系统初始化数据
-- 创建时间: 2024-12-25

SET NAMES utf8mb4;

-- ----------------------------
-- 用户组初始数据
-- ----------------------------
INSERT INTO `user_groups` (`id`, `name`, `score_cost`, `promotion_condition`, `max_daily_tasks`, `benefits`, `sort`, `status`, `create_time`, `update_time`) VALUES
(1, '新手用户', 1.00, 1000.00, 20, '["基础功能"]', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '普通用户', 0.90, 5000.00, 30, '["9折优惠", "优先审核"]', 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '高级用户', 0.80, 20000.00, 40, '["8折优惠", "专属客服", "高级功能"]', 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, '元老用户', 0.70, 50000.00, 50, '["7折优惠", "VIP通道", "定制服务"]', 4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(5, 'VIP用户', 0.60, 100000.00, 100, '["6折优惠", "专属经理", "无限制功能"]', 5, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- ----------------------------
-- 系统配置初始数据
-- ----------------------------
INSERT INTO `system_configs` (`key`, `value`, `description`, `type`, `status`, `create_time`, `update_time`) VALUES
('site_name', 'SEO积分系统', '网站名称', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('site_logo', '/static/images/logo.png', '网站Logo', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('contact_phone', '400-xxx-xxxx', '联系电话', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('contact_email', '<EMAIL>', '联系邮箱', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('icp_number', '京ICP备xxxxxxxx号', 'ICP备案号', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('copyright', '© 2024 SEO积分系统 版权所有', '版权信息', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('default_score_cost', '1.0', '默认积分单价', 'float', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('min_recharge_amount', '100', '最小充值金额', 'integer', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('max_daily_tasks', '50', '每日最大任务数', 'integer', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('task_review_timeout', '24', '任务审核超时时间（小时）', 'integer', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('score_refund_rate', '0.8', '积分退款比例', 'float', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('sensitive_word_check', '1', '是否启用敏感词检测', 'boolean', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('auto_approve_keywords', '0', '是否自动审核关键词', 'boolean', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('max_keyword_length', '50', '关键词最大长度', 'integer', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('min_online_days', '1', '最小上线天数', 'integer', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('max_online_days', '30', '最大上线天数', 'integer', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('slow_query_threshold', '1000', '慢查询阈值（毫秒）', 'integer', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('memory_threshold', '134217728', '内存使用阈值（字节）', 'integer', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- ----------------------------
-- 敏感词初始数据
-- ----------------------------
INSERT INTO `sensitive_words` (`word`, `category`, `level`, `action`, `replacement`, `status`, `create_time`, `update_time`) VALUES
('赌博', '违法违规', 3, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('博彩', '违法违规', 3, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('色情', '违法违规', 3, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('毒品', '违法违规', 3, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('枪支', '违法违规', 3, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('炸药', '违法违规', 3, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('假证', '违法违规', 3, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('发票', '违法违规', 2, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('刷单', '作弊行为', 2, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('刷量', '作弊行为', 2, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('作弊', '作弊行为', 2, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('外挂', '作弊行为', 2, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('病毒', '恶意软件', 2, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('木马', '恶意软件', 2, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('钓鱼', '恶意软件', 2, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('诈骗', '违法违规', 3, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('传销', '违法违规', 3, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('洗钱', '违法违规', 3, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('恐怖', '违法违规', 3, 2, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('暴力', '违法违规', 2, 1, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('血腥', '违法违规', 2, 1, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('政治', '敏感内容', 2, 1, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('宗教', '敏感内容', 1, 1, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('民族', '敏感内容', 1, 1, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- ----------------------------
-- 创建管理员账户（默认密码：admin123）
-- ----------------------------
INSERT INTO `users` (`mobile`, `mobile_hash`, `password`, `nickname`, `group_id`, `score`, `status`, `create_time`, `update_time`) VALUES
('admin', SHA2('admin', 256), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 5, 0.00, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- ----------------------------
-- 创建测试用户（默认密码：123456）
-- ----------------------------
INSERT INTO `users` (`mobile`, `mobile_hash`, `password`, `nickname`, `group_id`, `score`, `status`, `create_time`, `update_time`) VALUES
('13800138000', SHA2('13800138000', 256), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户', 1, 1000.00, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
