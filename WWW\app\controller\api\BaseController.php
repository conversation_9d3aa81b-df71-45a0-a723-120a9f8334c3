<?php

namespace app\controller\api;

use think\App;
use think\Response;

/**
 * API基础控制器
 */
class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 构造方法
     * @param App $app
     */
    public function __construct(App $app)
    {
        $this->app = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    /**
     * 初始化
     */
    protected function initialize()
    {
        // 设置跨域
        $this->setCors();
    }

    /**
     * 设置跨域
     */
    protected function setCors()
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization');
        
        // 处理预检请求
        if ($this->request->method() == 'OPTIONS') {
            exit();
        }
    }

    /**
     * 成功响应
     * @param mixed $data 数据
     * @param string $message 消息
     * @param int $code 状态码
     * @return Response
     */
    protected function success($data = null, $message = 'success', $code = 200)
    {
        $result = [
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ];

        return json($result);
    }

    /**
     * 错误响应
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 数据
     * @return Response
     */
    protected function error($message = 'error', $code = 400, $data = null)
    {
        $result = [
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ];

        return json($result);
    }

    /**
     * 分页响应
     * @param array $list 列表数据
     * @param int $total 总数
     * @param int $page 当前页
     * @param int $limit 每页数量
     * @param string $message 消息
     * @return Response
     */
    protected function paginate($list, $total, $page, $limit, $message = 'success')
    {
        $data = [
            'list' => $list,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]
        ];

        return $this->success($data, $message);
    }

    /**
     * 验证请求参数
     * @param array $rules 验证规则
     * @param array $data 数据
     * @param array $messages 错误消息
     * @return array|false
     */
    protected function validate($rules, $data = null, $messages = [])
    {
        if (is_null($data)) {
            $data = $this->request->param();
        }

        $validate = new \think\Validate();
        $validate->rule($rules);

        if (!empty($messages)) {
            $validate->message($messages);
        }

        if (!$validate->check($data)) {
            return $validate->getError();
        }

        return false;
    }

    /**
     * 获取当前用户
     * @return array|null
     */
    protected function getUser()
    {
        return $this->request->user ?? null;
    }

    /**
     * 获取当前管理员
     * @return array|null
     */
    protected function getAdmin()
    {
        return $this->request->admin ?? null;
    }

    /**
     * 检查用户权限
     * @param string $permission 权限标识
     * @return bool
     */
    protected function checkPermission($permission)
    {
        $user = $this->getUser();
        if (!$user) {
            return false;
        }

        // 这里可以实现具体的权限检查逻辑
        // 目前简单返回true，实际项目中需要根据用户角色和权限进行检查
        return true;
    }

    /**
     * 记录操作日志
     * @param string $action 操作
     * @param array $data 数据
     */
    protected function logAction($action, $data = [])
    {
        $user = $this->getUser();
        $admin = $this->getAdmin();

        if ($user) {
            \app\service\SecurityService::logUserAction($user['id'], $action, $data);
        } elseif ($admin) {
            \app\service\SecurityService::logUserAction($admin['id'], $action, array_merge($data, ['admin' => true]));
        }
    }

    /**
     * 获取分页参数
     * @return array
     */
    protected function getPageParams()
    {
        $page = max(1, (int)$this->request->param('page', 1));
        $limit = max(1, min(100, (int)$this->request->param('limit', 20))); // 限制最大100条

        return [$page, $limit];
    }

    /**
     * 获取排序参数
     * @param string $default 默认排序字段
     * @param string $defaultOrder 默认排序方向
     * @return array
     */
    protected function getSortParams($default = 'id', $defaultOrder = 'desc')
    {
        $sort = $this->request->param('sort', $default);
        $order = $this->request->param('order', $defaultOrder);

        // 验证排序方向
        if (!in_array(strtolower($order), ['asc', 'desc'])) {
            $order = $defaultOrder;
        }

        return [$sort, $order];
    }

    /**
     * 过滤空值
     * @param array $data
     * @return array
     */
    protected function filterEmpty($data)
    {
        return array_filter($data, function($value) {
            return $value !== '' && $value !== null;
        });
    }
}
