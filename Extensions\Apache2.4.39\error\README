
 Multi Language Custom Error Documents
 -------------------------------------

 The 'error' directory contains HTTP error messages in multiple languages.
 If the preferred language of a client is available it is selected
 automatically via the MultiViews feature. This feature is enabled
 by default via the Options, Language and ErrorDocument directives.

 You may configure the design and markup of the documents by modifying
 the HTML files in the directory 'error/include'.

 Supported Languages:
 
  +-----------------------+------------------------------------------+
  | Language              | Contributed by                           |
  +-----------------------+------------------------------------------+
  | Brazilian (pt-br)     | <PERSON>                            |
  | Chinese (zh-cn/zh-tw) | CodeingBoy & popcorner                   |
  | Czech (cs)            | <PERSON>                            |
  | Dutch (nl)            | <PERSON>                         |
  | English (en)          | <PERSON>                          |
  | French (fr)           | <PERSON><PERSON>                          |
  | German (de)           | <PERSON>                          |
  | <PERSON> (it)          | <PERSON>                               |
  | Japanese (ja)         | TAKAHASHI Makoto                         |
  | Korean (ko)           | Jaeho Shin                               |
  | Norwegian Bokmål (nb) | Tom Fredrik Klaussen                     |
  | Polish (pl)           | Tomasz Kepczynski                        |
  | Romanian (ro)         | Andrei Besleaga                          |
  | Russian (ru)          | Alexander Gaganashvili                   |
  | Serbian (sr)          | Nikola Smolenski                         |
  | Spanish (es)          | Karla Quintero                           |
  | Swedish (sv)          | Thomas Sjögren                           |
  | Turkish (tr)          | Emre Sokullu & Nilgün Belma Bugüner      |
  | Irish (ga)            | Noirin Shirley                           |
  +-----------------------+------------------------------------------+
  (Please see http://httpd.apache.org/docs-project/ if you would
   like to contribute the pages in an additional language.)

