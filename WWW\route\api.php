<?php

use think\facade\Route;

// API路由配置

// 公开接口（无需认证）
Route::group('api', function () {
    // 用户认证相关
    Route::post('auth/register', 'api.Auth/register');
    Route::post('auth/login', 'api.Auth/login');
    Route::post('auth/refresh', 'api.Auth/refresh');
    
    // 获取积分充值套餐
    Route::get('score/packages', 'api.Score/packages');
    
    // 获取系统配置
    Route::get('system/config', 'api.System/config');
    
    // 验证码
    Route::get('captcha', 'api.System/captcha');
    
})->middleware([
    'app\middleware\IpBlacklistMiddleware',
    'app\middleware\RiskControlMiddleware'
]);

// 用户认证接口
Route::group('api', function () {
    // 用户信息
    Route::get('auth/profile', 'api.Auth/profile');
    Route::put('auth/profile', 'api.Auth/updateProfile');
    Route::post('auth/change-password', 'api.Auth/changePassword');
    Route::post('auth/logout', 'api.Auth/logout');
    
    // 任务管理
    Route::get('tasks', 'api.Task/index');
    Route::post('tasks', 'api.Task/create');
    Route::get('tasks/:id', 'api.Task/show');
    Route::put('tasks/:id/pause', 'api.Task/pause');
    Route::put('tasks/:id/resume', 'api.Task/resume');
    Route::delete('tasks/:id', 'api.Task/delete');
    Route::get('tasks/statistics', 'api.Task/statistics');
    
    // 积分管理
    Route::get('score/balance', 'api.Score/balance');
    Route::get('score/logs', 'api.Score/logs');
    Route::get('score/statistics', 'api.Score/statistics');
    Route::post('score/recharge', 'api.Score/recharge');
    
    // 用户中心
    Route::get('user/dashboard', 'api.User/dashboard');
    Route::get('user/notifications', 'api.User/notifications');
    Route::put('user/notifications/:id/read', 'api.User/markNotificationRead');
    
})->middleware([
    'app\middleware\IpBlacklistMiddleware',
    'app\middleware\RiskControlMiddleware',
    'app\middleware\AuthMiddleware',
    'app\middleware\RateLimitMiddleware'
]);

// 管理员认证接口
Route::group('api/admin', function () {
    // 管理员登录
    Route::post('auth/login', 'admin.Auth/login');
    Route::post('auth/refresh', 'admin.Auth/refresh');
    
})->middleware([
    'app\middleware\IpBlacklistMiddleware',
    'app\middleware\RiskControlMiddleware'
]);

// 管理员功能接口
Route::group('api/admin', function () {
    // 管理员信息
    Route::get('auth/profile', 'admin.Auth/profile');
    Route::post('auth/change-password', 'admin.Auth/changePassword');
    Route::post('auth/logout', 'admin.Auth/logout');
    
    // 仪表盘
    Route::get('dashboard', 'admin.Admin/dashboard');
    
    // 用户管理
    Route::get('users', 'admin.Admin/users');
    Route::get('users/:id', 'admin.Admin/userDetail');
    Route::put('users/:id/status', 'admin.Admin/updateUserStatus');
    Route::post('users/:id/recharge', 'admin.Admin/manualRecharge');
    Route::get('users/:id/tasks', 'admin.Admin/userTasks');
    Route::get('users/:id/scores', 'admin.Admin/userScores');
    
    // 任务管理
    Route::get('tasks', 'admin.Admin/tasks');
    Route::get('tasks/:id', 'admin.Admin/taskDetail');
    Route::put('tasks/:id/review', 'admin.Admin/reviewTask');
    Route::put('tasks/:id/status', 'admin.Admin/updateTaskStatus');
    Route::get('tasks/statistics', 'admin.Admin/taskStatistics');
    
    // 财务管理
    Route::get('financial/overview', 'admin.Admin/financial');
    Route::get('financial/recharge', 'admin.Admin/rechargeStats');
    Route::get('financial/consumption', 'admin.Admin/consumptionStats');
    Route::get('financial/trend', 'admin.Admin/financialTrend');
    
    // 系统管理
    Route::get('system/info', 'admin.System/info');
    Route::get('system/logs', 'admin.System/logs');
    Route::post('system/cache/clear', 'admin.System/clearCache');
    Route::get('system/database/stats', 'admin.System/databaseStats');
    
    // 安全管理
    Route::get('security/logs', 'admin.Security/logs');
    Route::get('security/ip-blacklist', 'admin.Security/ipBlacklist');
    Route::post('security/ip-blacklist', 'admin.Security/addIpBlacklist');
    Route::delete('security/ip-blacklist/:id', 'admin.Security/removeIpBlacklist');
    Route::get('security/risk-stats', 'admin.Security/riskStats');
    
})->middleware([
    'app\middleware\IpBlacklistMiddleware',
    'app\middleware\RiskControlMiddleware',
    'app\middleware\AdminAuthMiddleware',
    'app\middleware\RateLimitMiddleware'
]);

// 超级管理员接口
Route::group('api/admin', function () {
    // 管理员管理
    Route::get('admins', 'admin.AdminManage/index');
    Route::post('admins', 'admin.AdminManage/create');
    Route::get('admins/:id', 'admin.AdminManage/show');
    Route::put('admins/:id', 'admin.AdminManage/update');
    Route::delete('admins/:id', 'admin.AdminManage/delete');
    Route::put('admins/:id/status', 'admin.AdminManage/updateStatus');
    
    // 系统配置
    Route::get('config', 'admin.Config/index');
    Route::put('config', 'admin.Config/update');
    Route::post('config/backup', 'admin.Config/backup');
    Route::post('config/restore', 'admin.Config/restore');
    
})->middleware([
    'app\middleware\IpBlacklistMiddleware',
    'app\middleware\RiskControlMiddleware',
    'app\middleware\AdminAuthMiddleware:super_admin',
    'app\middleware\RateLimitMiddleware'
]);

// WebHook接口（第三方回调）
Route::group('webhook', function () {
    // 支付回调
    Route::post('payment/alipay', 'webhook.Payment/alipayNotify');
    Route::post('payment/wechat', 'webhook.Payment/wechatNotify');
    
    // 任务执行回调
    Route::post('task/callback', 'webhook.Task/callback');
    
})->middleware([
    'app\middleware\IpBlacklistMiddleware'
]);

// 开放API接口（需要API密钥）
Route::group('open-api/v1', function () {
    // 任务管理
    Route::post('tasks', 'openapi.Task/create');
    Route::get('tasks/:id', 'openapi.Task/show');
    Route::get('tasks/:id/status', 'openapi.Task/status');
    
    // 积分查询
    Route::get('score/balance', 'openapi.Score/balance');
    Route::get('score/logs', 'openapi.Score/logs');
    
})->middleware([
    'app\middleware\IpBlacklistMiddleware',
    'app\middleware\ApiKeyMiddleware',
    'app\middleware\RateLimitMiddleware'
]);

// 健康检查接口
Route::get('health', function () {
    return json([
        'status' => 'ok',
        'timestamp' => time(),
        'version' => '1.0.0'
    ]);
});

// 404处理
Route::miss(function () {
    return json([
        'code' => 404,
        'message' => 'API接口不存在',
        'data' => null,
        'timestamp' => time()
    ], 404);
});
