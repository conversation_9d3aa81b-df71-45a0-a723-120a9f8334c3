# SEO积分系统 - 前端实现参考

## 🎨 前端技术栈

### 推荐技术选型
```yaml
基础框架: Vue.js 3.x + TypeScript
UI组件库: Element Plus / Ant Design Vue
状态管理: Pinia
路由管理: Vue Router 4.x
HTTP客户端: Axios
构建工具: Vite
CSS预处理: Sass/Less
图表库: ECharts
```

### 项目结构
```
src/
├── api/                 # API接口
├── assets/             # 静态资源
├── components/         # 公共组件
├── composables/        # 组合式函数
├── layouts/            # 布局组件
├── pages/              # 页面组件
├── router/             # 路由配置
├── stores/             # 状态管理
├── styles/             # 样式文件
├── types/              # TypeScript类型
└── utils/              # 工具函数
```

## 🔧 核心功能实现

### 1. API封装
```typescript
// api/request.ts
import axios, { AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, message, data } = response.data
    
    if (code === 200) {
      return data
    } else {
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message))
    }
  },
  (error) => {
    if (error.response?.status === 401) {
      const userStore = useUserStore()
      userStore.logout()
      ElMessage.error('登录已过期，请重新登录')
    } else {
      ElMessage.error(error.message || '网络错误')
    }
    return Promise.reject(error)
  }
)

export default request
```

### 2. 用户状态管理
```typescript
// stores/user.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, getUserInfo } from '@/api/user'
import type { UserInfo, LoginParams } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  
  const isLoggedIn = computed(() => !!token.value)
  
  // 登录
  const loginAction = async (params: LoginParams) => {
    try {
      const result = await login(params)
      token.value = result.token
      userInfo.value = result.user_info
      
      localStorage.setItem('token', result.token)
      return result
    } catch (error) {
      throw error
    }
  }
  
  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const result = await getUserInfo()
      userInfo.value = result
      return result
    } catch (error) {
      throw error
    }
  }
  
  // 登出
  const logout = () => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
  }
  
  return {
    token,
    userInfo,
    isLoggedIn,
    loginAction,
    getUserInfoAction,
    logout
  }
})
```

### 3. 任务管理组件
```vue
<!-- pages/TaskManagement.vue -->
<template>
  <div class="task-management">
    <div class="header">
      <h2>任务管理</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        创建任务
      </el-button>
    </div>
    
    <!-- 筛选条件 -->
    <div class="filters">
      <el-form :model="filters" inline>
        <el-form-item label="任务类型">
          <el-select v-model="filters.type" placeholder="请选择">
            <el-option label="关键词排名" :value="1" />
            <el-option label="下拉词" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="请选择">
            <el-option label="待审核" :value="1" />
            <el-option label="执行中" :value="2" />
            <el-option label="已完成" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadTasks">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 任务列表 -->
    <el-table :data="tasks" v-loading="loading">
      <el-table-column prop="id" label="任务ID" width="80" />
      <el-table-column prop="keyword" label="关键词" />
      <el-table-column prop="url" label="目标URL" show-overflow-tooltip />
      <el-table-column prop="search_engine" label="搜索引擎" width="100" />
      <el-table-column prop="online_days" label="上线天数" width="100" />
      <el-table-column prop="daily_amount" label="每日数量" width="100" />
      <el-table-column prop="pre_deduct_score" label="预扣积分" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button size="small" @click="viewTask(row)">查看</el-button>
          <el-button 
            v-if="row.status === 2" 
            size="small" 
            type="warning" 
            @click="pauseTask(row.id)"
          >
            暂停
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.limit"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="loadTasks"
      @current-change="loadTasks"
    />
    
    <!-- 创建任务对话框 -->
    <CreateTaskDialog 
      v-model="showCreateDialog" 
      @success="loadTasks" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getTasks, pauseTask as pauseTaskApi } from '@/api/task'
import { ElMessage, ElMessageBox } from 'element-plus'
import CreateTaskDialog from '@/components/CreateTaskDialog.vue'
import type { Task, TaskFilters } from '@/types/task'

const loading = ref(false)
const tasks = ref<Task[]>([])
const showCreateDialog = ref(false)

const filters = reactive<TaskFilters>({
  type: '',
  status: '',
  keyword: ''
})

const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 加载任务列表
const loadTasks = async () => {
  loading.value = true
  try {
    const result = await getTasks({
      ...filters,
      page: pagination.page,
      limit: pagination.limit
    })
    tasks.value = result.data
    pagination.total = result.total
  } catch (error) {
    console.error('加载任务失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    type: '',
    status: '',
    keyword: ''
  })
  loadTasks()
}

// 查看任务详情
const viewTask = (task: Task) => {
  // 跳转到任务详情页
  console.log('查看任务:', task)
}

// 暂停任务
const pauseTask = async (taskId: number) => {
  try {
    await ElMessageBox.confirm('确定要暂停这个任务吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await pauseTaskApi(taskId)
    ElMessage.success('任务已暂停')
    loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('暂停任务失败:', error)
    }
  }
}

// 获取状态类型
const getStatusType = (status: number) => {
  const types = {
    1: 'info',
    2: 'success',
    3: 'success',
    4: 'warning',
    5: 'danger'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: number) => {
  const texts = {
    1: '待审核',
    2: '执行中',
    3: '已完成',
    4: '已暂停',
    5: '已取消'
  }
  return texts[status] || '未知'
}

onMounted(() => {
  loadTasks()
})
</script>

<style scoped lang="scss">
.task-management {
  padding: 20px;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
    }
  }
  
  .filters {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
  }
  
  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
```

### 4. 创建任务对话框
```vue
<!-- components/CreateTaskDialog.vue -->
<template>
  <el-dialog
    v-model="visible"
    title="创建任务"
    width="600px"
    @close="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="任务类型" prop="type">
        <el-radio-group v-model="form.type">
          <el-radio :label="1">关键词排名</el-radio>
          <el-radio :label="2">下拉词</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="关键词" prop="keyword">
        <el-input v-model="form.keyword" placeholder="请输入关键词" />
      </el-form-item>
      
      <el-form-item 
        v-if="form.type === 1" 
        label="目标URL" 
        prop="url"
      >
        <el-input v-model="form.url" placeholder="请输入目标URL" />
      </el-form-item>
      
      <el-form-item label="搜索引擎" prop="search_engine">
        <el-select v-model="form.search_engine" placeholder="请选择">
          <el-option label="百度" value="baidu" />
          <el-option label="360" value="360" />
          <el-option label="搜狗" value="sogou" />
          <el-option label="神马" value="sm" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="上线天数" prop="online_days">
        <el-input-number 
          v-model="form.online_days" 
          :min="1" 
          :max="30" 
          controls-position="right"
        />
      </el-form-item>
      
      <el-form-item 
        :label="form.type === 1 ? '每日点击' : '每日搜索'" 
        prop="daily_amount"
      >
        <el-input-number 
          v-model="form.daily_amount" 
          :min="10" 
          :max="500" 
          controls-position="right"
        />
      </el-form-item>
      
      <el-form-item label="预计积分">
        <span class="score-preview">{{ calculatedScore }} 积分</span>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitForm" :loading="submitting">
        创建任务
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { createTask } from '@/api/task'
import { useUserStore } from '@/stores/user'
import type { CreateTaskForm } from '@/types/task'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

const userStore = useUserStore()
const formRef = ref<FormInstance>()
const submitting = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const form = reactive<CreateTaskForm>({
  type: 1,
  keyword: '',
  url: '',
  search_engine: '',
  online_days: 15,
  daily_amount: 50
})

const rules: FormRules = {
  type: [{ required: true, message: '请选择任务类型' }],
  keyword: [{ required: true, message: '请输入关键词' }],
  url: [
    { 
      required: true, 
      message: '请输入目标URL',
      trigger: 'blur'
    },
    {
      pattern: /^https?:\/\/.+/,
      message: '请输入有效的URL',
      trigger: 'blur'
    }
  ],
  search_engine: [{ required: true, message: '请选择搜索引擎' }],
  online_days: [{ required: true, message: '请输入上线天数' }],
  daily_amount: [{ required: true, message: '请输入每日数量' }]
}

// 计算预计积分
const calculatedScore = computed(() => {
  const userGroup = userStore.userInfo?.group_info
  const scoreCost = userGroup?.score_cost || 1.0
  const multiplier = form.type === 2 ? 1.5 : 1.0 // 下拉词任务费用更高
  
  return Math.ceil(form.online_days * form.daily_amount * scoreCost * multiplier)
})

// 监听任务类型变化，重置URL验证规则
watch(() => form.type, (newType) => {
  if (newType === 2) {
    // 下拉词任务不需要URL
    rules.url = []
  } else {
    rules.url = [
      { required: true, message: '请输入目标URL', trigger: 'blur' },
      { pattern: /^https?:\/\/.+/, message: '请输入有效的URL', trigger: 'blur' }
    ]
  }
})

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    await createTask(form)
    
    ElMessage.success('任务创建成功')
    visible.value = false
    emit('success')
  } catch (error) {
    console.error('创建任务失败:', error)
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    type: 1,
    keyword: '',
    url: '',
    search_engine: '',
    online_days: 15,
    daily_amount: 50
  })
}
</script>

<style scoped lang="scss">
.score-preview {
  color: #409eff;
  font-weight: bold;
  font-size: 16px;
}
</style>
```

### 5. 工具函数
```typescript
// utils/format.ts
/**
 * 格式化数字
 */
export const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

/**
 * 格式化积分
 */
export const formatScore = (score: number): string => {
  return `${formatNumber(score)} 积分`
}

/**
 * 格式化日期
 */
export const formatDate = (timestamp: number): string => {
  return new Date(timestamp * 1000).toLocaleString()
}

/**
 * 格式化相对时间
 */
export const formatRelativeTime = (timestamp: number): string => {
  const now = Date.now()
  const diff = now - timestamp * 1000
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else {
    return `${Math.floor(diff / day)}天前`
  }
}

/**
 * 防抖函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}
```

---

**前端开发要点**:
1. **组件化设计**: 合理拆分组件，提高代码复用性
2. **状态管理**: 使用Pinia管理全局状态，保持数据一致性
3. **类型安全**: 使用TypeScript提供类型检查和智能提示
4. **用户体验**: 加载状态、错误处理、表单验证等细节优化
5. **性能优化**: 合理使用防抖节流、虚拟滚动等技术
6. **响应式设计**: 适配不同屏幕尺寸，提供良好的移动端体验
