<?php

namespace app\middleware;

use think\Request;
use think\Response;
use app\service\SecurityService;

/**
 * 频率限制中间件
 */
class RateLimitMiddleware
{
    /**
     * 处理请求
     */
    public function handle(Request $request, \Closure $next, $maxRequests = 60, $timeWindow = 60)
    {
        // 获取限制键
        $key = $this->getRateLimitKey($request);
        
        // 检查频率限制
        if (!SecurityService::checkRateLimit($key, $maxRequests, $timeWindow)) {
            return $this->rateLimitResponse($maxRequests, $timeWindow);
        }
        
        $response = $next($request);
        
        // 添加频率限制头信息
        $this->addRateLimitHeaders($response, $key, $maxRequests, $timeWindow);
        
        return $response;
    }
    
    /**
     * 获取频率限制键
     */
    private function getRateLimitKey(Request $request)
    {
        $ip = $request->ip();
        $route = $request->pathinfo();
        
        // 如果用户已登录，使用用户ID
        if (isset($request->user) && $request->user) {
            return "user:{$request->user['id']}:{$route}";
        }
        
        // 如果管理员已登录，使用管理员ID
        if (isset($request->admin) && $request->admin) {
            return "admin:{$request->admin['id']}:{$route}";
        }
        
        // 否则使用IP地址
        return "ip:{$ip}:{$route}";
    }
    
    /**
     * 添加频率限制头信息
     */
    private function addRateLimitHeaders(Response $response, $key, $maxRequests, $timeWindow)
    {
        // 获取当前请求数
        $cacheKey = "rate_limit:{$key}";
        $requests = cache($cacheKey, []);
        
        $now = time();
        $windowStart = $now - $timeWindow;
        
        // 清理过期的请求记录
        $requests = array_filter($requests, function($timestamp) use ($windowStart) {
            return $timestamp > $windowStart;
        });
        
        $currentRequests = count($requests);
        $remaining = max(0, $maxRequests - $currentRequests);
        $resetTime = $now + $timeWindow;
        
        $response->header([
            'X-RateLimit-Limit' => $maxRequests,
            'X-RateLimit-Remaining' => $remaining,
            'X-RateLimit-Reset' => $resetTime,
            'X-RateLimit-Window' => $timeWindow
        ]);
    }
    
    /**
     * 返回频率限制响应
     */
    private function rateLimitResponse($maxRequests, $timeWindow)
    {
        return json([
            'code' => 429,
            'message' => "请求过于频繁，每{$timeWindow}秒最多{$maxRequests}次请求",
            'data' => [
                'max_requests' => $maxRequests,
                'time_window' => $timeWindow,
                'retry_after' => $timeWindow
            ]
        ], 429)->header([
            'Retry-After' => $timeWindow
        ]);
    }
}
