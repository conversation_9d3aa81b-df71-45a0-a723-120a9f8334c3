<?php

namespace app\middleware;

use think\Request;
use think\Response;
use think\exception\ValidateException;
use app\service\JwtService;
use app\service\UserService;
use app\service\SecurityService;

/**
 * 用户认证中间件
 */
class AuthMiddleware
{
    /**
     * 处理请求
     */
    public function handle(Request $request, \Closure $next)
    {
        // 获取Token
        $token = $this->getTokenFromRequest($request);
        
        if (empty($token)) {
            return $this->unauthorizedResponse('缺少认证Token');
        }
        
        try {
            // 验证Token
            $payload = JwtService::validateToken($token);
            $userData = $payload['data'] ?? [];
            
            if (empty($userData['user_id'])) {
                return $this->unauthorizedResponse('Token数据无效');
            }
            
            // 获取用户信息
            $user = UserService::getUserById($userData['user_id']);
            
            if (!$user) {
                return $this->unauthorizedResponse('用户不存在');
            }
            
            if ($user['status'] != 1) {
                return $this->unauthorizedResponse('账户已被禁用');
            }
            
            // 检查IP是否被封禁
            if (SecurityService::isIpBlocked($request->ip())) {
                return $this->forbiddenResponse('IP已被封禁');
            }
            
            // 将用户信息存储到请求中
            $request->user = $user;
            $request->token = $token;
            $request->tokenData = $userData;
            
            // 记录用户活动
            $this->logUserActivity($user['id'], $request);
            
        } catch (ValidateException $e) {
            return $this->unauthorizedResponse($e->getMessage());
        } catch (\Exception $e) {
            return $this->unauthorizedResponse('Token验证失败');
        }
        
        return $next($request);
    }
    
    /**
     * 从请求中获取Token
     */
    private function getTokenFromRequest(Request $request)
    {
        // 优先从Header中获取
        $authorization = $request->header('Authorization');
        if ($authorization && strpos($authorization, 'Bearer ') === 0) {
            return substr($authorization, 7);
        }
        
        // 从GET参数获取
        $token = $request->get('token');
        if ($token) {
            return $token;
        }
        
        // 从POST参数获取
        $token = $request->post('token');
        if ($token) {
            return $token;
        }
        
        return null;
    }
    
    /**
     * 记录用户活动
     */
    private function logUserActivity($userId, Request $request)
    {
        // 只记录重要的操作，避免日志过多
        $importantActions = [
            'POST', 'PUT', 'DELETE', 'PATCH'
        ];
        
        if (in_array($request->method(), $importantActions)) {
            SecurityService::logUserAction($userId, 'api_access', [
                'method' => $request->method(),
                'url' => $request->url(),
                'ip' => $request->ip()
            ]);
        }
    }
    
    /**
     * 返回未授权响应
     */
    private function unauthorizedResponse($message = '未授权访问')
    {
        return json([
            'code' => 401,
            'message' => $message,
            'data' => null
        ], 401);
    }
    
    /**
     * 返回禁止访问响应
     */
    private function forbiddenResponse($message = '禁止访问')
    {
        return json([
            'code' => 403,
            'message' => $message,
            'data' => null
        ], 403);
    }
}
