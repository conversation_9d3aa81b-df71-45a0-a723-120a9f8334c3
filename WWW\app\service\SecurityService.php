<?php

namespace app\service;

use think\facade\Db;
use think\facade\Cache;
use think\facade\Request;

/**
 * 安全服务类
 */
class SecurityService
{
    /**
     * 记录用户操作日志
     */
    public static function logUserAction($userId, $operation, $data = [])
    {
        $logData = [
            'user_id' => $userId,
            'type' => 'user_action',
            'operation' => $operation,
            'risk_level' => self::calculateRiskLevel($operation, $data),
            'ip' => Request::ip(),
            'user_agent' => Request::header('User-Agent'),
            'data' => json_encode($data),
            'create_time' => time()
        ];
        
        return Db::name('security_logs')->insert($logData);
    }
    
    /**
     * 记录登录尝试
     */
    public static function logLoginAttempt($userId, $ip, $success = true)
    {
        // 记录到用户登录日志表
        $loginLogData = [
            'user_id' => $userId,
            'login_ip' => $ip,
            'user_agent' => Request::header('User-Agent'),
            'device_fingerprint' => self::generateDeviceFingerprint(),
            'login_time' => time(),
            'status' => $success ? 1 : 2
        ];
        
        Db::name('user_login_logs')->insert($loginLogData);
        
        // 记录到安全日志
        self::logUserAction($userId, $success ? 'login_success' : 'login_failed', [
            'ip' => $ip,
            'success' => $success
        ]);
        
        // 如果登录失败，检查是否需要限制
        if (!$success) {
            self::checkLoginFailureLimit($ip, $userId);
        }
    }
    
    /**
     * 检查登录失败限制
     */
    public static function checkLoginFailureLimit($ip, $userId = null)
    {
        $cacheKey = "login_failures:{$ip}";
        $failures = Cache::get($cacheKey, 0);
        
        $failures++;
        Cache::set($cacheKey, $failures, 3600); // 1小时过期
        
        // 如果失败次数超过5次，记录高风险日志
        if ($failures >= 5) {
            self::logSecurityEvent('login_brute_force', [
                'ip' => $ip,
                'user_id' => $userId,
                'failure_count' => $failures
            ], 3); // 高风险
            
            // 可以在这里添加更多安全措施，如发送告警邮件
        }
        
        return $failures;
    }
    
    /**
     * 检查IP是否被限制
     */
    public static function isIpBlocked($ip)
    {
        $cacheKey = "blocked_ip:{$ip}";
        return Cache::has($cacheKey);
    }
    
    /**
     * 封禁IP
     */
    public static function blockIp($ip, $duration = 3600, $reason = '')
    {
        $cacheKey = "blocked_ip:{$ip}";
        Cache::set($cacheKey, [
            'blocked_time' => time(),
            'duration' => $duration,
            'reason' => $reason
        ], $duration);
        
        self::logSecurityEvent('ip_blocked', [
            'ip' => $ip,
            'duration' => $duration,
            'reason' => $reason
        ], 2);
    }
    
    /**
     * 检查频率限制
     */
    public static function checkRateLimit($key, $maxRequests = 60, $timeWindow = 60)
    {
        $cacheKey = "rate_limit:{$key}";
        $requests = Cache::get($cacheKey, []);
        
        $now = time();
        $windowStart = $now - $timeWindow;
        
        // 清理过期的请求记录
        $requests = array_filter($requests, function($timestamp) use ($windowStart) {
            return $timestamp > $windowStart;
        });
        
        // 检查是否超过限制
        if (count($requests) >= $maxRequests) {
            self::logSecurityEvent('rate_limit_exceeded', [
                'key' => $key,
                'requests' => count($requests),
                'max_requests' => $maxRequests,
                'time_window' => $timeWindow
            ], 2);
            
            return false;
        }
        
        // 记录当前请求
        $requests[] = $now;
        Cache::set($cacheKey, $requests, $timeWindow);
        
        return true;
    }
    
    /**
     * 检测异常行为
     */
    public static function detectAnomalousActivity($userId, $action, $data = [])
    {
        $riskScore = 0;
        $reasons = [];
        
        // 检查短时间内大量操作
        $recentActions = self::getRecentUserActions($userId, 300); // 5分钟内
        if (count($recentActions) > 20) {
            $riskScore += 30;
            $reasons[] = '短时间内操作频繁';
        }
        
        // 检查异常IP
        $userIps = self::getUserRecentIps($userId, 86400); // 24小时内的IP
        $currentIp = Request::ip();
        
        if (count($userIps) > 5 && !in_array($currentIp, $userIps)) {
            $riskScore += 20;
            $reasons[] = '使用异常IP地址';
        }
        
        // 检查设备指纹
        $deviceFingerprint = self::generateDeviceFingerprint();
        $userDevices = self::getUserRecentDevices($userId, 86400);
        
        if (count($userDevices) > 3 && !in_array($deviceFingerprint, $userDevices)) {
            $riskScore += 15;
            $reasons[] = '使用异常设备';
        }
        
        // 根据风险分数采取措施
        if ($riskScore >= 50) {
            self::logSecurityEvent('anomalous_activity_detected', [
                'user_id' => $userId,
                'action' => $action,
                'risk_score' => $riskScore,
                'reasons' => $reasons,
                'data' => $data
            ], 3);
            
            // 可以在这里添加更多安全措施
            return [
                'risk_level' => 'high',
                'risk_score' => $riskScore,
                'reasons' => $reasons,
                'action_required' => true
            ];
        } elseif ($riskScore >= 30) {
            return [
                'risk_level' => 'medium',
                'risk_score' => $riskScore,
                'reasons' => $reasons,
                'action_required' => false
            ];
        }
        
        return [
            'risk_level' => 'low',
            'risk_score' => $riskScore,
            'reasons' => [],
            'action_required' => false
        ];
    }
    
    /**
     * 记录安全事件
     */
    public static function logSecurityEvent($type, $data = [], $riskLevel = 1)
    {
        $logData = [
            'user_id' => $data['user_id'] ?? null,
            'type' => $type,
            'operation' => null,
            'risk_level' => $riskLevel,
            'ip' => Request::ip(),
            'user_agent' => Request::header('User-Agent'),
            'data' => json_encode($data),
            'create_time' => time()
        ];
        
        return Db::name('security_logs')->insert($logData);
    }
    
    /**
     * 生成设备指纹
     */
    public static function generateDeviceFingerprint()
    {
        $userAgent = Request::header('User-Agent', '');
        $acceptLanguage = Request::header('Accept-Language', '');
        $acceptEncoding = Request::header('Accept-Encoding', '');
        
        $fingerprint = $userAgent . '|' . $acceptLanguage . '|' . $acceptEncoding;
        
        return md5($fingerprint);
    }
    
    /**
     * 获取用户最近的操作记录
     */
    private static function getRecentUserActions($userId, $timeWindow)
    {
        $startTime = time() - $timeWindow;
        
        return Db::name('security_logs')
            ->where('user_id', $userId)
            ->where('create_time', '>=', $startTime)
            ->select();
    }
    
    /**
     * 获取用户最近使用的IP
     */
    private static function getUserRecentIps($userId, $timeWindow)
    {
        $startTime = time() - $timeWindow;
        
        $ips = Db::name('user_login_logs')
            ->where('user_id', $userId)
            ->where('login_time', '>=', $startTime)
            ->where('status', 1)
            ->column('login_ip');
        
        return array_unique($ips);
    }
    
    /**
     * 获取用户最近使用的设备
     */
    private static function getUserRecentDevices($userId, $timeWindow)
    {
        $startTime = time() - $timeWindow;
        
        $devices = Db::name('user_login_logs')
            ->where('user_id', $userId)
            ->where('login_time', '>=', $startTime)
            ->where('status', 1)
            ->column('device_fingerprint');
        
        return array_unique(array_filter($devices));
    }
    
    /**
     * 计算风险等级
     */
    private static function calculateRiskLevel($operation, $data)
    {
        $highRiskOperations = [
            'password_change', 'score_recharge', 'task_create', 'login_failed'
        ];
        
        $mediumRiskOperations = [
            'login_success', 'profile_update', 'task_pause'
        ];
        
        if (in_array($operation, $highRiskOperations)) {
            return 2;
        } elseif (in_array($operation, $mediumRiskOperations)) {
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 清理过期的安全日志
     */
    public static function cleanExpiredLogs($days = 30)
    {
        $expireTime = time() - ($days * 24 * 3600);
        
        return Db::name('security_logs')
            ->where('create_time', '<', $expireTime)
            ->delete();
    }
}
