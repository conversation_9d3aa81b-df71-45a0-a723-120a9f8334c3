TYPE=VIEW
query=select `t`.`id` AS `task_id`,`t`.`video_id` AS `video_id`,`t`.`video_title` AS `video_title`,`a`.`account_name` AS `account_name`,`a`.`id` AS `account_id`,`t`.`task_status` AS `task_status`,`t`.`collection_limit` AS `collection_limit`,`t`.`collection_interval_minutes` AS `collection_interval_minutes`,`t`.`high_intent_keywords` AS `high_intent_keywords`,`t`.`last_collection_time` AS `last_collection_time`,`t`.`next_collection_time` AS `next_collection_time`,coalesce(`s`.`total_comments`,0) AS `total_comments`,coalesce(`s`.`primary_comments`,0) AS `primary_comments`,coalesce(`s`.`reply_comments`,0) AS `reply_comments`,coalesce(`s`.`high_intent_comments`,0) AS `high_intent_comments`,coalesce(`s`.`messages_sent_success`,0) AS `messages_sent_success`,coalesce(`s`.`messages_sent_failed`,0) AS `messages_sent_failed`,`t`.`created_time` AS `created_time`,`t`.`remarks` AS `remarks` from ((`douyin_comments`.`dy_monitor_tasks` `t` left join `douyin_comments`.`dy_accounts` `a` on((`t`.`account_id` = `a`.`id`))) left join `douyin_comments`.`dy_task_statistics` `s` on((`t`.`id` = `s`.`task_id`)))
md5=1ce3614c52705857322ef35353e1a143
updatable=0
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=2025-06-25 12:11:06
create-version=1
source=SELECT
\n    t.id as task_id,
\n    t.video_id,
\n    t.video_title,
\n    a.account_name,
\n    a.id as account_id,
\n    t.task_status,
\n    t.collection_limit,
\n    t.collection_interval_minutes,
\n    t.high_intent_keywords,
\n    t.last_collection_time,
\n    t.next_collection_time,
\n    COALESCE(s.total_comments, 0) as total_comments,
\n    COALESCE(s.primary_comments, 0) as primary_comments,
\n    COALESCE(s.reply_comments, 0) as reply_comments,
\n    COALESCE(s.high_intent_comments, 0) as high_intent_comments,
\n    COALESCE(s.messages_sent_success, 0) as messages_sent_success,
\n    COALESCE(s.messages_sent_failed, 0) as messages_sent_failed,
\n    t.created_time,
\n    t.remarks
\nFROM dy_monitor_tasks t
\nLEFT JOIN dy_accounts a ON t.account_id = a.id
\nLEFT JOIN dy_task_statistics s ON t.id = s.task_id
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_general_ci
view_body_utf8=select `t`.`id` AS `task_id`,`t`.`video_id` AS `video_id`,`t`.`video_title` AS `video_title`,`a`.`account_name` AS `account_name`,`a`.`id` AS `account_id`,`t`.`task_status` AS `task_status`,`t`.`collection_limit` AS `collection_limit`,`t`.`collection_interval_minutes` AS `collection_interval_minutes`,`t`.`high_intent_keywords` AS `high_intent_keywords`,`t`.`last_collection_time` AS `last_collection_time`,`t`.`next_collection_time` AS `next_collection_time`,coalesce(`s`.`total_comments`,0) AS `total_comments`,coalesce(`s`.`primary_comments`,0) AS `primary_comments`,coalesce(`s`.`reply_comments`,0) AS `reply_comments`,coalesce(`s`.`high_intent_comments`,0) AS `high_intent_comments`,coalesce(`s`.`messages_sent_success`,0) AS `messages_sent_success`,coalesce(`s`.`messages_sent_failed`,0) AS `messages_sent_failed`,`t`.`created_time` AS `created_time`,`t`.`remarks` AS `remarks` from ((`douyin_comments`.`dy_monitor_tasks` `t` left join `douyin_comments`.`dy_accounts` `a` on((`t`.`account_id` = `a`.`id`))) left join `douyin_comments`.`dy_task_statistics` `s` on((`t`.`id` = `s`.`task_id`)))
