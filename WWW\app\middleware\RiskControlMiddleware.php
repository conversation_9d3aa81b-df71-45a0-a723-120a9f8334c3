<?php

namespace app\middleware;

use think\Response;
use app\service\SecurityService;

/**
 * 风控中间件
 */
class RiskControlMiddleware
{
    // 风险等级
    const RISK_LOW = 1;
    const RISK_MEDIUM = 2;
    const RISK_HIGH = 3;
    const RISK_CRITICAL = 4;
    
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        $ip = $request->ip();
        $userAgent = $request->header('User-Agent');
        $url = $request->url();
        $method = $request->method();
        
        // 风险评估
        $riskLevel = $this->assessRisk($request);
        
        // 根据风险等级采取不同措施
        switch ($riskLevel) {
            case self::RISK_CRITICAL:
                // 严重风险：直接拒绝
                $this->logRiskEvent($ip, 'critical_risk_blocked', [
                    'url' => $url,
                    'method' => $method,
                    'user_agent' => $userAgent
                ]);
                
                return json([
                    'code' => 403,
                    'message' => '请求被拒绝',
                    'data' => null,
                    'timestamp' => time()
                ], 403);
                
            case self::RISK_HIGH:
                // 高风险：需要验证码
                if (!$this->verifyCaptcha($request)) {
                    $this->logRiskEvent($ip, 'high_risk_captcha_required', [
                        'url' => $url,
                        'method' => $method
                    ]);
                    
                    return json([
                        'code' => 428,
                        'message' => '需要验证码验证',
                        'data' => ['require_captcha' => true],
                        'timestamp' => time()
                    ], 428);
                }
                break;
                
            case self::RISK_MEDIUM:
                // 中等风险：增加监控
                $this->logRiskEvent($ip, 'medium_risk_detected', [
                    'url' => $url,
                    'method' => $method
                ]);
                break;
        }
        
        // 记录请求信息
        $this->recordRequest($request, $riskLevel);
        
        return $next($request);
    }
    
    /**
     * 风险评估
     */
    private function assessRisk($request)
    {
        $riskScore = 0;
        $ip = $request->ip();
        $userAgent = $request->header('User-Agent');
        $url = $request->url();
        
        // 1. IP风险评估
        $riskScore += $this->assessIpRisk($ip);
        
        // 2. User-Agent风险评估
        $riskScore += $this->assessUserAgentRisk($userAgent);
        
        // 3. 请求频率风险评估
        $riskScore += $this->assessFrequencyRisk($ip);
        
        // 4. 请求路径风险评估
        $riskScore += $this->assessUrlRisk($url);
        
        // 5. 时间风险评估
        $riskScore += $this->assessTimeRisk();
        
        // 6. 地理位置风险评估
        $riskScore += $this->assessLocationRisk($ip);
        
        // 根据总分确定风险等级
        if ($riskScore >= 80) {
            return self::RISK_CRITICAL;
        } elseif ($riskScore >= 60) {
            return self::RISK_HIGH;
        } elseif ($riskScore >= 40) {
            return self::RISK_MEDIUM;
        } else {
            return self::RISK_LOW;
        }
    }
    
    /**
     * IP风险评估
     */
    private function assessIpRisk($ip)
    {
        $score = 0;
        
        // 检查是否为已知恶意IP
        if ($this->isMaliciousIp($ip)) {
            $score += 50;
        }
        
        // 检查是否为代理/VPN
        if ($this->isProxyIp($ip)) {
            $score += 20;
        }
        
        // 检查历史行为
        $historyScore = $this->getIpHistoryScore($ip);
        $score += $historyScore;
        
        return min($score, 50); // 最高50分
    }
    
    /**
     * User-Agent风险评估
     */
    private function assessUserAgentRisk($userAgent)
    {
        $score = 0;
        
        if (empty($userAgent)) {
            $score += 30;
        } elseif ($this->isSuspiciousUserAgent($userAgent)) {
            $score += 20;
        } elseif ($this->isBotUserAgent($userAgent)) {
            $score += 10;
        }
        
        return min($score, 30); // 最高30分
    }
    
    /**
     * 请求频率风险评估
     */
    private function assessFrequencyRisk($ip)
    {
        $score = 0;
        
        // 检查1分钟内请求次数
        $minuteCount = $this->getRequestCount($ip, 60);
        if ($minuteCount > 100) {
            $score += 40;
        } elseif ($minuteCount > 50) {
            $score += 20;
        } elseif ($minuteCount > 30) {
            $score += 10;
        }
        
        // 检查5分钟内请求次数
        $fiveMinuteCount = $this->getRequestCount($ip, 300);
        if ($fiveMinuteCount > 300) {
            $score += 20;
        } elseif ($fiveMinuteCount > 200) {
            $score += 10;
        }
        
        return min($score, 40); // 最高40分
    }
    
    /**
     * URL风险评估
     */
    private function assessUrlRisk($url)
    {
        $score = 0;
        
        // 敏感路径检测
        $sensitivePatterns = [
            '/admin/',
            '/api/admin/',
            '/login',
            '/register',
            '/password',
            '/upload'
        ];
        
        foreach ($sensitivePatterns as $pattern) {
            if (strpos($url, $pattern) !== false) {
                $score += 10;
                break;
            }
        }
        
        // SQL注入模式检测
        if (preg_match('/(\bunion\b|\bselect\b|\binsert\b|\bdelete\b|\bupdate\b)/i', $url)) {
            $score += 30;
        }
        
        // XSS模式检测
        if (preg_match('/<script|javascript:|on\w+=/i', $url)) {
            $score += 30;
        }
        
        return min($score, 30); // 最高30分
    }
    
    /**
     * 时间风险评估
     */
    private function assessTimeRisk()
    {
        $score = 0;
        $hour = date('H');
        
        // 深夜时段风险较高
        if ($hour >= 2 && $hour <= 5) {
            $score += 10;
        }
        
        return $score;
    }
    
    /**
     * 地理位置风险评估
     */
    private function assessLocationRisk($ip)
    {
        $score = 0;
        
        // 这里可以集成IP地理位置服务
        // 根据地理位置判断风险
        
        return $score;
    }
    
    /**
     * 检查是否为恶意IP
     */
    private function isMaliciousIp($ip)
    {
        // 检查恶意IP数据库
        $count = \think\facade\Db::name('malicious_ips')
            ->where('ip', $ip)
            ->where('status', 1)
            ->count();
        
        return $count > 0;
    }
    
    /**
     * 检查是否为代理IP
     */
    private function isProxyIp($ip)
    {
        // 简单的代理检测，实际项目中可以使用专业的代理检测服务
        $proxyHeaders = [
            'HTTP_VIA',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED'
        ];
        
        foreach ($proxyHeaders as $header) {
            if (!empty($_SERVER[$header])) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 获取IP历史评分
     */
    private function getIpHistoryScore($ip)
    {
        $score = 0;
        
        // 检查最近24小时内的异常行为
        $dayAgo = time() - 86400;
        
        $suspiciousCount = \think\facade\Db::name('security_logs')
            ->where('ip', $ip)
            ->where('create_time', '>=', $dayAgo)
            ->where('action', 'in', [
                'login_failed',
                'suspicious_activity',
                'high_risk_detected'
            ])
            ->count();
        
        $score += min($suspiciousCount * 2, 20);
        
        return $score;
    }
    
    /**
     * 检查是否为可疑User-Agent
     */
    private function isSuspiciousUserAgent($userAgent)
    {
        $suspiciousPatterns = [
            'curl',
            'wget',
            'python',
            'java',
            'perl',
            'ruby',
            'scanner',
            'bot',
            'crawler'
        ];
        
        $userAgentLower = strtolower($userAgent);
        
        foreach ($suspiciousPatterns as $pattern) {
            if (strpos($userAgentLower, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否为机器人User-Agent
     */
    private function isBotUserAgent($userAgent)
    {
        $botPatterns = [
            'googlebot',
            'bingbot',
            'slurp',
            'duckduckbot',
            'baiduspider',
            'yandexbot',
            'facebookexternalhit'
        ];
        
        $userAgentLower = strtolower($userAgent);
        
        foreach ($botPatterns as $pattern) {
            if (strpos($userAgentLower, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 获取请求次数
     */
    private function getRequestCount($ip, $seconds)
    {
        $key = "request_count:{$ip}:{$seconds}";
        return cache($key) ?: 0;
    }
    
    /**
     * 验证验证码
     */
    private function verifyCaptcha($request)
    {
        $captcha = $request->param('captcha');
        
        if (empty($captcha)) {
            return false;
        }
        
        return captcha_check($captcha);
    }
    
    /**
     * 记录风险事件
     */
    private function logRiskEvent($ip, $event, $data = [])
    {
        SecurityService::logUserAction(0, $event, array_merge($data, [
            'ip' => $ip,
            'timestamp' => time()
        ]));
    }
    
    /**
     * 记录请求信息
     */
    private function recordRequest($request, $riskLevel)
    {
        $ip = $request->ip();
        
        // 更新请求计数
        $this->updateRequestCount($ip);
        
        // 如果是高风险请求，记录详细信息
        if ($riskLevel >= self::RISK_MEDIUM) {
            SecurityService::logUserAction(0, 'risk_request', [
                'ip' => $ip,
                'risk_level' => $riskLevel,
                'url' => $request->url(),
                'method' => $request->method(),
                'user_agent' => $request->header('User-Agent'),
                'params' => $request->param()
            ]);
        }
    }
    
    /**
     * 更新请求计数
     */
    private function updateRequestCount($ip)
    {
        $intervals = [60, 300, 3600]; // 1分钟、5分钟、1小时
        
        foreach ($intervals as $interval) {
            $key = "request_count:{$ip}:{$interval}";
            $count = cache($key) ?: 0;
            cache($key, $count + 1, $interval);
        }
    }
}
