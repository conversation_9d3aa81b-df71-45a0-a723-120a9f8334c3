# SEO积分系统

专业的SEO优化积分管理平台，基于ThinkPHP 8.0开发。

## 🚀 快速开始

### 环境要求
- PHP 8.0+
- MySQL 8.0+
- Redis 6.0+
- Nginx 1.15+
- Composer

### 快速部署
1. **运行部署脚本**
   ```bash
   deploy.bat
   ```

2. **手动部署步骤**
   ```bash
   # 1. 安装依赖
   composer install
   composer require predis/predis
   
   # 2. 配置环境
   cp .env.example .env
   # 编辑 .env 文件，配置数据库和Redis连接
   
   # 3. 创建数据库
   mysql -u root -p -e "CREATE DATABASE seo_points_system"
   
   # 4. 导入数据库
   mysql -u root -p seo_points_system < database/install.sql
   
   # 5. 设置权限
   chmod -R 755 runtime/
   chmod -R 755 public/uploads/
   ```

3. **配置Nginx**
   - 配置文件位置：`d:\phpstudy_pro\Extensions\Nginx1.15.11\conf\vhosts\seo_points_system.conf`
   - 重启Nginx服务

## 📱 系统访问

### 前端地址
- **首页**: http://localhost/
- **用户中心**: http://localhost/user/
- **管理后台**: http://localhost/admin/

### API地址
- **用户API**: http://localhost/api/
- **管理API**: http://localhost/api/admin/
- **健康检查**: http://localhost/health
- **系统配置**: http://localhost/api/system/config

## 👤 默认账号

### 管理员账号
- **用户名**: admin
- **密码**: admin123456
- **登录地址**: http://localhost/admin/

### 测试用户
- **手机号**: 13800138000
- **密码**: password123
- **登录地址**: http://localhost/user/

## 🔧 系统配置

### 数据库配置 (.env)
```ini
[DATABASE]
TYPE=mysql
HOSTNAME=127.0.0.1
DATABASE=seo_points_system
USERNAME=root
PASSWORD=root
HOSTPORT=3306
CHARSET=utf8mb4
```

### Redis配置 (.env)
```ini
[REDIS]
HOST=127.0.0.1
PORT=6379
PASSWORD=
SELECT=0
```

### JWT配置 (.env)
```ini
[JWT]
SECRET=your-secret-key-here
EXPIRE=7200
```

## ⏰ 定时任务

添加以下定时任务到系统crontab：

```bash
# 任务执行（每分钟）
* * * * * cd /path/to/project && php think task:execute

# 积分结算（每小时）
0 * * * * cd /path/to/project && php think score:settlement

# 数据清理（每天凌晨2点）
0 2 * * * cd /path/to/project && php think data:cleanup

# 系统优化（每周日凌晨3点）
0 3 * * 0 cd /path/to/project && php think system:optimize
```

## 🏗️ 系统架构

### 技术栈
- **后端框架**: ThinkPHP 8.0
- **数据库**: MySQL 8.0 (支持分区表)
- **缓存**: Redis 6.0
- **Web服务器**: Nginx 1.15+
- **认证**: JWT Token
- **前端**: HTML5 + JavaScript (原生)

### 目录结构
```
WWW/
├── app/                    # 应用目录
│   ├── controller/         # 控制器
│   ├── model/             # 模型
│   ├── middleware/        # 中间件
│   ├── service/           # 服务层
│   ├── command/           # 命令行工具
│   └── validate/          # 验证器
├── config/                # 配置文件
├── database/              # 数据库文件
│   └── install.sql        # 数据库初始化脚本
├── public/                # 公共目录
│   ├── index.html         # 系统首页
│   ├── user/              # 用户前端
│   └── admin/             # 管理后台
├── route/                 # 路由配置
├── runtime/               # 运行时目录
├── vendor/                # 第三方库
├── .env                   # 环境配置
├── deploy.bat             # 部署脚本
├── 安装命令.txt            # 安装说明
└── 项目说明.md             # 项目说明
```

## 🔍 功能特性

### 用户功能
- ✅ 用户注册/登录
- ✅ 积分充值/消费
- ✅ 任务提交/管理
- ✅ 数据统计/报告
- ✅ 用户等级系统

### 管理功能
- ✅ 用户管理
- ✅ 任务审核
- ✅ 积分管理
- ✅ 财务统计
- ✅ 系统配置
- ✅ 安全日志

### 系统功能
- ✅ RESTful API
- ✅ JWT认证
- ✅ 数据分区
- ✅ 缓存优化
- ✅ 安全防护
- ✅ 健康检查

## 🛠️ 开发调试

### 启用调试模式
在 `.env` 文件中设置：
```ini
APP_DEBUG=true
APP_TRACE=true
```

### 内置服务器测试
```bash
php think run -p 8000
```
访问：http://localhost:8000

### 日志查看
- 应用日志：`runtime/log/`
- Nginx日志：`d:\phpstudy_pro\Extensions\Nginx1.15.11\logs/`

## 🔒 安全配置

### Nginx安全配置
- SQL注入防护
- XSS攻击防护
- 请求频率限制
- 文件访问控制
- 安全头设置

### 应用安全
- JWT Token认证
- 密码加密存储
- 输入数据验证
- SQL预处理
- CSRF防护

## 📊 性能优化

### 数据库优化
- 表分区设计
- 索引优化
- 查询缓存
- 连接池配置

### 缓存策略
- Redis缓存
- 查询结果缓存
- 会话缓存
- 配置缓存

## 🐛 常见问题

### 1. 404错误
**问题**: 访问页面返回404
**解决**: 检查Nginx配置，确保伪静态规则生效

### 2. 数据库连接失败
**问题**: 无法连接数据库
**解决**: 检查.env文件中的数据库配置信息

### 3. Redis连接失败
**问题**: Redis连接异常
**解决**: 确保Redis服务已启动，检查连接配置

### 4. 权限问题
**问题**: 文件写入失败
**解决**: 确保runtime目录有写入权限

### 5. API接口不存在
**问题**: 访问API返回404
**解决**: 检查路由配置和Nginx重写规则

## 📞 技术支持

如有问题，请检查：
1. 系统健康状态：http://localhost/health
2. 错误日志：`runtime/log/`
3. Nginx日志：查看访问和错误日志
4. 数据库连接：测试数据库连接是否正常

## 📄 许可证

本项目采用 MIT 许可证。

---

**SEO积分系统 v1.0.0** | 基于ThinkPHP 8.0开发
