<?php

namespace app\controller\api;

use think\Request;
use think\exception\ValidateException;
use app\service\ScoreService;
use app\service\SecurityService;
use app\validate\ScoreValidate;

/**
 * 积分管理控制器
 */
class ScoreController extends BaseController
{
    /**
     * 获取用户积分余额
     */
    public function balance(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $balance = ScoreService::getUserScore($user['id']);
            
            return $this->success([
                'balance' => $balance,
                'total_recharge' => $user['total_recharge'] ?? 0,
                'total_consumption' => $user['total_consumption'] ?? 0,
                'today_consumption' => $user['today_consumption'] ?? 0
            ]);
            
        } catch (\Exception $e) {
            return $this->error('获取积分余额失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取积分记录
     */
    public function logs(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            [$page, $limit] = $this->getPageParams();
            $type = $request->param('type');
            
            $result = ScoreService::getUserScoreLogs($user['id'], $page, $limit, $type);
            
            return $this->paginate(
                $result['list'],
                $result['total'],
                $page,
                $limit
            );
            
        } catch (\Exception $e) {
            return $this->error('获取积分记录失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取积分统计
     */
    public function statistics(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $days = max(1, min(365, (int)$request->param('days', 30)));
            
            $stats = ScoreService::getScoreStats($user['id'], $days);
            
            return $this->success($stats);
            
        } catch (\Exception $e) {
            return $this->error('获取积分统计失败：' . $e->getMessage());
        }
    }
    
    /**
     * 积分充值（预留接口，实际需要对接支付系统）
     */
    public function recharge(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $data = $request->post();
            
            // 验证数据
            $validate = new ScoreValidate();
            if (!$validate->scene('recharge')->check($data)) {
                return $this->error($validate->getError());
            }
            
            // 检查频率限制
            $rateLimitKey = 'score_recharge:' . $user['id'];
            if (!SecurityService::checkRateLimit($rateLimitKey, 5, 3600)) {
                return $this->error('充值过于频繁，请稍后再试', 429);
            }
            
            // 这里应该对接支付系统，获取支付结果
            // 暂时模拟充值成功
            $amount = $data['amount'];
            $paymentMethod = $data['payment_method'] ?? 'alipay';
            $orderNo = 'R' . date('YmdHis') . mt_rand(1000, 9999);
            
            // 记录充值（实际应该在支付回调中处理）
            $logId = ScoreService::recharge($user['id'], $amount, "在线充值 - 订单号：{$orderNo}");
            
            // 记录操作日志
            $this->logAction('score_recharge', [
                'amount' => $amount,
                'payment_method' => $paymentMethod,
                'order_no' => $orderNo,
                'log_id' => $logId
            ]);
            
            return $this->success([
                'order_no' => $orderNo,
                'amount' => $amount,
                'log_id' => $logId
            ], '充值成功');
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('充值失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取充值套餐
     */
    public function packages(Request $request)
    {
        $packages = [
            [
                'id' => 1,
                'name' => '基础套餐',
                'amount' => 100,
                'bonus' => 0,
                'total' => 100,
                'description' => '适合新手用户'
            ],
            [
                'id' => 2,
                'name' => '标准套餐',
                'amount' => 500,
                'bonus' => 50,
                'total' => 550,
                'description' => '赠送50积分，性价比高'
            ],
            [
                'id' => 3,
                'name' => '高级套餐',
                'amount' => 1000,
                'bonus' => 150,
                'total' => 1150,
                'description' => '赠送150积分，超值优惠'
            ],
            [
                'id' => 4,
                'name' => '至尊套餐',
                'amount' => 2000,
                'bonus' => 400,
                'total' => 2400,
                'description' => '赠送400积分，VIP专享'
            ]
        ];
        
        return $this->success($packages);
    }
    
    /**
     * 获取积分类型列表
     */
    public function types(Request $request)
    {
        $types = [
            [
                'value' => ScoreService::TYPE_RECHARGE,
                'label' => '充值',
                'color' => 'green'
            ],
            [
                'value' => ScoreService::TYPE_CONSUME,
                'label' => '消费',
                'color' => 'red'
            ],
            [
                'value' => ScoreService::TYPE_REFUND,
                'label' => '退款',
                'color' => 'blue'
            ],
            [
                'value' => ScoreService::TYPE_REWARD,
                'label' => '奖励',
                'color' => 'orange'
            ],
            [
                'value' => ScoreService::TYPE_DEDUCT,
                'label' => '扣除',
                'color' => 'gray'
            ]
        ];
        
        return $this->success($types);
    }
    
    /**
     * 获取积分消费明细
     */
    public function consumeDetails(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            [$page, $limit] = $this->getPageParams();
            
            // 获取消费记录
            $result = ScoreService::getUserScoreLogs($user['id'], $page, $limit, ScoreService::TYPE_CONSUME);
            
            return $this->paginate(
                $result['list'],
                $result['total'],
                $page,
                $limit
            );
            
        } catch (\Exception $e) {
            return $this->error('获取消费明细失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取积分充值记录
     */
    public function rechargeHistory(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            [$page, $limit] = $this->getPageParams();
            
            // 获取充值记录
            $result = ScoreService::getUserScoreLogs($user['id'], $page, $limit, ScoreService::TYPE_RECHARGE);
            
            return $this->paginate(
                $result['list'],
                $result['total'],
                $page,
                $limit
            );
            
        } catch (\Exception $e) {
            return $this->error('获取充值记录失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取积分退款记录
     */
    public function refundHistory(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            [$page, $limit] = $this->getPageParams();
            
            // 获取退款记录
            $result = ScoreService::getUserScoreLogs($user['id'], $page, $limit, ScoreService::TYPE_REFUND);
            
            return $this->paginate(
                $result['list'],
                $result['total'],
                $page,
                $limit
            );
            
        } catch (\Exception $e) {
            return $this->error('获取退款记录失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取积分消费趋势
     */
    public function consumeTrend(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $days = max(7, min(90, (int)$request->param('days', 30)));
            
            $trend = $this->getScoreConsumeTrend($user['id'], $days);
            
            return $this->success($trend);
            
        } catch (\Exception $e) {
            return $this->error('获取消费趋势失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取积分消费趋势数据
     */
    private function getScoreConsumeTrend($userId, $days)
    {
        $startTime = time() - ($days * 24 * 3600);
        
        $trend = \think\facade\Db::name('score_logs')
            ->where('user_id', $userId)
            ->where('type', ScoreService::TYPE_CONSUME)
            ->where('create_time', '>=', $startTime)
            ->field([
                'DATE(FROM_UNIXTIME(create_time)) as date',
                'SUM(ABS(amount)) as daily_consume',
                'COUNT(*) as daily_count'
            ])
            ->group('date')
            ->order('date', 'asc')
            ->select();
        
        $result = [
            'trend_data' => [],
            'total_consume' => 0,
            'total_count' => 0,
            'avg_daily_consume' => 0,
            'max_daily_consume' => 0
        ];
        
        $totalConsume = 0;
        $totalCount = 0;
        $maxDailyConsume = 0;
        
        foreach ($trend as $item) {
            $dailyConsume = $item['daily_consume'];
            $dailyCount = $item['daily_count'];
            
            $totalConsume += $dailyConsume;
            $totalCount += $dailyCount;
            $maxDailyConsume = max($maxDailyConsume, $dailyConsume);
            
            $result['trend_data'][] = [
                'date' => $item['date'],
                'consume' => $dailyConsume,
                'count' => $dailyCount
            ];
        }
        
        $result['total_consume'] = $totalConsume;
        $result['total_count'] = $totalCount;
        $result['max_daily_consume'] = $maxDailyConsume;
        
        if (count($trend) > 0) {
            $result['avg_daily_consume'] = round($totalConsume / count($trend), 2);
        }
        
        return $result;
    }
}
