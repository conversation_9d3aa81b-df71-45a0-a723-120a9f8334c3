<?php

namespace app\service;

use think\facade\Cache;
use think\exception\ValidateException;

/**
 * JWT服务类
 */
class JwtService
{
    /**
     * 生成JWT Token
     */
    public static function generateToken($payload, $ttl = null)
    {
        $config = config('jwt');
        $ttl = $ttl ?: $config['ttl'];
        
        $header = [
            'typ' => 'JWT',
            'alg' => $config['algo']
        ];
        
        $now = time();
        $claims = [
            'iss' => $config['issuer'],
            'aud' => $config['audience'],
            'iat' => $now,
            'nbf' => $now,
            'exp' => $now + $ttl,
            'jti' => self::generateJti(),
            'data' => $payload
        ];
        
        $headerEncoded = self::base64UrlEncode(json_encode($header));
        $payloadEncoded = self::base64UrlEncode(json_encode($claims));
        
        $signature = self::sign($headerEncoded . '.' . $payloadEncoded, $config['secret']);
        
        $token = $headerEncoded . '.' . $payloadEncoded . '.' . $signature;
        
        // 如果启用黑名单，将token存储到缓存
        if ($config['blacklist_enabled']) {
            Cache::set("jwt:token:{$claims['jti']}", $token, $ttl);
        }
        
        return $token;
    }
    
    /**
     * 验证JWT Token
     */
    public static function validateToken($token)
    {
        if (empty($token)) {
            throw new ValidateException('Token不能为空');
        }
        
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            throw new ValidateException('Token格式错误');
        }
        
        list($headerEncoded, $payloadEncoded, $signature) = $parts;
        
        // 验证签名
        $config = config('jwt');
        $expectedSignature = self::sign($headerEncoded . '.' . $payloadEncoded, $config['secret']);
        
        if (!hash_equals($signature, $expectedSignature)) {
            throw new ValidateException('Token签名验证失败');
        }
        
        // 解析payload
        $payload = json_decode(self::base64UrlDecode($payloadEncoded), true);
        if (!$payload) {
            throw new ValidateException('Token payload解析失败');
        }
        
        // 验证必需的声明
        foreach ($config['required_claims'] as $claim) {
            if (!isset($payload[$claim])) {
                throw new ValidateException("缺少必需的声明: {$claim}");
            }
        }
        
        // 验证时间
        $now = time();
        
        if (isset($payload['nbf']) && $now < $payload['nbf']) {
            throw new ValidateException('Token尚未生效');
        }
        
        if (isset($payload['exp']) && $now > $payload['exp']) {
            throw new ValidateException('Token已过期');
        }
        
        // 检查黑名单
        if ($config['blacklist_enabled'] && isset($payload['jti'])) {
            if (!Cache::has("jwt:token:{$payload['jti']}")) {
                throw new ValidateException('Token已被撤销');
            }
        }
        
        return $payload;
    }
    
    /**
     * 刷新Token
     */
    public static function refreshToken($token)
    {
        try {
            $payload = self::validateToken($token);
        } catch (ValidateException $e) {
            // 如果是过期错误，检查是否在刷新期内
            if (strpos($e->getMessage(), '已过期') !== false) {
                $parts = explode('.', $token);
                $payloadData = json_decode(self::base64UrlDecode($parts[1]), true);
                
                $config = config('jwt');
                $refreshTtl = $config['refresh_ttl'] * 60; // 转换为秒
                
                if (time() - $payloadData['exp'] <= $refreshTtl) {
                    // 在刷新期内，生成新token
                    return self::generateToken($payloadData['data']);
                }
            }
            throw $e;
        }
        
        // Token仍然有效，生成新token
        return self::generateToken($payload['data']);
    }
    
    /**
     * 撤销Token（加入黑名单）
     */
    public static function revokeToken($token)
    {
        $config = config('jwt');
        if (!$config['blacklist_enabled']) {
            return true;
        }
        
        try {
            $payload = self::validateToken($token);
            if (isset($payload['jti'])) {
                Cache::delete("jwt:token:{$payload['jti']}");
            }
            return true;
        } catch (ValidateException $e) {
            return false;
        }
    }
    
    /**
     * 获取Token中的用户数据
     */
    public static function getUserData($token)
    {
        $payload = self::validateToken($token);
        return $payload['data'] ?? [];
    }
    
    /**
     * 生成JWT ID
     */
    private static function generateJti()
    {
        return md5(uniqid(mt_rand(), true));
    }
    
    /**
     * 签名
     */
    private static function sign($data, $secret)
    {
        return self::base64UrlEncode(hash_hmac('sha256', $data, $secret, true));
    }
    
    /**
     * Base64 URL编码
     */
    private static function base64UrlEncode($data)
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    /**
     * Base64 URL解码
     */
    private static function base64UrlDecode($data)
    {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }
    
    /**
     * 清理过期的Token缓存
     */
    public static function cleanExpiredTokens()
    {
        // 这个方法需要配合定时任务使用
        // 由于ThinkPHP的Cache接口限制，这里只是示例
        // 实际实现可能需要直接操作Redis
        
        $config = config('jwt');
        if (!$config['blacklist_enabled']) {
            return;
        }
        
        // 获取所有jwt token缓存键
        $pattern = 'jwt:token:*';
        
        // 这里需要根据实际的缓存驱动实现清理逻辑
        // Redis示例：
        // $redis = Cache::store('redis')->handler();
        // $keys = $redis->keys($pattern);
        // foreach ($keys as $key) {
        //     if (!Cache::has($key)) {
        //         Cache::delete($key);
        //     }
        // }
    }
}
