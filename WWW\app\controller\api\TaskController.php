<?php

namespace app\controller\api;

use think\Request;
use think\exception\ValidateException;
use app\service\TaskService;
use app\service\SecurityService;
use app\validate\TaskValidate;

/**
 * 任务管理控制器
 */
class TaskController extends BaseController
{
    /**
     * 创建任务
     */
    public function create(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $data = $request->post();
            
            // 验证数据
            $validate = new TaskValidate();
            if (!$validate->scene('create')->check($data)) {
                return $this->error($validate->getError());
            }
            
            // 检查频率限制
            $rateLimitKey = 'task_create:' . $user['id'];
            if (!SecurityService::checkRateLimit($rateLimitKey, 10, 3600)) {
                return $this->error('任务创建过于频繁，请稍后再试', 429);
            }
            
            // 创建任务
            $taskId = TaskService::createTask($user['id'], $data);
            
            // 记录操作日志
            $this->logAction('task_create', [
                'task_id' => $taskId,
                'task_data' => $data
            ]);
            
            return $this->success([
                'task_id' => $taskId
            ], '任务创建成功');
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('任务创建失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取任务列表
     */
    public function index(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            [$page, $limit] = $this->getPageParams();
            
            // 获取筛选条件
            $filters = $this->filterEmpty([
                'type' => $request->param('type'),
                'status' => $request->param('status'),
                'keyword' => $request->param('keyword')
            ]);
            
            // 获取任务列表
            $result = TaskService::getUserTasks($user['id'], $page, $limit, $filters);
            
            return $this->paginate(
                $result['list'],
                $result['total'],
                $page,
                $limit
            );
            
        } catch (\Exception $e) {
            return $this->error('获取任务列表失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取任务详情
     */
    public function show(Request $request, $id)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $task = TaskService::getTaskById($id);
            
            if (!$task) {
                return $this->error('任务不存在', 404);
            }
            
            // 检查权限
            if ($task['user_id'] != $user['id']) {
                return $this->error('无权查看此任务', 403);
            }
            
            return $this->success($task);
            
        } catch (\Exception $e) {
            return $this->error('获取任务详情失败：' . $e->getMessage());
        }
    }
    
    /**
     * 暂停任务
     */
    public function pause(Request $request, $id)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $result = TaskService::pauseTask($id, $user['id']);
            
            if ($result) {
                // 记录操作日志
                $this->logAction('task_pause', [
                    'task_id' => $id
                ]);
                
                return $this->success(null, '任务已暂停');
            } else {
                return $this->error('任务暂停失败');
            }
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('任务暂停失败：' . $e->getMessage());
        }
    }
    
    /**
     * 恢复任务
     */
    public function resume(Request $request, $id)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $result = TaskService::resumeTask($id, $user['id']);
            
            if ($result) {
                // 记录操作日志
                $this->logAction('task_resume', [
                    'task_id' => $id
                ]);
                
                return $this->success(null, '任务已恢复');
            } else {
                return $this->error('任务恢复失败');
            }
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('任务恢复失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取任务统计
     */
    public function statistics(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $stats = $this->getTaskStatistics($user['id']);
            
            return $this->success($stats);
            
        } catch (\Exception $e) {
            return $this->error('获取统计数据失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取今日任务数量
     */
    public function todayCount(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $count = TaskService::getTodayTaskCount($user['id']);
            $maxTasks = $user['max_daily_tasks'] ?? 50;
            
            return $this->success([
                'today_count' => $count,
                'max_daily_tasks' => $maxTasks,
                'remaining' => max(0, $maxTasks - $count)
            ]);
            
        } catch (\Exception $e) {
            return $this->error('获取今日任务数量失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取任务类型列表
     */
    public function types(Request $request)
    {
        $types = [
            [
                'value' => TaskService::TYPE_KEYWORD_RANKING,
                'label' => '关键词排名',
                'description' => '提升指定关键词在搜索引擎中的排名'
            ],
            [
                'value' => TaskService::TYPE_DROPDOWN_WORDS,
                'label' => '下拉词',
                'description' => '在搜索框中显示相关下拉词'
            ]
        ];
        
        return $this->success($types);
    }
    
    /**
     * 获取任务状态列表
     */
    public function statuses(Request $request)
    {
        $statuses = [
            [
                'value' => TaskService::STATUS_PENDING,
                'label' => '待审核',
                'color' => 'orange'
            ],
            [
                'value' => TaskService::STATUS_RUNNING,
                'label' => '执行中',
                'color' => 'blue'
            ],
            [
                'value' => TaskService::STATUS_COMPLETED,
                'label' => '已完成',
                'color' => 'green'
            ],
            [
                'value' => TaskService::STATUS_PAUSED,
                'label' => '已暂停',
                'color' => 'yellow'
            ],
            [
                'value' => TaskService::STATUS_CANCELLED,
                'label' => '已取消',
                'color' => 'red'
            ]
        ];
        
        return $this->success($statuses);
    }
    
    /**
     * 获取任务统计数据
     */
    private function getTaskStatistics($userId)
    {
        $stats = \think\facade\Db::name('tasks')
            ->where('user_id', $userId)
            ->field([
                'status',
                'COUNT(*) as count',
                'SUM(pre_deduct_score) as total_score',
                'SUM(actual_cost_score) as actual_score'
            ])
            ->group('status')
            ->select();
        
        $result = [
            'total_tasks' => 0,
            'total_score' => 0,
            'actual_score' => 0,
            'by_status' => []
        ];
        
        foreach ($stats as $stat) {
            $result['total_tasks'] += $stat['count'];
            $result['total_score'] += $stat['total_score'];
            $result['actual_score'] += $stat['actual_score'];
            
            $result['by_status'][$stat['status']] = [
                'status' => $stat['status'],
                'status_text' => TaskService::getStatusText($stat['status']),
                'count' => $stat['count'],
                'total_score' => $stat['total_score'],
                'actual_score' => $stat['actual_score']
            ];
        }
        
        // 今日任务统计
        $todayStart = strtotime(date('Y-m-d'));
        $todayStats = \think\facade\Db::name('tasks')
            ->where('user_id', $userId)
            ->where('create_time', '>=', $todayStart)
            ->field([
                'COUNT(*) as today_count',
                'SUM(pre_deduct_score) as today_score'
            ])
            ->find();
        
        $result['today_count'] = $todayStats['today_count'] ?? 0;
        $result['today_score'] = $todayStats['today_score'] ?? 0;
        
        return $result;
    }
}
