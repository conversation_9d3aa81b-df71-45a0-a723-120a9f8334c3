TYPE=VIEW
query=select `douyin_comments`.`comments`.`uid` AS `uid`,`douyin_comments`.`comments`.`nickname` AS `nickname`,count(0) AS `total_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'primary\') then 1 else 0 end)) AS `primary_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'reply\') then 1 else 0 end)) AS `reply_comments`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes_received`,round(avg(`douyin_comments`.`comments`.`digg_count`),2) AS `avg_likes_per_comment`,count(distinct `douyin_comments`.`comments`.`video_id`) AS `videos_commented`,min(`douyin_comments`.`comments`.`collection_time`) AS `first_comment_time`,max(`douyin_comments`.`comments`.`collection_time`) AS `last_comment_time`,`douyin_comments`.`comments`.`ip_label` AS `ip_label`,((to_days(max(`douyin_comments`.`comments`.`collection_time`)) - to_days(min(`douyin_comments`.`comments`.`collection_time`))) + 1) AS `active_days`,round((count(0) / ((to_days(max(`douyin_comments`.`comments`.`collection_time`)) - to_days(min(`douyin_comments`.`comments`.`collection_time`))) + 1)),2) AS `comments_per_day` from `douyin_comments`.`comments` where ((`douyin_comments`.`comments`.`uid` is not null) and (`douyin_comments`.`comments`.`uid` <> \'\')) group by `douyin_comments`.`comments`.`uid`,`douyin_comments`.`comments`.`nickname`,`douyin_comments`.`comments`.`ip_label` having (`total_comments` >= 2) order by `total_comments` desc
md5=7eb9080db64946bc1b2c1e0c1f9c7084
updatable=0
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=2025-06-23 12:49:47
create-version=1
source=SELECT 
\n    uid,
\n    nickname,
\n    COUNT(*) as total_comments,
\n    SUM(CASE WHEN comment_type = \'primary\' THEN 1 ELSE 0 END) as primary_comments,
\n    SUM(CASE WHEN comment_type = \'reply\' THEN 1 ELSE 0 END) as reply_comments,
\n    SUM(digg_count) as total_likes_received,
\n    ROUND(AVG(digg_count), 2) as avg_likes_per_comment,
\n    COUNT(DISTINCT video_id) as videos_commented,
\n    MIN(collection_time) as first_comment_time,
\n    MAX(collection_time) as last_comment_time,
\n    ip_label,
\n    -- 活跃度指标
\n    DATEDIFF(MAX(collection_time), MIN(collection_time)) + 1 as active_days,
\n    ROUND(COUNT(*) / (DATEDIFF(MAX(collection_time), MIN(collection_time)) + 1), 2) as comments_per_day
\nFROM comments 
\nWHERE uid IS NOT NULL AND uid != \'\'
\nGROUP BY uid, nickname, ip_label
\nHAVING total_comments >= 2  -- 只显示评论数>=2的用户
\nORDER BY total_comments DESC
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_general_ci
view_body_utf8=select `douyin_comments`.`comments`.`uid` AS `uid`,`douyin_comments`.`comments`.`nickname` AS `nickname`,count(0) AS `total_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'primary\') then 1 else 0 end)) AS `primary_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'reply\') then 1 else 0 end)) AS `reply_comments`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes_received`,round(avg(`douyin_comments`.`comments`.`digg_count`),2) AS `avg_likes_per_comment`,count(distinct `douyin_comments`.`comments`.`video_id`) AS `videos_commented`,min(`douyin_comments`.`comments`.`collection_time`) AS `first_comment_time`,max(`douyin_comments`.`comments`.`collection_time`) AS `last_comment_time`,`douyin_comments`.`comments`.`ip_label` AS `ip_label`,((to_days(max(`douyin_comments`.`comments`.`collection_time`)) - to_days(min(`douyin_comments`.`comments`.`collection_time`))) + 1) AS `active_days`,round((count(0) / ((to_days(max(`douyin_comments`.`comments`.`collection_time`)) - to_days(min(`douyin_comments`.`comments`.`collection_time`))) + 1)),2) AS `comments_per_day` from `douyin_comments`.`comments` where ((`douyin_comments`.`comments`.`uid` is not null) and (`douyin_comments`.`comments`.`uid` <> \'\')) group by `douyin_comments`.`comments`.`uid`,`douyin_comments`.`comments`.`nickname`,`douyin_comments`.`comments`.`ip_label` having (`total_comments` >= 2) order by `total_comments` desc
