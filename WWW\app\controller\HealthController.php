<?php

namespace app\controller;

use think\facade\Db;
use think\facade\Cache;
use think\Response;

/**
 * 健康检查控制器
 */
class HealthController
{
    /**
     * 健康检查
     */
    public function index()
    {
        $status = 'ok';
        $checks = [];
        $timestamp = time();
        
        try {
            // 检查数据库连接
            $dbCheck = $this->checkDatabase();
            $checks['database'] = $dbCheck;
            
            // 检查Redis连接
            $redisCheck = $this->checkRedis();
            $checks['redis'] = $redisCheck;
            
            // 检查磁盘空间
            $diskCheck = $this->checkDiskSpace();
            $checks['disk'] = $diskCheck;
            
            // 检查PHP扩展
            $extensionCheck = $this->checkExtensions();
            $checks['extensions'] = $extensionCheck;
            
            // 检查目录权限
            $permissionCheck = $this->checkPermissions();
            $checks['permissions'] = $permissionCheck;
            
            // 检查系统负载
            $loadCheck = $this->checkSystemLoad();
            $checks['system_load'] = $loadCheck;
            
            // 判断整体状态
            foreach ($checks as $check) {
                if ($check['status'] !== 'ok') {
                    $status = 'warning';
                    if ($check['status'] === 'error') {
                        $status = 'error';
                        break;
                    }
                }
            }
            
        } catch (\Exception $e) {
            $status = 'error';
            $checks['error'] = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
        
        $response = [
            'status' => $status,
            'timestamp' => $timestamp,
            'datetime' => date('Y-m-d H:i:s', $timestamp),
            'checks' => $checks,
            'system_info' => [
                'php_version' => PHP_VERSION,
                'memory_usage' => $this->formatBytes(memory_get_usage(true)),
                'memory_peak' => $this->formatBytes(memory_get_peak_usage(true)),
                'uptime' => $this->getUptime()
            ]
        ];
        
        // 根据状态设置HTTP状态码
        $httpStatus = 200;
        if ($status === 'warning') {
            $httpStatus = 200; // 警告仍返回200
        } elseif ($status === 'error') {
            $httpStatus = 503; // 服务不可用
        }
        
        return Response::create($response, 'json', $httpStatus)
            ->header('Content-Type', 'application/json; charset=utf-8')
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }
    
    /**
     * 检查数据库连接
     */
    private function checkDatabase()
    {
        try {
            $startTime = microtime(true);
            
            // 测试数据库连接
            $result = Db::query('SELECT 1 as test');
            
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            if ($result && isset($result[0]['test']) && $result[0]['test'] == 1) {
                return [
                    'status' => 'ok',
                    'message' => 'Database connection successful',
                    'response_time' => $responseTime . 'ms'
                ];
            } else {
                return [
                    'status' => 'error',
                    'message' => 'Database query failed'
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 检查Redis连接
     */
    private function checkRedis()
    {
        try {
            $startTime = microtime(true);
            
            // 测试Redis连接
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';
            
            Cache::set($testKey, $testValue, 10);
            $result = Cache::get($testKey);
            Cache::delete($testKey);
            
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            if ($result === $testValue) {
                return [
                    'status' => 'ok',
                    'message' => 'Redis connection successful',
                    'response_time' => $responseTime . 'ms'
                ];
            } else {
                return [
                    'status' => 'error',
                    'message' => 'Redis read/write test failed'
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Redis connection failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 检查磁盘空间
     */
    private function checkDiskSpace()
    {
        try {
            $totalSpace = disk_total_space('.');
            $freeSpace = disk_free_space('.');
            $usedSpace = $totalSpace - $freeSpace;
            $usagePercent = round(($usedSpace / $totalSpace) * 100, 2);
            
            $status = 'ok';
            $message = 'Disk space sufficient';
            
            if ($usagePercent > 90) {
                $status = 'error';
                $message = 'Disk space critically low';
            } elseif ($usagePercent > 80) {
                $status = 'warning';
                $message = 'Disk space running low';
            }
            
            return [
                'status' => $status,
                'message' => $message,
                'usage_percent' => $usagePercent . '%',
                'free_space' => $this->formatBytes($freeSpace),
                'total_space' => $this->formatBytes($totalSpace)
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Failed to check disk space: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 检查PHP扩展
     */
    private function checkExtensions()
    {
        $requiredExtensions = [
            'pdo',
            'pdo_mysql',
            'json',
            'mbstring',
            'openssl',
            'curl'
        ];
        
        $optionalExtensions = [
            'redis',
            'opcache',
            'gd',
            'zip'
        ];
        
        $missing = [];
        $optional_missing = [];
        
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                $missing[] = $ext;
            }
        }
        
        foreach ($optionalExtensions as $ext) {
            if (!extension_loaded($ext)) {
                $optional_missing[] = $ext;
            }
        }
        
        $status = 'ok';
        $message = 'All required extensions loaded';
        
        if (!empty($missing)) {
            $status = 'error';
            $message = 'Missing required extensions: ' . implode(', ', $missing);
        } elseif (!empty($optional_missing)) {
            $status = 'warning';
            $message = 'Missing optional extensions: ' . implode(', ', $optional_missing);
        }
        
        return [
            'status' => $status,
            'message' => $message,
            'missing_required' => $missing,
            'missing_optional' => $optional_missing
        ];
    }
    
    /**
     * 检查目录权限
     */
    private function checkPermissions()
    {
        $directories = [
            'runtime',
            'runtime/cache',
            'runtime/log',
            'runtime/temp',
            'public/uploads'
        ];
        
        $issues = [];
        
        foreach ($directories as $dir) {
            $fullPath = root_path() . $dir;
            
            if (!is_dir($fullPath)) {
                // 尝试创建目录
                if (!mkdir($fullPath, 0755, true)) {
                    $issues[] = "Cannot create directory: $dir";
                }
            } elseif (!is_writable($fullPath)) {
                $issues[] = "Directory not writable: $dir";
            }
        }
        
        $status = empty($issues) ? 'ok' : 'error';
        $message = empty($issues) ? 'All directories have proper permissions' : 'Permission issues found';
        
        return [
            'status' => $status,
            'message' => $message,
            'issues' => $issues
        ];
    }
    
    /**
     * 检查系统负载
     */
    private function checkSystemLoad()
    {
        try {
            $memoryUsage = memory_get_usage(true);
            $memoryLimit = $this->parseBytes(ini_get('memory_limit'));
            $memoryPercent = round(($memoryUsage / $memoryLimit) * 100, 2);
            
            $status = 'ok';
            $message = 'System load normal';
            
            if ($memoryPercent > 90) {
                $status = 'error';
                $message = 'Memory usage critically high';
            } elseif ($memoryPercent > 80) {
                $status = 'warning';
                $message = 'Memory usage high';
            }
            
            return [
                'status' => $status,
                'message' => $message,
                'memory_usage' => $this->formatBytes($memoryUsage),
                'memory_limit' => $this->formatBytes($memoryLimit),
                'memory_percent' => $memoryPercent . '%'
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Failed to check system load: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * 解析字节数
     */
    private function parseBytes($val)
    {
        $val = trim($val);
        $last = strtolower($val[strlen($val) - 1]);
        $val = (int) $val;
        
        switch ($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }
        
        return $val;
    }
    
    /**
     * 获取系统运行时间
     */
    private function getUptime()
    {
        if (function_exists('sys_getloadavg') && is_readable('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $uptime = floatval($uptime);
            
            $days = floor($uptime / 86400);
            $hours = floor(($uptime % 86400) / 3600);
            $minutes = floor(($uptime % 3600) / 60);
            
            return "{$days}d {$hours}h {$minutes}m";
        }
        
        return 'Unknown';
    }
}
