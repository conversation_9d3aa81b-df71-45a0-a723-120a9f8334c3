-- SEO积分系统数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `seo_points_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `seo_points_system`;

SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户组表
-- ----------------------------
DROP TABLE IF EXISTS `user_groups`;
CREATE TABLE `user_groups` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户组ID',
  `name` varchar(50) NOT NULL COMMENT '用户组名称',
  `score_cost` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '积分消费倍率',
  `promotion_condition` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '升级条件（消费金额）',
  `max_daily_tasks` int(11) unsigned NOT NULL DEFAULT '10' COMMENT '每日最大任务数',
  `benefits` text DEFAULT NULL COMMENT '用户组权益（JSON格式）',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序权重',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_sort_status` (`sort`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组表';

-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `mobile` varchar(20) NOT NULL COMMENT '手机号（加密存储）',
  `mobile_hash` varchar(64) NOT NULL COMMENT '手机号哈希(用于查询)',
  `password` varchar(255) NOT NULL COMMENT '密码（bcrypt加密）',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `group_id` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '用户组ID',
  `score` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '积分余额',
  `total_recharge` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计充值',
  `total_consumption` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计消费',
  `today_consumption` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '今日消费',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1正常 2锁定 3禁用',
  `last_login_time` int(11) unsigned DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` int(11) unsigned NOT NULL COMMENT '注册时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_mobile_hash` (`mobile_hash`),
  KEY `idx_group_status` (`group_id`, `status`),
  KEY `idx_status_create` (`status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 管理员表
-- ----------------------------
DROP TABLE IF EXISTS `admins`;
CREATE TABLE `admins` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码（bcrypt加密）',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `role` varchar(50) NOT NULL DEFAULT 'admin' COMMENT '角色：super_admin超级管理员 admin普通管理员',
  `permissions` text DEFAULT NULL COMMENT '权限列表（JSON格式）',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1正常 2锁定 3禁用',
  `last_login_time` int(11) unsigned DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_role_status` (`role`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- ----------------------------
-- 系统配置表
-- ----------------------------
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `key` varchar(100) NOT NULL COMMENT '配置键',
  `value` text NOT NULL COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '数据类型',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key` (`key`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ----------------------------
-- 插入默认数据
-- ----------------------------

-- 插入用户组数据
INSERT INTO `user_groups` (`id`, `name`, `score_cost`, `promotion_condition`, `max_daily_tasks`, `benefits`, `sort`, `status`, `create_time`, `update_time`) VALUES
(1, '新手用户', 1.00, 0.00, 10, '{"description": "新用户默认等级"}', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '普通用户', 0.95, 100.00, 20, '{"description": "消费满100元升级", "discount": "5%"}', 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '高级用户', 0.90, 500.00, 50, '{"description": "消费满500元升级", "discount": "10%"}', 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, '资深用户', 0.85, 1000.00, 100, '{"description": "消费满1000元升级", "discount": "15%"}', 4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(5, 'VIP用户', 0.80, 5000.00, 200, '{"description": "消费满5000元升级", "discount": "20%"}', 5, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入默认管理员账号 (密码: admin123456)
INSERT INTO `admins` (`username`, `password`, `email`, `nickname`, `role`, `permissions`, `status`, `create_time`, `update_time`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '超级管理员', 'super_admin', '[\"*\"]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入测试用户 (密码: password123)
INSERT INTO `users` (`mobile`, `mobile_hash`, `password`, `nickname`, `group_id`, `score`, `status`, `create_time`, `update_time`) VALUES
('***********', MD5('***********'), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户', 1, 100.00, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入系统配置
INSERT INTO `system_configs` (`key`, `value`, `description`, `type`, `status`, `create_time`, `update_time`) VALUES
('site_name', 'SEO积分系统', '网站名称', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('site_description', '专业的SEO优化积分管理平台', '网站描述', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('contact_email', '<EMAIL>', '联系邮箱', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('min_recharge_amount', '10', '最小充值金额', 'number', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('max_recharge_amount', '10000', '最大充值金额', 'number', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('task_review_required', '1', '任务是否需要审核', 'boolean', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('auto_settlement_enabled', '1', '是否启用自动结算', 'boolean', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('maintenance_mode', '0', '维护模式', 'boolean', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

SET FOREIGN_KEY_CHECKS = 1;

-- 安装完成提示
SELECT 'SEO积分系统数据库初始化完成！' as message,
       '管理员账号: admin / admin123456' as admin_account,
       '测试用户: *********** / password123' as test_account;
