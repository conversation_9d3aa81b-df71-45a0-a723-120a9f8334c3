<?php

namespace app\controller\api;

use think\Request;
use think\facade\Cache;

/**
 * 系统控制器
 */
class SystemController extends BaseController
{
    /**
     * 获取系统配置
     */
    public function config(Request $request)
    {
        try {
            // 获取公开的系统配置
            $config = [
                'system_name' => config('system.system_name', 'SEO积分系统'),
                'system_version' => config('system.version', '1.0.0'),
                'contact_qq' => config('system.contact_qq', ''),
                'contact_wechat' => config('system.contact_wechat', ''),
                'contact_phone' => config('system.contact_phone', ''),
                'service_hours' => config('system.service_hours', '9:00-18:00'),
                'announcement' => config('system.announcement', ''),
                'user_agreement' => config('system.user_agreement', ''),
                'privacy_policy' => config('system.privacy_policy', ''),
                'features' => [
                    'keyword_ranking' => config('system.enable_keyword_ranking', true),
                    'dropdown_words' => config('system.enable_dropdown_words', true),
                    'auto_recharge' => config('system.enable_auto_recharge', false),
                    'api_access' => config('system.enable_api_access', false)
                ],
                'limits' => [
                    'max_daily_tasks' => config('system.max_daily_tasks', 100),
                    'max_keyword_length' => config('system.max_keyword_length', 50),
                    'max_url_length' => config('system.max_url_length', 200),
                    'min_recharge_amount' => config('system.min_recharge_amount', 10)
                ]
            ];
            
            return $this->success($config);
            
        } catch (\Exception $e) {
            return $this->error('获取系统配置失败：' . $e->getMessage());
        }
    }
    
    /**
     * 生成验证码
     */
    public function captcha(Request $request)
    {
        try {
            $config = [
                'fontSize' => 16,
                'length' => 4,
                'useNoise' => true,
                'useCurve' => true,
                'imageH' => 40,
                'imageW' => 120,
                'fontttf' => '',
                'bg' => [255, 255, 255],
                'expire' => 300 // 5分钟过期
            ];
            
            $captcha = new \think\captcha\Captcha($config);
            return $captcha->entry();
            
        } catch (\Exception $e) {
            return $this->error('生成验证码失败：' . $e->getMessage());
        }
    }
    
    /**
     * 验证验证码
     */
    public function verifyCaptcha(Request $request)
    {
        try {
            $code = $request->param('code', '');
            
            if (empty($code)) {
                return $this->error('验证码不能为空');
            }
            
            $result = captcha_check($code);
            
            if ($result) {
                return $this->success(null, '验证码正确');
            } else {
                return $this->error('验证码错误');
            }
            
        } catch (\Exception $e) {
            return $this->error('验证验证码失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取用户等级配置
     */
    public function userLevels(Request $request)
    {
        try {
            $levels = config('system.user_groups', []);
            
            // 只返回公开信息
            $publicLevels = [];
            foreach ($levels as $key => $level) {
                $publicLevels[$key] = [
                    'name' => $level['name'],
                    'discount' => $level['discount'],
                    'daily_task_limit' => $level['daily_task_limit'],
                    'upgrade_threshold' => $level['upgrade_threshold'] ?? 0,
                    'benefits' => $level['benefits'] ?? []
                ];
            }
            
            return $this->success($publicLevels);
            
        } catch (\Exception $e) {
            return $this->error('获取用户等级配置失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取支付方式
     */
    public function paymentMethods(Request $request)
    {
        try {
            $methods = [];
            
            // 支付宝
            if (config('payment.alipay.enable', false)) {
                $methods[] = [
                    'code' => 'alipay',
                    'name' => '支付宝',
                    'icon' => '/static/images/alipay.png',
                    'min_amount' => config('payment.alipay.min_amount', 1),
                    'max_amount' => config('payment.alipay.max_amount', 10000)
                ];
            }
            
            // 微信支付
            if (config('payment.wechat.enable', false)) {
                $methods[] = [
                    'code' => 'wechat',
                    'name' => '微信支付',
                    'icon' => '/static/images/wechat.png',
                    'min_amount' => config('payment.wechat.min_amount', 1),
                    'max_amount' => config('payment.wechat.max_amount', 10000)
                ];
            }
            
            // 银行卡
            if (config('payment.bank.enable', false)) {
                $methods[] = [
                    'code' => 'bank',
                    'name' => '银行卡',
                    'icon' => '/static/images/bank.png',
                    'min_amount' => config('payment.bank.min_amount', 10),
                    'max_amount' => config('payment.bank.max_amount', 50000)
                ];
            }
            
            return $this->success($methods);
            
        } catch (\Exception $e) {
            return $this->error('获取支付方式失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取搜索引擎列表
     */
    public function searchEngines(Request $request)
    {
        try {
            $engines = [
                [
                    'code' => 'baidu',
                    'name' => '百度',
                    'icon' => '/static/images/baidu.png',
                    'enabled' => config('system.enable_baidu', true)
                ],
                [
                    'code' => 'google',
                    'name' => '谷歌',
                    'icon' => '/static/images/google.png',
                    'enabled' => config('system.enable_google', false)
                ],
                [
                    'code' => 'sogou',
                    'name' => '搜狗',
                    'icon' => '/static/images/sogou.png',
                    'enabled' => config('system.enable_sogou', true)
                ],
                [
                    'code' => '360',
                    'name' => '360搜索',
                    'icon' => '/static/images/360.png',
                    'enabled' => config('system.enable_360', true)
                ]
            ];
            
            // 只返回启用的搜索引擎
            $enabledEngines = array_filter($engines, function($engine) {
                return $engine['enabled'];
            });
            
            return $this->success(array_values($enabledEngines));
            
        } catch (\Exception $e) {
            return $this->error('获取搜索引擎列表失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取系统状态
     */
    public function status(Request $request)
    {
        try {
            $status = [
                'system' => 'online',
                'database' => $this->checkDatabase(),
                'cache' => $this->checkCache(),
                'queue' => $this->checkQueue(),
                'storage' => $this->checkStorage(),
                'timestamp' => time()
            ];
            
            return $this->success($status);
            
        } catch (\Exception $e) {
            return $this->error('获取系统状态失败：' . $e->getMessage());
        }
    }
    
    /**
     * 检查数据库状态
     */
    private function checkDatabase()
    {
        try {
            \think\facade\Db::query('SELECT 1');
            return 'online';
        } catch (\Exception $e) {
            return 'offline';
        }
    }
    
    /**
     * 检查缓存状态
     */
    private function checkCache()
    {
        try {
            Cache::set('health_check', time(), 10);
            $value = Cache::get('health_check');
            return $value ? 'online' : 'offline';
        } catch (\Exception $e) {
            return 'offline';
        }
    }
    
    /**
     * 检查队列状态
     */
    private function checkQueue()
    {
        // 这里可以检查队列服务状态
        // 暂时返回在线状态
        return 'online';
    }
    
    /**
     * 检查存储状态
     */
    private function checkStorage()
    {
        try {
            $testFile = runtime_path() . 'health_check.txt';
            file_put_contents($testFile, time());
            $content = file_get_contents($testFile);
            unlink($testFile);
            return $content ? 'online' : 'offline';
        } catch (\Exception $e) {
            return 'offline';
        }
    }
    
    /**
     * 获取帮助文档
     */
    public function help(Request $request)
    {
        try {
            $type = $request->param('type', 'general');
            
            $helpContent = [
                'general' => [
                    'title' => '使用帮助',
                    'content' => '这里是系统使用的基本帮助信息...'
                ],
                'task' => [
                    'title' => '任务管理',
                    'content' => '任务管理相关的帮助信息...'
                ],
                'score' => [
                    'title' => '积分系统',
                    'content' => '积分系统相关的帮助信息...'
                ],
                'api' => [
                    'title' => 'API接口',
                    'content' => 'API接口使用相关的帮助信息...'
                ]
            ];
            
            $help = $helpContent[$type] ?? $helpContent['general'];
            
            return $this->success($help);
            
        } catch (\Exception $e) {
            return $this->error('获取帮助文档失败：' . $e->getMessage());
        }
    }
    
    /**
     * 意见反馈
     */
    public function feedback(Request $request)
    {
        try {
            $data = $request->post();
            
            // 验证数据
            if (empty($data['content'])) {
                return $this->error('反馈内容不能为空');
            }
            
            if (strlen($data['content']) > 1000) {
                return $this->error('反馈内容不能超过1000字符');
            }
            
            $user = $this->getUser();
            
            $feedbackData = [
                'user_id' => $user ? $user['id'] : 0,
                'type' => $data['type'] ?? 'general',
                'content' => $data['content'],
                'contact' => $data['contact'] ?? '',
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'create_time' => time()
            ];
            
            $result = \think\facade\Db::name('user_feedback')->insert($feedbackData);
            
            if ($result) {
                return $this->success(null, '反馈提交成功，感谢您的建议');
            } else {
                return $this->error('反馈提交失败');
            }
            
        } catch (\Exception $e) {
            return $this->error('提交反馈失败：' . $e->getMessage());
        }
    }
}
