<?php

return [
    // 系统基础配置
    'site_name' => 'SEO积分系统',
    'site_logo' => '/static/images/logo.png',
    'contact_phone' => '400-xxx-xxxx',
    'contact_email' => '<EMAIL>',
    'icp_number' => '京ICP备xxxxxxxx号',
    'copyright' => '© 2024 SEO积分系统 版权所有',
    
    // 业务配置
    'default_score_cost' => 1.0, // 默认积分单价
    'min_recharge_amount' => 100, // 最小充值金额
    'max_daily_tasks' => 50, // 每日最大任务数
    'task_review_timeout' => 24, // 任务审核超时时间（小时）
    'score_refund_rate' => 0.8, // 积分退款比例
    'sensitive_word_check' => true, // 是否启用敏感词检测
    'auto_approve_keywords' => false, // 是否自动审核关键词
    'max_keyword_length' => 50, // 关键词最大长度
    'min_online_days' => 1, // 最小上线天数
    'max_online_days' => 30, // 最大上线天数
    
    // 用户组配置
    'user_groups' => [
        1 => [
            'name' => '新手用户',
            'score_cost' => 1.0,
            'promotion_condition' => 1000,
            'max_daily_tasks' => 20,
            'benefits' => ['基础功能']
        ],
        2 => [
            'name' => '普通用户', 
            'score_cost' => 0.9,
            'promotion_condition' => 5000,
            'max_daily_tasks' => 30,
            'benefits' => ['9折优惠', '优先审核']
        ],
        3 => [
            'name' => '高级用户',
            'score_cost' => 0.8,
            'promotion_condition' => 20000,
            'max_daily_tasks' => 40,
            'benefits' => ['8折优惠', '专属客服', '高级功能']
        ],
        4 => [
            'name' => '元老用户',
            'score_cost' => 0.7,
            'promotion_condition' => 50000,
            'max_daily_tasks' => 50,
            'benefits' => ['7折优惠', 'VIP通道', '定制服务']
        ],
        5 => [
            'name' => 'VIP用户',
            'score_cost' => 0.6,
            'promotion_condition' => 100000,
            'max_daily_tasks' => 100,
            'benefits' => ['6折优惠', '专属经理', '无限制功能']
        ]
    ],
    
    // 搜索引擎配置
    'search_engines' => [
        'baidu' => '百度',
        '360' => '360搜索',
        'sogou' => '搜狗',
        'sm' => '神马'
    ],
    
    // 任务类型配置
    'task_types' => [
        1 => [
            'name' => '关键词排名',
            'description' => '提升关键词在搜索引擎中的排名',
            'multiplier' => 1.0
        ],
        2 => [
            'name' => '下拉词',
            'description' => '在搜索框中显示相关下拉词',
            'multiplier' => 1.5
        ]
    ],
    
    // 任务状态配置
    'task_status' => [
        1 => '待审核',
        2 => '执行中',
        3 => '已完成',
        4 => '已暂停',
        5 => '已取消'
    ],
    
    // 积分操作类型
    'score_types' => [
        1 => '充值',
        2 => '消费',
        3 => '退款',
        4 => '奖励',
        5 => '扣除'
    ],
    
    // 文件上传配置
    'upload' => [
        'max_size' => env('UPLOAD_MAX_SIZE', 10485760), // 10MB
        'allowed_ext' => explode(',', env('UPLOAD_ALLOWED_EXT', 'jpg,jpeg,png,gif')),
        'path' => 'uploads/',
    ],
    
    // 监控配置
    'monitor' => [
        'webhook_url' => env('MONITOR_WEBHOOK_URL', ''),
        'slow_query_threshold' => 1000, // 慢查询阈值（毫秒）
        'memory_threshold' => 128 * 1024 * 1024, // 内存使用阈值（字节）
    ],
];
