# SEO积分系统开发文档 - 完善版3.1

## 项目概述

### 系统架构
- **后端框架**: ThinkPHP 8
- **前端技术**: HTML5 + CSS3 + JavaScript + jQuery
- **数据库**: MySQL 8.0+
- **缓存**: Redis (支持主从复制)
- **队列**: Redis队列 (分级处理)
- **Web服务器**: Nginx + PHP-FPM
- **监控**: 性能监控 + 安全监控

### 技术栈说明
- **PHP版本**: 8.1+
- **数据库引擎**: InnoDB
- **字符集**: utf8mb4
- **API风格**: RESTful API
- **认证方式**: JWT <PERSON>ken (增强安全配置)
- **缓存策略**: 多层缓存架构
- **安全防护**: 多重安全机制

## 🚀 性能优化架构

### 1. 数据库性能优化

#### 索引优化策略
```sql
-- 任务表复合索引优化
ALTER TABLE tasks ADD INDEX idx_user_status_type (user_id, status, type);
ALTER TABLE tasks ADD INDEX idx_status_create_time (status, create_time);
ALTER TABLE tasks ADD INDEX idx_search_engine_status (search_engine, status);
ALTER TABLE tasks ADD INDEX idx_online_days_status (online_days, status);

-- 积分记录表优化
ALTER TABLE score_logs ADD INDEX idx_user_type_time (user_id, type, create_time);
ALTER TABLE score_logs ADD INDEX idx_task_type (task_id, type);
ALTER TABLE score_logs ADD INDEX idx_type_create_time (type, create_time);

-- 用户表优化
ALTER TABLE users ADD INDEX idx_group_status (group_id, status);
ALTER TABLE users ADD INDEX idx_status_create_time (status, create_time);
ALTER TABLE users ADD INDEX idx_mobile_status (mobile, status);

-- 登录日志表优化
ALTER TABLE user_login_logs ADD INDEX idx_user_login_time (user_id, login_time);
ALTER TABLE user_login_logs ADD INDEX idx_login_time (login_time);
```

#### 分区表设计
```sql
-- 积分记录表按月分区
ALTER TABLE score_logs PARTITION BY RANGE (create_time) (
    PARTITION p202401 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01')),
    PARTITION p202403 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01')),
    PARTITION p202404 VALUES LESS THAN (UNIX_TIMESTAMP('2024-05-01')),
    PARTITION p202405 VALUES LESS THAN (UNIX_TIMESTAMP('2024-06-01')),
    PARTITION p202406 VALUES LESS THAN (UNIX_TIMESTAMP('2024-07-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 任务执行日志表按月分区
ALTER TABLE task_execution_logs PARTITION BY RANGE (create_time) (
    PARTITION p202401 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01')),
    PARTITION p202403 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 2. 多层缓存架构

#### Redis主从配置
```php
// config/cache.php
'redis' => [
    'type' => 'redis',
    'host' => '127.0.0.1',
    'port' => 6379,
    'password' => env('REDIS_PASSWORD', ''),
    'select' => 0,
    'timeout' => 0,
    'expire' => 0,
    'persistent' => false,
    'prefix' => 'seo_',
    // 主从配置
    'master' => [
        'host' => '127.0.0.1',
        'port' => 6379,
        'password' => env('REDIS_PASSWORD', ''),
    ],
    'slave' => [
        'host' => '127.0.0.1',
        'port' => 6380,
        'password' => env('REDIS_PASSWORD', ''),
    ]
],
```

#### 缓存策略实现
```php
// app/service/CacheService.php
class CacheService
{
    // 用户信息缓存（1小时）
    public static function getUserInfo($userId)
    {
        $key = 'user_info_' . $userId;
        return Cache::tag('user')->remember($key, function() use ($userId) {
            return Db::name('users')->where('id', $userId)->find();
        }, 3600);
    }
    
    // 用户组配置缓存（24小时）
    public static function getUserGroups()
    {
        return Cache::tag('config')->remember('user_groups', function() {
            return Db::name('user_groups')->where('status', 1)->order('sort')->select();
        }, 86400);
    }
    
    // 敏感词缓存（12小时）
    public static function getSensitiveWords()
    {
        return Cache::tag('sensitive')->remember('sensitive_words', function() {
            return Db::name('sensitive_words')->where('status', 1)->column('word');
        }, 43200);
    }
    
    // 任务统计缓存（5分钟）
    public static function getTaskStats($userId)
    {
        $key = 'task_stats_' . $userId;
        return Cache::tag('stats')->remember($key, function() use ($userId) {
            return [
                'total' => Db::name('tasks')->where('user_id', $userId)->count(),
                'running' => Db::name('tasks')->where('user_id', $userId)->where('status', 2)->count(),
                'pending' => Db::name('tasks')->where('user_id', $userId)->where('status', 1)->count(),
            ];
        }, 300);
    }
    
    // 清除用户相关缓存
    public static function clearUserCache($userId)
    {
        Cache::tag('user')->clear();
        Cache::tag('stats')->clear();
        Cache::delete('user_info_' . $userId);
        Cache::delete('task_stats_' . $userId);
    }
}
```

### 3. 队列性能优化

#### 分级队列处理
```php
// app/service/QueueService.php
class QueueService
{
    // 高优先级队列（VIP用户）
    public static function pushHighPriority($taskData)
    {
        return Queue::push('app\\job\\HighPriorityTask', $taskData, 'high_priority');
    }
    
    // 普通优先级队列
    public static function pushNormal($taskData)
    {
        return Queue::push('app\\job\\NormalTask', $taskData, 'normal');
    }
    
    // 低优先级队列（批量任务）
    public static function pushLowPriority($taskData)
    {
        return Queue::push('app\\job\\BatchTask', $taskData, 'low_priority');
    }
    
    // 根据用户组自动分配队列
    public static function pushByUserGroup($userId, $taskData)
    {
        $user = CacheService::getUserInfo($userId);
        $userGroup = Db::name('user_groups')->where('id', $user['group_id'])->find();
        
        if ($userGroup['id'] >= 4) { // 元老用户和VIP用户
            return self::pushHighPriority($taskData);
        } elseif ($userGroup['id'] >= 2) { // 普通用户和高级用户
            return self::pushNormal($taskData);
        } else {
            return self::pushLowPriority($taskData);
        }
    }
}
```

### 4. 批量操作优化

#### 批量插入优化
```php
// app/service/TaskService.php
class TaskService
{
    // 批量添加任务优化
    public function batchAddTasks($userId, $tasks)
    {
        $successCount = 0;
        $failedTasks = [];
        $user = CacheService::getUserInfo($userId);
        
        // 分批处理，每次1000条
        $chunks = array_chunk($tasks, 1000);
        
        Db::transaction(function() use ($chunks, $userId, $user, &$successCount, &$failedTasks) {
            foreach ($chunks as $chunk) {
                $validTasks = [];
                
                foreach ($chunk as $taskData) {
                    // 验证任务数据
                    $validation = $this->validateTaskData($taskData, $user);
                    if ($validation['success']) {
                        $validTasks[] = $validation['data'];
                        $successCount++;
                    } else {
                        $failedTasks[] = [
                            'data' => $taskData,
                            'error' => $validation['error']
                        ];
                    }
                }
                
                // 批量插入有效任务
                if (!empty($validTasks)) {
                    Db::name('tasks')->insertAll($validTasks);
                }
            }
        });
        
        // 清除相关缓存
        CacheService::clearUserCache($userId);
        
        return [
            'success_count' => $successCount,
            'failed_tasks' => $failedTasks
        ];
    }
    
    // 任务数据验证
    private function validateTaskData($taskData, $user)
    {
        // 积分计算
        $requiredScore = $taskData['online_days'] * $user['score_cost'] * $taskData['daily_clicks'];
        
        // 检查积分余额
        if ($user['score'] < $requiredScore) {
            return [
                'success' => false,
                'error' => '积分余额不足'
            ];
        }
        
        // 敏感词检测
        if ($this->containsSensitiveWords($taskData['keyword'])) {
            return [
                'success' => false,
                'error' => '包含敏感词'
            ];
        }
        
        // 重复任务检测（仅关键词排名任务）
        if ($taskData['type'] == 1) {
            $exists = Db::name('tasks')
                ->where('user_id', $user['id'])
                ->where('keyword', $taskData['keyword'])
                ->where('url', $taskData['url'])
                ->where('status', 'in', [1, 2]) // 待审核或优化中
                ->find();
                
            if ($exists) {
                return [
                    'success' => false,
                    'error' => '任务已存在'
                ];
            }
        }
        
        return [
            'success' => true,
            'data' => array_merge($taskData, [
                'user_id' => $user['id'],
                'pre_deduct_score' => $requiredScore,
                'score_cost' => $user['score_cost'],
                'create_time' => time(),
                'update_time' => time()
            ])
        ];
    }
}
```

## 🔒 安全优化架构

### 1. 认证安全加强

#### JWT Token安全配置
```php
// config/jwt.php
return [
    'secret' => env('JWT_SECRET'), // 使用环境变量
    'ttl' => 7200, // 2小时过期
    'refresh_ttl' => 20160, // 14天刷新期
    'algo' => 'HS256',
    'required_claims' => [
        'iss', // 签发者
        'iat', // 签发时间
        'exp', // 过期时间
        'nbf', // 生效时间
        'sub', // 主题
        'jti'  // JWT ID
    ],
    'blacklist_enabled' => true, // 启用黑名单
    'blacklist_grace_period' => 0,
    'providers' => [
        'jwt' => 'app\\service\\JwtService',
        'auth' => 'app\\service\\AuthService',
        'storage' => 'app\\service\\JwtStorageService'
    ]
];
```

#### 多因素认证实现
```php
// app/service/AuthService.php
class AuthService
{
    // 设备指纹验证
    public function generateDeviceFingerprint($request)
    {
        $components = [
            $request->header('User-Agent'),
            $request->ip(),
            $request->header('Accept-Language'),
            $request->header('Accept-Encoding')
        ];
        
        return hash('sha256', implode('|', $components));
    }
    
    // 登录安全检查
    public function securityCheck($userId, $request)
    {
        $fingerprint = $this->generateDeviceFingerprint($request);
        $ip = $request->ip();
        
        // 检查设备指纹
        $lastFingerprint = Cache::get('device_fingerprint_' . $userId);
        if ($lastFingerprint && $lastFingerprint !== $fingerprint) {
            // 新设备登录，需要额外验证
            $this->sendSecurityAlert($userId, $ip, '新设备登录');
        }
        
        // 检查IP地理位置
        $lastIp = Cache::get('last_login_ip_' . $userId);
        if ($lastIp && $this->isIpLocationChanged($lastIp, $ip)) {
            $this->sendSecurityAlert($userId, $ip, '异地登录');
        }
        
        // 更新缓存
        Cache::set('device_fingerprint_' . $userId, $fingerprint, 86400 * 30);
        Cache::set('last_login_ip_' . $userId, $ip, 86400 * 30);
    }
    
    // 发送安全警报
    private function sendSecurityAlert($userId, $ip, $type)
    {
        $user = Db::name('users')->where('id', $userId)->find();
        
        // 记录安全日志
        Db::name('security_logs')->insert([
            'user_id' => $userId,
            'type' => $type,
            'ip' => $ip,
            'create_time' => time()
        ]);
        
        // 发送短信通知（可选）
        // SmsService::sendSecurityAlert($user['mobile'], $type, $ip);
    }
}
```

### 2. 数据安全防护

#### 敏感数据加密
```php
// app/service/EncryptService.php
class EncryptService
{
    private static $key;
    private static $iv;
    
    public static function init()
    {
        self::$key = config('app.encrypt_key');
        self::$iv = substr(hash('sha256', self::$key), 0, 16);
    }
    
    // 手机号加密存储
    public static function encryptMobile($mobile)
    {
        self::init();
        return base64_encode(openssl_encrypt($mobile, 'AES-256-CBC', self::$key, 0, self::$iv));
    }
    
    // 手机号解密
    public static function decryptMobile($encryptedMobile)
    {
        self::init();
        return openssl_decrypt(base64_decode($encryptedMobile), 'AES-256-CBC', self::$key, 0, self::$iv);
    }
    
    // 积分操作签名验证
    public static function generateScoreSignature($userId, $amount, $timestamp)
    {
        $data = $userId . $amount . $timestamp . config('app.secret_key');
        return hash('sha256', $data);
    }
    
    // 验证积分操作签名
    public static function verifyScoreSignature($userId, $amount, $timestamp, $signature)
    {
        $expectedSignature = self::generateScoreSignature($userId, $amount, $timestamp);
        return hash_equals($signature, $expectedSignature);
    }
}
```

#### SQL注入防护增强
```php
// app/service/DatabaseService.php
class DatabaseService
{
    // 安全的动态查询构建
    public static function buildSafeQuery($table, $conditions = [], $fields = '*')
    {
        $query = Db::name($table);
        
        // 白名单字段验证
        $allowedFields = self::getAllowedFields($table);
        $allowedOperators = ['=', '>', '<', '>=', '<=', 'like', 'in', 'not in'];
        
        foreach ($conditions as $condition) {
            if (!isset($condition['field'], $condition['operator'], $condition['value'])) {
                continue;
            }
            
            // 验证字段名
            if (!in_array($condition['field'], $allowedFields)) {
                throw new Exception('Invalid field: ' . $condition['field']);
            }
            
            // 验证操作符
            if (!in_array(strtolower($condition['operator']), $allowedOperators)) {
                throw new Exception('Invalid operator: ' . $condition['operator']);
            }
            
            $query->where($condition['field'], $condition['operator'], $condition['value']);
        }
        
        return $query;
    }
    
    // 获取表的允许字段
    private static function getAllowedFields($table)
    {
        $allowedFields = [
            'users' => ['id', 'mobile', 'nickname', 'group_id', 'score', 'status', 'create_time'],
            'tasks' => ['id', 'user_id', 'type', 'keyword', 'url', 'status', 'create_time'],
            'score_logs' => ['id', 'user_id', 'type', 'amount', 'create_time']
        ];
        
        return $allowedFields[$table] ?? [];
    }
}
```

### 3. 业务安全防护

#### 防刷机制实现
```php
// app/middleware/RateLimitMiddleware.php
class RateLimitMiddleware
{
    public function handle($request, \Closure $next)
    {
        $userId = $request->user['id'] ?? null;
        $ip = $request->ip();
        $action = $request->controller() . '/' . $request->action();
        
        // 用户级别限制
        if ($userId) {
            $this->checkUserRateLimit($userId, $action);
        }
        
        // IP级别限制
        $this->checkIpRateLimit($ip, $action);
        
        return $next($request);
    }
    
    // 用户频率限制
    private function checkUserRateLimit($userId, $action)
    {
        $limits = [
            'user/tasks' => ['max' => 100, 'window' => 3600], // 每小时100次
            'user/score' => ['max' => 50, 'window' => 3600],  // 每小时50次
            'user/login' => ['max' => 10, 'window' => 900],   // 15分钟10次
        ];
        
        $limit = $limits[$action] ?? ['max' => 60, 'window' => 60]; // 默认每分钟60次
        
        $key = "rate_limit:user:{$action}:{$userId}";
        $attempts = Cache::get($key, 0);
        
        if ($attempts >= $limit['max']) {
            throw new Exception('请求过于频繁，请稍后再试', 429);
        }
        
        Cache::set($key, $attempts + 1, $limit['window']);
    }
    
    // IP频率限制
    private function checkIpRateLimit($ip, $action)
    {
        $key = "rate_limit:ip:{$action}:{$ip}";
        $attempts = Cache::get($key, 0);
        
        if ($attempts >= 200) { // IP每小时200次请求
            throw new Exception('IP请求过于频繁', 429);
        }
        
        Cache::set($key, $attempts + 1, 3600);
    }
}
```

#### 异常操作监控
```php
// app/service/SecurityMonitorService.php
class SecurityMonitorService
{
    // 监控异常操作
    public static function monitorOperation($userId, $operation, $data = [])
    {
        $patterns = [
            'high_frequency' => self::detectHighFrequency($userId, $operation),
            'large_amount' => self::detectLargeAmount($operation, $data),
            'unusual_time' => self::detectUnusualTime(),
            'suspicious_pattern' => self::detectSuspiciousPattern($userId, $operation, $data)
        ];
        
        foreach ($patterns as $type => $detected) {
            if ($detected) {
                self::logSuspiciousActivity($userId, $operation, $type, $data);
                self::handleSuspiciousActivity($userId, $type);
            }
        }
    }
    
    // 检测高频操作
    private static function detectHighFrequency($userId, $operation)
    {
        $key = "operation_count:{$operation}:{$userId}";
        $count = Cache::get($key, 0);
        
        Cache::set($key, $count + 1, 300); // 5分钟窗口
        
        $thresholds = [
            'add_task' => 50,
            'score_operation' => 20,
            'login' => 10
        ];
        
        return $count > ($thresholds[$operation] ?? 30);
    }
    
    // 检测大额操作
    private static function detectLargeAmount($operation, $data)
    {
        if ($operation === 'score_operation' && isset($data['amount'])) {
            return $data['amount'] > 100000; // 超过10万积分
        }
        
        if ($operation === 'batch_add_task' && isset($data['count'])) {
            return $data['count'] > 1000; // 超过1000个任务
        }
        
        return false;
    }
    
    // 检测异常时间
    private static function detectUnusualTime()
    {
        $hour = date('H');
        return $hour >= 2 && $hour <= 5; // 凌晨2-5点
    }
    
    // 检测可疑模式
    private static function detectSuspiciousPattern($userId, $operation, $data)
    {
        // 检测是否在短时间内进行了多种高风险操作
        $riskOperations = Cache::get("risk_operations:{$userId}", []);
        $riskOperations[] = [
            'operation' => $operation,
            'time' => time(),
            'data' => $data
        ];
        
        // 保留最近1小时的操作记录
        $riskOperations = array_filter($riskOperations, function($op) {
            return time() - $op['time'] <= 3600;
        });
        
        Cache::set("risk_operations:{$userId}", $riskOperations, 3600);
        
        // 如果1小时内有超过5种不同的高风险操作，标记为可疑
        $operationTypes = array_unique(array_column($riskOperations, 'operation'));
        return count($operationTypes) > 5;
    }
    
    // 记录可疑活动
    private static function logSuspiciousActivity($userId, $operation, $type, $data)
    {
        Db::name('security_logs')->insert([
            'user_id' => $userId,
            'operation' => $operation,
            'risk_type' => $type,
            'data' => json_encode($data),
            'ip' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'create_time' => time()
        ]);
    }
    
    // 处理可疑活动
    private static function handleSuspiciousActivity($userId, $type)
    {
        switch ($type) {
            case 'high_frequency':
                // 临时限制操作频率
                Cache::set("temp_limit:{$userId}", true, 1800); // 30分钟限制
                break;
                
            case 'large_amount':
                // 需要额外验证
                Cache::set("require_verification:{$userId}", true, 3600);
                break;
                
            case 'unusual_time':
                // 发送安全提醒
                self::sendSecurityAlert($userId, '异常时间操作');
                break;
                
            case 'suspicious_pattern':
                // 临时锁定账户
                Db::name('users')->where('id', $userId)->update([
                    'status' => 2, // 临时锁定状态
                    'update_time' => time()
                ]);
                break;
        }
    }
}
```

## 📊 监控和日志系统

### 1. 性能监控

#### 慢查询监控
```php
// app/service/PerformanceMonitorService.php
class PerformanceMonitorService
{
    // 监控慢查询
    public static function logSlowQuery($sql, $time, $bindings = [])
    {
        if ($time > 1000) { // 超过1秒的查询
            Log::warning('Slow Query Detected', [
                'sql' => $sql,
                'time' => $time . 'ms',
                'bindings' => $bindings,
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5),
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true)
            ]);
            
            // 记录到数据库
            Db::name('performance_logs')->insert([
                'type' => 'slow_query',
                'sql' => $sql,
                'execution_time' => $time,
                'memory_usage' => memory_get_usage(true),
                'create_time' => time()
            ]);
        }
    }
    
    // 监控内存使用
    public static function monitorMemoryUsage()
    {
        $usage = memory_get_usage(true);
        $peak = memory_get_peak_usage(true);
        $limit = ini_get('memory_limit');
        
        if ($usage > 128 * 1024 * 1024) { // 超过128MB
            Log::warning('High Memory Usage', [
                'current' => self::formatBytes($usage),
                'peak' => self::formatBytes($peak),
                'limit' => $limit,
                'usage_percent' => round($usage / self::parseBytes($limit) * 100, 2) . '%'
            ]);
        }
    }
    
    // 监控API响应时间
    public static function monitorApiResponse($controller, $action, $responseTime)
    {
        $key = "api_response_time:{$controller}:{$action}";
        $times = Cache::get($key, []);
        $times[] = $responseTime;
        
        // 保留最近100次记录
        if (count($times) > 100) {
            $times = array_slice($times, -100);
        }
        
        Cache::set($key, $times, 3600);
        
        // 计算平均响应时间
        $avgTime = array_sum($times) / count($times);
        
        if ($avgTime > 2000) { // 平均响应时间超过2秒
            Log::warning('Slow API Response', [
                'controller' => $controller,
                'action' => $action,
                'current_time' => $responseTime,
                'average_time' => $avgTime,
                'sample_count' => count($times)
            ]);
        }
    }
    
    // 格式化字节数
    private static function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $i = 0;
        while ($bytes >= 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }
    
    // 解析字节数
    private static function parseBytes($val)
    {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = (int) $val;
        switch($last) {
            case 'g': $val *= 1024;
            case 'm': $val *= 1024;
            case 'k': $val *= 1024;
        }
        return $val;
    }
}
```

### 2. 系统配置优化

#### 环境变量安全配置
```env
# .env 安全配置示例
APP_DEBUG=false
APP_ENCRYPT_KEY=your-32-character-secret-key-here
JWT_SECRET=your-jwt-secret-key-here
DB_PASSWORD=your-strong-database-password
REDIS_PASSWORD=your-strong-redis-password

# 安全头配置
SECURITY_HEADERS=true
CSRF_PROTECTION=true
XSS_PROTECTION=true

# 文件上传限制
MAX_UPLOAD_SIZE=10M
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif

# 日志配置
LOG_LEVEL=warning
LOG_MAX_FILES=30
```

#### Nginx安全配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    root /path/to/project/public;
    index index.php;
    
    # SSL配置
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 安全头设置
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';" always;
    
    # 限制请求大小
    client_max_body_size 10M;
    client_body_buffer_size 128k;
    
    # 隐藏服务器信息
    server_tokens off;
    
    # 限制请求方法
    if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE)$ ) {
        return 405;
    }
    
    # 防止访问敏感文件
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(env|log|ini)$ {
        deny all;
    }
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 安全参数
        fastcgi_hide_header X-Powered-By;
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 主要路由
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # 访问日志
    access_log /var/log/nginx/seo_access.log;
    error_log /var/log/nginx/seo_error.log;
}
```

---

**注意**: 这是完善版3.1文档，包含了性能优化和安全优化的核心内容。完善版3.2将包含具体的功能模块实现、数据库设计和API接口等内容。