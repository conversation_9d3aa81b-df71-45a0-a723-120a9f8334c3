<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\service\TaskExecutionService;
use app\middleware\IpBlacklistMiddleware;

/**
 * 数据清理命令
 */
class DataCleaner extends Command
{
    protected function configure()
    {
        $this->setName('data:clean')
            ->setDescription('清理过期数据');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始清理过期数据...');
        
        try {
            $totalCleaned = 0;
            
            // 清理过期的任务执行日志
            $count = $this->cleanTaskExecutionLogs($output);
            $totalCleaned += $count;
            
            // 清理过期的安全日志
            $count = $this->cleanSecurityLogs($output);
            $totalCleaned += $count;
            
            // 清理过期的IP黑名单
            $count = $this->cleanIpBlacklist($output);
            $totalCleaned += $count;
            
            // 清理过期的JWT黑名单
            $count = $this->cleanJwtBlacklist($output);
            $totalCleaned += $count;
            
            // 清理过期的缓存
            $this->cleanExpiredCache($output);
            
            // 优化数据库表
            $this->optimizeTables($output);
            
            $output->writeln("数据清理完成，共清理 {$totalCleaned} 条记录");
            
        } catch (\Exception $e) {
            $output->writeln('数据清理失败：' . $e->getMessage());
        }
    }
    
    /**
     * 清理任务执行日志
     */
    private function cleanTaskExecutionLogs(Output $output)
    {
        $output->writeln('清理任务执行日志...');
        
        // 保留30天的日志
        $count = TaskExecutionService::cleanExpiredLogs(30);
        
        $output->writeln("清理了 {$count} 条任务执行日志");
        
        return $count;
    }
    
    /**
     * 清理安全日志
     */
    private function cleanSecurityLogs(Output $output)
    {
        $output->writeln('清理安全日志...');
        
        // 保留60天的安全日志
        $expireTime = time() - (60 * 24 * 3600);
        
        $count = Db::name('security_logs')
            ->where('create_time', '<', $expireTime)
            ->delete();
        
        $output->writeln("清理了 {$count} 条安全日志");
        
        return $count;
    }
    
    /**
     * 清理IP黑名单
     */
    private function cleanIpBlacklist(Output $output)
    {
        $output->writeln('清理过期IP黑名单...');
        
        $count = IpBlacklistMiddleware::cleanExpiredBlacklist();
        
        $output->writeln("清理了 {$count} 条过期IP黑名单记录");
        
        return $count;
    }
    
    /**
     * 清理JWT黑名单
     */
    private function cleanJwtBlacklist(Output $output)
    {
        $output->writeln('清理过期JWT黑名单...');
        
        // 清理过期的JWT令牌黑名单
        $expireTime = time();
        
        $count = Db::name('jwt_blacklist')
            ->where('expire_time', '<', $expireTime)
            ->delete();
        
        $output->writeln("清理了 {$count} 条过期JWT黑名单记录");
        
        return $count;
    }
    
    /**
     * 清理过期缓存
     */
    private function cleanExpiredCache(Output $output)
    {
        $output->writeln('清理过期缓存...');
        
        try {
            // 清理Redis过期键（Redis会自动清理，这里主要是手动触发）
            $redis = cache()->handler();
            if (method_exists($redis, 'flushExpired')) {
                $redis->flushExpired();
            }
            
            $output->writeln('缓存清理完成');
        } catch (\Exception $e) {
            $output->writeln('缓存清理失败：' . $e->getMessage());
        }
    }
    
    /**
     * 优化数据库表
     */
    private function optimizeTables(Output $output)
    {
        $output->writeln('优化数据库表...');
        
        $tables = [
            'tasks',
            'task_execution_logs',
            'score_logs',
            'security_logs',
            'users',
            'admins'
        ];
        
        foreach ($tables as $table) {
            try {
                Db::execute("OPTIMIZE TABLE {$table}");
                $output->writeln("优化表 {$table} 完成");
            } catch (\Exception $e) {
                $output->writeln("优化表 {$table} 失败：" . $e->getMessage());
            }
        }
    }
    
    /**
     * 清理临时文件
     */
    private function cleanTempFiles(Output $output)
    {
        $output->writeln('清理临时文件...');
        
        $tempDirs = [
            runtime_path() . 'temp/',
            runtime_path() . 'cache/',
            runtime_path() . 'log/'
        ];
        
        $totalSize = 0;
        $totalFiles = 0;
        
        foreach ($tempDirs as $dir) {
            if (is_dir($dir)) {
                $result = $this->cleanDirectory($dir, 7); // 清理7天前的文件
                $totalSize += $result['size'];
                $totalFiles += $result['files'];
            }
        }
        
        $output->writeln("清理了 {$totalFiles} 个临时文件，释放空间 " . $this->formatBytes($totalSize));
    }
    
    /**
     * 清理目录中的过期文件
     */
    private function cleanDirectory($dir, $days)
    {
        $expireTime = time() - ($days * 24 * 3600);
        $totalSize = 0;
        $totalFiles = 0;
        
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getMTime() < $expireTime) {
                $size = $file->getSize();
                if (unlink($file->getPathname())) {
                    $totalSize += $size;
                    $totalFiles++;
                }
            }
        }
        
        return [
            'size' => $totalSize,
            'files' => $totalFiles
        ];
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * 生成清理报告
     */
    private function generateCleanReport(Output $output)
    {
        $output->writeln('生成清理报告...');
        
        $report = [
            'clean_time' => date('Y-m-d H:i:s'),
            'database_stats' => $this->getDatabaseStats(),
            'cache_stats' => $this->getCacheStats(),
            'disk_stats' => $this->getDiskStats()
        ];
        
        $reportFile = runtime_path() . 'clean_report_' . date('Ymd_His') . '.json';
        file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        $output->writeln("清理报告已保存到: {$reportFile}");
    }
    
    /**
     * 获取数据库统计信息
     */
    private function getDatabaseStats()
    {
        $stats = [];
        
        $tables = [
            'tasks',
            'task_execution_logs',
            'score_logs',
            'security_logs',
            'users'
        ];
        
        foreach ($tables as $table) {
            try {
                $count = Db::name($table)->count();
                $stats[$table] = $count;
            } catch (\Exception $e) {
                $stats[$table] = 'error: ' . $e->getMessage();
            }
        }
        
        return $stats;
    }
    
    /**
     * 获取缓存统计信息
     */
    private function getCacheStats()
    {
        try {
            $redis = cache()->handler();
            
            if (method_exists($redis, 'info')) {
                $info = $redis->info();
                return [
                    'used_memory' => $info['used_memory_human'] ?? 'unknown',
                    'connected_clients' => $info['connected_clients'] ?? 'unknown',
                    'total_commands_processed' => $info['total_commands_processed'] ?? 'unknown'
                ];
            }
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
        
        return ['status' => 'unavailable'];
    }
    
    /**
     * 获取磁盘统计信息
     */
    private function getDiskStats()
    {
        $path = root_path();
        
        return [
            'total_space' => $this->formatBytes(disk_total_space($path)),
            'free_space' => $this->formatBytes(disk_free_space($path)),
            'used_space' => $this->formatBytes(disk_total_space($path) - disk_free_space($path))
        ];
    }
}
