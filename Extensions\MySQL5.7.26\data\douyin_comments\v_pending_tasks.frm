TYPE=VIEW
query=select `t`.`id` AS `id`,`t`.`account_id` AS `account_id`,`t`.`video_id` AS `video_id`,`t`.`video_title` AS `video_title`,`t`.`collection_limit` AS `collection_limit`,`t`.`task_status` AS `task_status`,`t`.`scheduled_start_time` AS `scheduled_start_time`,`t`.`high_intent_keywords` AS `high_intent_keywords`,`t`.`filter_author_replies` AS `filter_author_replies`,`t`.`collection_interval_minutes` AS `collection_interval_minutes`,`t`.`last_collection_time` AS `last_collection_time`,`t`.`next_collection_time` AS `next_collection_time`,`t`.`created_time` AS `created_time`,`t`.`updated_time` AS `updated_time`,`t`.`remarks` AS `remarks`,`a`.`account_name` AS `account_name`,`a`.`sec_uid` AS `account_sec_uid` from (`douyin_comments`.`dy_monitor_tasks` `t` join `douyin_comments`.`dy_accounts` `a` on((`t`.`account_id` = `a`.`id`))) where ((`t`.`task_status` = \'active\') and (`a`.`status` = \'active\') and (isnull(`t`.`next_collection_time`) or (`t`.`next_collection_time` <= now()))) order by `t`.`next_collection_time`,`t`.`created_time`
md5=3afebf2941ed892a9367e139bcad4299
updatable=1
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=2025-06-25 12:11:06
create-version=1
source=SELECT
\n    t.*,
\n    a.account_name,
\n    a.sec_uid as account_sec_uid
\nFROM dy_monitor_tasks t
\nJOIN dy_accounts a ON t.account_id = a.id
\nWHERE t.task_status = \'active\'
\n  AND a.status = \'active\'
\n  AND (t.next_collection_time IS NULL OR t.next_collection_time <= NOW())
\nORDER BY t.next_collection_time ASC, t.created_time ASC
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_general_ci
view_body_utf8=select `t`.`id` AS `id`,`t`.`account_id` AS `account_id`,`t`.`video_id` AS `video_id`,`t`.`video_title` AS `video_title`,`t`.`collection_limit` AS `collection_limit`,`t`.`task_status` AS `task_status`,`t`.`scheduled_start_time` AS `scheduled_start_time`,`t`.`high_intent_keywords` AS `high_intent_keywords`,`t`.`filter_author_replies` AS `filter_author_replies`,`t`.`collection_interval_minutes` AS `collection_interval_minutes`,`t`.`last_collection_time` AS `last_collection_time`,`t`.`next_collection_time` AS `next_collection_time`,`t`.`created_time` AS `created_time`,`t`.`updated_time` AS `updated_time`,`t`.`remarks` AS `remarks`,`a`.`account_name` AS `account_name`,`a`.`sec_uid` AS `account_sec_uid` from (`douyin_comments`.`dy_monitor_tasks` `t` join `douyin_comments`.`dy_accounts` `a` on((`t`.`account_id` = `a`.`id`))) where ((`t`.`task_status` = \'active\') and (`a`.`status` = \'active\') and (isnull(`t`.`next_collection_time`) or (`t`.`next_collection_time` <= now()))) order by `t`.`next_collection_time`,`t`.`created_time`
