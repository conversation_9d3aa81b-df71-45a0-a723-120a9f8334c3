<?php

namespace app\controller\api;

use think\Request;
use think\exception\ValidateException;
use app\service\UserService;
use app\service\JwtService;
use app\service\SecurityService;
use app\validate\UserValidate;

/**
 * 用户认证控制器
 */
class AuthController extends BaseController
{
    /**
     * 用户注册
     */
    public function register(Request $request)
    {
        try {
            $data = $request->post();
            
            // 验证数据
            $validate = new UserValidate();
            if (!$validate->scene('register')->check($data)) {
                return $this->error($validate->getError());
            }
            
            // 检查频率限制
            $rateLimitKey = 'register:' . $request->ip();
            if (!SecurityService::checkRateLimit($rateLimitKey, 5, 3600)) {
                return $this->error('注册过于频繁，请稍后再试', 429);
            }
            
            // 注册用户
            $userId = UserService::register(
                $data['mobile'],
                $data['password'],
                $data['nickname'] ?? null
            );
            
            // 自动登录
            $loginResult = UserService::login($data['mobile'], $data['password']);
            
            return $this->success([
                'user_id' => $userId,
                'token' => $loginResult['token'],
                'user_info' => $loginResult['user_info']
            ], '注册成功');
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('注册失败：' . $e->getMessage());
        }
    }
    
    /**
     * 用户登录
     */
    public function login(Request $request)
    {
        try {
            $data = $request->post();
            
            // 验证数据
            $validate = new UserValidate();
            if (!$validate->scene('login')->check($data)) {
                return $this->error($validate->getError());
            }
            
            // 检查登录失败次数
            $ip = $request->ip();
            $failureCount = SecurityService::checkLoginFailureLimit($ip);
            
            if ($failureCount >= 5) {
                return $this->error('登录失败次数过多，请1小时后再试', 429);
            }
            
            // 用户登录
            $result = UserService::login(
                $data['mobile'],
                $data['password'],
                $data['remember'] ?? false
            );
            
            return $this->success($result, '登录成功');
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('登录失败：' . $e->getMessage());
        }
    }
    
    /**
     * 刷新Token
     */
    public function refresh(Request $request)
    {
        try {
            $token = $request->post('token') ?: $request->header('Authorization');
            
            if (empty($token)) {
                return $this->error('缺少Token');
            }
            
            // 移除Bearer前缀
            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }
            
            $newToken = JwtService::refreshToken($token);
            
            return $this->success([
                'token' => $newToken
            ], 'Token刷新成功');
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('Token刷新失败');
        }
    }
    
    /**
     * 用户登出
     */
    public function logout(Request $request)
    {
        try {
            $token = $request->token ?? null;
            
            if ($token) {
                // 撤销Token
                JwtService::revokeToken($token);
                
                // 记录登出日志
                if (isset($request->user)) {
                    SecurityService::logUserAction($request->user['id'], 'logout', [
                        'ip' => $request->ip()
                    ]);
                }
            }
            
            return $this->success(null, '登出成功');
            
        } catch (\Exception $e) {
            return $this->success(null, '登出成功'); // 即使失败也返回成功
        }
    }
    
    /**
     * 获取当前用户信息
     */
    public function profile(Request $request)
    {
        try {
            $user = $request->user;
            
            if (!$user) {
                return $this->error('用户信息获取失败');
            }
            
            // 获取完整的用户信息
            $userInfo = UserService::getUserById($user['id']);
            
            return $this->success($userInfo);
            
        } catch (\Exception $e) {
            return $this->error('获取用户信息失败');
        }
    }
    
    /**
     * 更新用户信息
     */
    public function updateProfile(Request $request)
    {
        try {
            $user = $request->user;
            $data = $request->post();
            
            // 验证数据
            $validate = new UserValidate();
            if (!$validate->scene('update')->check($data)) {
                return $this->error($validate->getError());
            }
            
            // 更新用户信息
            $result = UserService::updateUser($user['id'], $data);
            
            if ($result) {
                return $this->success(null, '更新成功');
            } else {
                return $this->error('更新失败');
            }
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('更新失败：' . $e->getMessage());
        }
    }
    
    /**
     * 修改密码
     */
    public function changePassword(Request $request)
    {
        try {
            $user = $request->user;
            $data = $request->post();
            
            // 验证数据
            $validate = new UserValidate();
            if (!$validate->scene('changePassword')->check($data)) {
                return $this->error($validate->getError());
            }
            
            // 修改密码
            $result = UserService::changePassword(
                $user['id'],
                $data['old_password'],
                $data['new_password']
            );
            
            if ($result) {
                // 撤销当前Token，要求重新登录
                if ($request->token) {
                    JwtService::revokeToken($request->token);
                }
                
                return $this->success(null, '密码修改成功，请重新登录');
            } else {
                return $this->error('密码修改失败');
            }
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('密码修改失败：' . $e->getMessage());
        }
    }
    
    /**
     * 检查Token有效性
     */
    public function checkToken(Request $request)
    {
        try {
            $token = $request->post('token') ?: $request->header('Authorization');
            
            if (empty($token)) {
                return $this->error('缺少Token');
            }
            
            // 移除Bearer前缀
            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }
            
            $payload = JwtService::validateToken($token);
            
            return $this->success([
                'valid' => true,
                'expires_at' => $payload['exp'],
                'user_data' => $payload['data']
            ], 'Token有效');
            
        } catch (ValidateException $e) {
            return $this->success([
                'valid' => false,
                'error' => $e->getMessage()
            ], 'Token无效');
        } catch (\Exception $e) {
            return $this->success([
                'valid' => false,
                'error' => 'Token验证失败'
            ], 'Token无效');
        }
    }
}
