<VirtualHost _default_:80>
    DocumentRoot "D:/phpstudy_pro/WWW/public"
    FcgidInitialEnv PHPRC "D:/phpstudy_pro/Extensions/php/php8.0.2nts"
    AddHandler fcgid-script .php
    FcgidWrapper "D:/phpstudy_pro/Extensions/php/php8.0.2nts/php-cgi.exe" .php
  <Directory "D:/phpstudy_pro/WWW/public">
      Options FollowSymLinks ExecCGI
      AllowOverride All
      Order allow,deny
      Allow from all
      Require all granted
	  DirectoryIndex index.php index.html
  </Directory>
  ErrorDocument 400 /error/400.html
  ErrorDocument 403 /error/403.html
  ErrorDocument 404 /error/404.html
  ErrorDocument 500 /error/500.html
  ErrorDocument 501 /error/501.html
  ErrorDocument 502 /error/502.html
  ErrorDocument 503 /error/503.html
  ErrorDocument 504 /error/504.html
  ErrorDocument 505 /error/505.html
  ErrorDocument 506 /error/506.html
  ErrorDocument 507 /error/507.html
  ErrorDocument 510 /error/510.html
</VirtualHost>
