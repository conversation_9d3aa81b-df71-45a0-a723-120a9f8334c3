<?php

namespace app\middleware;

use think\Response;
use app\service\SecurityService;

/**
 * IP黑名单中间件
 */
class IpBlacklistMiddleware
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        $ip = $request->ip();
        
        // 检查IP是否在黑名单中
        if ($this->isBlacklisted($ip)) {
            return json([
                'code' => 403,
                'message' => '访问被拒绝',
                'data' => null,
                'timestamp' => time()
            ], 403);
        }
        
        // 检查IP访问频率
        if ($this->isFrequencyExceeded($ip)) {
            // 记录异常访问
            SecurityService::logUserAction(0, 'ip_frequency_exceeded', [
                'ip' => $ip,
                'user_agent' => $request->header('User-Agent'),
                'url' => $request->url(),
                'method' => $request->method()
            ]);
            
            return json([
                'code' => 429,
                'message' => '访问过于频繁，请稍后再试',
                'data' => null,
                'timestamp' => time()
            ], 429);
        }
        
        // 记录IP访问
        $this->recordIpAccess($ip, $request);
        
        return $next($request);
    }
    
    /**
     * 检查IP是否在黑名单中
     */
    private function isBlacklisted($ip)
    {
        // 从缓存中获取黑名单
        $blacklist = cache('ip_blacklist');
        
        if (!$blacklist) {
            // 从数据库加载黑名单
            $blacklist = $this->loadBlacklistFromDb();
            cache('ip_blacklist', $blacklist, 300); // 缓存5分钟
        }
        
        return in_array($ip, $blacklist);
    }
    
    /**
     * 从数据库加载黑名单
     */
    private function loadBlacklistFromDb()
    {
        $blacklist = \think\facade\Db::name('ip_blacklist')
            ->where('status', 1)
            ->where('expire_time', '>', time())
            ->column('ip');
        
        return $blacklist ?: [];
    }
    
    /**
     * 检查IP访问频率是否超限
     */
    private function isFrequencyExceeded($ip)
    {
        $key = 'ip_access_count:' . $ip;
        $count = cache($key) ?: 0;
        
        // 每分钟最多100次请求
        $maxRequests = config('system.max_requests_per_minute', 100);
        
        if ($count >= $maxRequests) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 记录IP访问
     */
    private function recordIpAccess($ip, $request)
    {
        $key = 'ip_access_count:' . $ip;
        $count = cache($key) ?: 0;
        
        // 增加访问计数
        cache($key, $count + 1, 60); // 1分钟过期
        
        // 记录访问日志（可选，避免日志过多）
        if ($count % 50 == 0) { // 每50次访问记录一次
            SecurityService::logUserAction(0, 'ip_access', [
                'ip' => $ip,
                'count' => $count + 1,
                'user_agent' => $request->header('User-Agent'),
                'url' => $request->url()
            ]);
        }
    }
    
    /**
     * 添加IP到黑名单
     */
    public static function addToBlacklist($ip, $reason = '', $expireTime = null)
    {
        if (!$expireTime) {
            $expireTime = time() + 86400; // 默认24小时
        }
        
        $data = [
            'ip' => $ip,
            'reason' => $reason,
            'status' => 1,
            'expire_time' => $expireTime,
            'create_time' => time()
        ];
        
        // 检查是否已存在
        $exists = \think\facade\Db::name('ip_blacklist')
            ->where('ip', $ip)
            ->find();
        
        if ($exists) {
            // 更新现有记录
            \think\facade\Db::name('ip_blacklist')
                ->where('ip', $ip)
                ->update([
                    'reason' => $reason,
                    'status' => 1,
                    'expire_time' => $expireTime,
                    'update_time' => time()
                ]);
        } else {
            // 插入新记录
            \think\facade\Db::name('ip_blacklist')->insert($data);
        }
        
        // 清除缓存
        cache('ip_blacklist', null);
        
        return true;
    }
    
    /**
     * 从黑名单中移除IP
     */
    public static function removeFromBlacklist($ip)
    {
        $result = \think\facade\Db::name('ip_blacklist')
            ->where('ip', $ip)
            ->update(['status' => 0, 'update_time' => time()]);
        
        // 清除缓存
        cache('ip_blacklist', null);
        
        return $result;
    }
    
    /**
     * 检查并自动加入黑名单
     */
    public static function checkAndAutoBlock($ip)
    {
        // 检查最近1小时内的异常行为
        $hourAgo = time() - 3600;
        
        $suspiciousCount = \think\facade\Db::name('security_logs')
            ->where('ip', $ip)
            ->where('create_time', '>=', $hourAgo)
            ->where('action', 'in', [
                'login_failed',
                'admin_login_failed',
                'ip_frequency_exceeded',
                'suspicious_activity'
            ])
            ->count();
        
        // 如果异常行为超过阈值，自动加入黑名单
        $threshold = config('system.auto_block_threshold', 20);
        
        if ($suspiciousCount >= $threshold) {
            self::addToBlacklist($ip, "自动封禁：1小时内异常行为{$suspiciousCount}次", time() + 3600);
            
            // 记录自动封禁日志
            SecurityService::logUserAction(0, 'auto_ip_block', [
                'ip' => $ip,
                'suspicious_count' => $suspiciousCount,
                'threshold' => $threshold
            ]);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取IP地理位置信息（可选功能）
     */
    private function getIpLocation($ip)
    {
        // 这里可以集成第三方IP地理位置服务
        // 如：高德、百度、腾讯等API
        
        // 简单的内网IP检测
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            // 公网IP，可以查询地理位置
            return [
                'country' => '未知',
                'province' => '未知',
                'city' => '未知',
                'isp' => '未知'
            ];
        } else {
            // 内网IP
            return [
                'country' => '本地',
                'province' => '本地',
                'city' => '本地',
                'isp' => '内网'
            ];
        }
    }
    
    /**
     * 清理过期的黑名单记录
     */
    public static function cleanExpiredBlacklist()
    {
        $count = \think\facade\Db::name('ip_blacklist')
            ->where('expire_time', '<', time())
            ->where('status', 1)
            ->update(['status' => 0, 'update_time' => time()]);
        
        if ($count > 0) {
            // 清除缓存
            cache('ip_blacklist', null);
        }
        
        return $count;
    }
    
    /**
     * 获取黑名单统计
     */
    public static function getBlacklistStats()
    {
        $total = \think\facade\Db::name('ip_blacklist')->count();
        $active = \think\facade\Db::name('ip_blacklist')
            ->where('status', 1)
            ->where('expire_time', '>', time())
            ->count();
        $expired = \think\facade\Db::name('ip_blacklist')
            ->where('expire_time', '<=', time())
            ->count();
        
        return [
            'total' => $total,
            'active' => $active,
            'expired' => $expired
        ];
    }
}
