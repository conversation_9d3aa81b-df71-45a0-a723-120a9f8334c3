@echo off
chcp 65001 >nul
echo ========================================
echo SEO积分系统 - 数据库导入工具
echo ========================================
echo.

:: 设置数据库连接参数
set DB_HOST=127.0.0.1
set DB_PORT=3306
set DB_NAME=seo_points_system
set DB_USER=root

:: 提示用户输入密码
set /p DB_PASSWORD="请输入MySQL root密码: "

echo.
echo [信息] 开始导入数据库...
echo [信息] 数据库: %DB_NAME%
echo [信息] 主机: %DB_HOST%:%DB_PORT%
echo [信息] 用户: %DB_USER%
echo.

:: 检查MySQL是否可用
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] MySQL客户端未安装或未添加到PATH环境变量
    echo 请确保MySQL已安装并添加到系统PATH
    pause
    exit /b 1
)

:: 导入数据库
echo [步骤1] 导入数据库结构和数据...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% < init_database.sql

if %errorlevel% equ 0 (
    echo [成功] 数据库导入完成！
    echo.
    echo ========================================
    echo 数据库信息
    echo ========================================
    echo 数据库名: %DB_NAME%
    echo 管理员账号: admin / admin123456
    echo 测试用户: 13800138000 / password123
    echo.
    echo 接下来请：
    echo 1. 配置 .env 文件中的数据库连接信息
    echo 2. 重启Nginx服务
    echo 3. 访问 http://localhost/ 测试系统
    echo ========================================
) else (
    echo [错误] 数据库导入失败！
    echo 请检查：
    echo 1. MySQL服务是否已启动
    echo 2. 用户名和密码是否正确
    echo 3. 是否有创建数据库的权限
)

echo.
pause
