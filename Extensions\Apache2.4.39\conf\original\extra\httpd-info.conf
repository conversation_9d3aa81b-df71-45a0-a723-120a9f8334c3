#
# Get information about the requests being processed by the server
# and the configuration of the server.
#
# Required modules: mod_authz_core, mod_authz_host,
#                   mod_info (for the server-info handler),
#                   mod_status (for the server-status handler)

#
# Allow server status reports generated by mod_status,
# with the URL of http://servername/server-status
# Change the ".example.com" to match your domain to enable.

<Location /server-status>
    SetHandler server-status
    Require host .example.com
    Require ip 127
</Location>

#
# ExtendedStatus controls whether Apache will generate "full" status
# information (ExtendedStatus On) or just basic information (ExtendedStatus
# Off) when the "server-status" handler is called. The default is Off.
#
#ExtendedStatus On

#
# Allow remote server configuration reports, with the URL of
#  http://servername/server-info (requires that mod_info.c be loaded).
# Change the ".example.com" to match your domain to enable.
#
<Location /server-info>
    SetHandler server-info
    Require host .example.com
    Require ip 127
</Location>
