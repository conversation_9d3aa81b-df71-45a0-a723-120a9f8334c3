<?php

namespace app\service;

use think\facade\Db;
use think\facade\Cache;
use think\exception\ValidateException;
use app\service\UserService;

/**
 * 积分服务类
 */
class ScoreService
{
    // 积分操作类型
    const TYPE_RECHARGE = 1;    // 充值
    const TYPE_CONSUME = 2;     // 消费
    const TYPE_REFUND = 3;      // 退款
    const TYPE_REWARD = 4;      // 奖励
    const TYPE_DEDUCT = 5;      // 扣除
    
    /**
     * 添加积分记录
     */
    public static function addLog($userId, $taskId, $type, $amount, $description, $adminId = null)
    {
        // 开启事务
        Db::startTrans();
        try {
            // 获取用户当前余额
            $user = Db::name('users')->where('id', $userId)->lock(true)->find();
            if (!$user) {
                throw new ValidateException('用户不存在');
            }
            
            $balanceBefore = $user['score'];
            $balanceAfter = $balanceBefore + $amount;
            
            // 检查余额是否足够（扣除操作）
            if ($amount < 0 && $balanceAfter < 0) {
                throw new ValidateException('积分余额不足');
            }
            
            // 插入积分记录
            $logId = Db::name('score_logs')->insertGetId([
                'user_id' => $userId,
                'task_id' => $taskId,
                'type' => $type,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description,
                'admin_id' => $adminId,
                'create_time' => time()
            ]);
            
            // 更新用户积分余额
            $updateData = ['score' => $balanceAfter, 'update_time' => time()];
            
            // 更新累计充值和消费
            if ($type == self::TYPE_RECHARGE && $amount > 0) {
                $updateData['total_recharge'] = Db::raw('total_recharge + ' . $amount);
            } elseif (in_array($type, [self::TYPE_CONSUME, self::TYPE_DEDUCT]) && $amount < 0) {
                $updateData['total_consumption'] = Db::raw('total_consumption + ' . abs($amount));
                $updateData['today_consumption'] = Db::raw('today_consumption + ' . abs($amount));
            }
            
            Db::name('users')->where('id', $userId)->update($updateData);
            
            Db::commit();
            
            // 清除用户缓存
            self::clearUserScoreCache($userId);
            
            // 检查用户组升级
            if (in_array($type, [self::TYPE_RECHARGE, self::TYPE_CONSUME])) {
                UserService::checkGroupUpgrade($userId);
            }
            
            return $logId;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 充值积分
     */
    public static function recharge($userId, $amount, $description = '积分充值', $adminId = null)
    {
        if ($amount <= 0) {
            throw new ValidateException('充值金额必须大于0');
        }
        
        return self::addLog($userId, null, self::TYPE_RECHARGE, $amount, $description, $adminId);
    }
    
    /**
     * 消费积分
     */
    public static function consume($userId, $taskId, $amount, $description = '任务消费')
    {
        if ($amount <= 0) {
            throw new ValidateException('消费金额必须大于0');
        }
        
        return self::addLog($userId, $taskId, self::TYPE_CONSUME, -$amount, $description);
    }
    
    /**
     * 退款积分
     */
    public static function refund($userId, $taskId, $amount, $description = '任务退款', $adminId = null)
    {
        if ($amount <= 0) {
            throw new ValidateException('退款金额必须大于0');
        }
        
        return self::addLog($userId, $taskId, self::TYPE_REFUND, $amount, $description, $adminId);
    }
    
    /**
     * 奖励积分
     */
    public static function reward($userId, $amount, $description = '系统奖励', $adminId = null)
    {
        if ($amount <= 0) {
            throw new ValidateException('奖励金额必须大于0');
        }
        
        return self::addLog($userId, null, self::TYPE_REWARD, $amount, $description, $adminId);
    }
    
    /**
     * 扣除积分
     */
    public static function deduct($userId, $amount, $description = '系统扣除', $adminId = null)
    {
        if ($amount <= 0) {
            throw new ValidateException('扣除金额必须大于0');
        }
        
        return self::addLog($userId, null, self::TYPE_DEDUCT, -$amount, $description, $adminId);
    }
    
    /**
     * 获取用户积分余额
     */
    public static function getUserScore($userId)
    {
        $cacheKey = "user:score:{$userId}";
        
        return Cache::remember($cacheKey, function() use ($userId) {
            $user = Db::name('users')->where('id', $userId)->field('score')->find();
            return $user ? $user['score'] : 0;
        }, 300); // 缓存5分钟
    }
    
    /**
     * 获取用户积分记录
     */
    public static function getUserScoreLogs($userId, $page = 1, $limit = 20, $type = null)
    {
        $where = [['user_id', '=', $userId]];
        
        if ($type !== null) {
            $where[] = ['type', '=', $type];
        }
        
        $query = Db::name('score_logs')
            ->alias('sl')
            ->leftJoin('tasks t', 'sl.task_id = t.id')
            ->field('sl.*, t.keyword, t.type as task_type')
            ->where($where)
            ->order('sl.create_time', 'desc');
        
        $total = $query->count();
        $list = $query->page($page, $limit)->select();
        
        // 格式化数据
        foreach ($list as &$item) {
            $item['type_text'] = self::getTypeText($item['type']);
            $item['amount_text'] = ($item['amount'] > 0 ? '+' : '') . $item['amount'];
            $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
        }
        
        return [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 获取积分统计
     */
    public static function getScoreStats($userId, $days = 30)
    {
        $startTime = strtotime("-{$days} days");
        
        // 获取指定天数内的积分记录统计
        $stats = Db::name('score_logs')
            ->where('user_id', $userId)
            ->where('create_time', '>=', $startTime)
            ->field([
                'type',
                'SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as income',
                'SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as expense',
                'COUNT(*) as count'
            ])
            ->group('type')
            ->select();
        
        $result = [
            'total_income' => 0,
            'total_expense' => 0,
            'total_count' => 0,
            'by_type' => []
        ];
        
        foreach ($stats as $stat) {
            $result['total_income'] += $stat['income'];
            $result['total_expense'] += $stat['expense'];
            $result['total_count'] += $stat['count'];
            
            $result['by_type'][$stat['type']] = [
                'type' => $stat['type'],
                'type_text' => self::getTypeText($stat['type']),
                'income' => $stat['income'],
                'expense' => $stat['expense'],
                'count' => $stat['count']
            ];
        }
        
        return $result;
    }
    
    /**
     * 批量处理积分退款
     */
    public static function batchRefund($refunds)
    {
        if (empty($refunds)) {
            return [];
        }
        
        $results = [];
        
        Db::startTrans();
        try {
            foreach ($refunds as $refund) {
                $userId = $refund['user_id'];
                $taskId = $refund['task_id'];
                $amount = $refund['amount'];
                $description = $refund['description'] ?? '批量退款';
                $adminId = $refund['admin_id'] ?? null;
                
                $logId = self::refund($userId, $taskId, $amount, $description, $adminId);
                $results[] = [
                    'user_id' => $userId,
                    'task_id' => $taskId,
                    'log_id' => $logId,
                    'success' => true
                ];
            }
            
            Db::commit();
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
        
        return $results;
    }
    
    /**
     * 重置每日消费统计
     */
    public static function resetDailyConsumption()
    {
        return Db::name('users')->update([
            'today_consumption' => 0,
            'update_time' => time()
        ]);
    }
    
    /**
     * 获取积分类型文本
     */
    public static function getTypeText($type)
    {
        $types = [
            self::TYPE_RECHARGE => '充值',
            self::TYPE_CONSUME => '消费',
            self::TYPE_REFUND => '退款',
            self::TYPE_REWARD => '奖励',
            self::TYPE_DEDUCT => '扣除'
        ];
        
        return $types[$type] ?? '未知';
    }
    
    /**
     * 清除用户积分缓存
     */
    private static function clearUserScoreCache($userId)
    {
        Cache::delete("user:score:{$userId}");
        Cache::delete("user:info:{$userId}");
    }
}
