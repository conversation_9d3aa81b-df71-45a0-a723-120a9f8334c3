# SEO积分系统开发文档 - 完善版3.2

## 功能模块详细设计

### 用户后台模块

#### 1. 用户认证模块

##### 登录功能
- **手机号登录**: 支持手机号+密码登录
- **验证码登录**: 支持手机号+短信验证码登录
- **记住登录**: 可选择7天免登录
- **安全验证**: 异常登录时需要额外验证

##### 注册功能
- **手机号注册**: 手机号+短信验证码注册
- **密码设置**: 密码强度验证（8位以上，包含字母数字）
- **用户协议**: 必须同意用户协议才能注册
- **默认用户组**: 注册后默认为"新手用户"组

#### 2. 任务管理模块

##### 关键词排名任务
```php
// 任务表单字段
$taskForm = [
    'keyword' => '关键词（必填）',
    'url' => '目标URL（必填）',
    'search_engine' => '搜索引擎（百度/360/搜狗/神马）',
    'online_days' => '上线天数（1-30天）',
    'daily_clicks' => '每日点击量（10-200次）',
    'click_time_range' => '点击时间段（可选）',
    'region' => '地域设置（可选）'
];

// 积分计算公式
$requiredScore = $onlineDays * $userScoreCost * $dailyClicks;
```

##### 下拉词任务
```php
// 下拉词表单字段
$dropdownForm = [
    'main_keyword' => '主关键词（必填）',
    'dropdown_words' => '下拉词列表（最多10个）',
    'search_engine' => '搜索引擎（百度/360/搜狗）',
    'online_days' => '上线天数（7-30天）',
    'daily_searches' => '每日搜索量（50-500次）'
];

// 积分计算（下拉词任务费用更高）
$requiredScore = $onlineDays * $userScoreCost * $dailySearches * 1.5;
```

##### 任务状态管理
- **待审核(1)**: 任务提交后等待管理员审核
- **优化中(2)**: 审核通过，正在执行优化
- **已完成(3)**: 任务执行完成
- **已暂停(4)**: 用户主动暂停或系统暂停
- **已取消(5)**: 用户取消或管理员拒绝

#### 3. 个人中心模块

##### 个人信息管理
- **基本信息**: 昵称、头像、手机号
- **密码修改**: 原密码验证+新密码设置
- **安全设置**: 登录通知、异常提醒
- **实名认证**: 身份证认证（可选功能）

##### 积分管理
- **积分余额**: 当前可用积分
- **充值记录**: 历史充值明细
- **消费记录**: 积分使用明细
- **积分统计**: 图表展示积分变化趋势

#### 4. 用户组升级系统

##### 用户组等级
```php
$userGroups = [
    1 => [
        'name' => '新手用户',
        'score_cost' => 1.0,
        'promotion_condition' => '累计消费1000积分',
        'benefits' => ['基础功能']
    ],
    2 => [
        'name' => '普通用户', 
        'score_cost' => 0.9,
        'promotion_condition' => '累计消费5000积分',
        'benefits' => ['9折优惠', '优先审核']
    ],
    3 => [
        'name' => '高级用户',
        'score_cost' => 0.8,
        'promotion_condition' => '累计消费20000积分',
        'benefits' => ['8折优惠', '专属客服', '高级功能']
    ],
    4 => [
        'name' => '元老用户',
        'score_cost' => 0.7,
        'promotion_condition' => '累计消费50000积分',
        'benefits' => ['7折优惠', 'VIP通道', '定制服务']
    ],
    5 => [
        'name' => 'VIP用户',
        'score_cost' => 0.6,
        'promotion_condition' => '累计消费100000积分',
        'benefits' => ['6折优惠', '专属经理', '无限制功能']
    ]
];
```

### 管理员后台模块

#### 1. 系统设置模块

##### 基础配置
```php
$systemConfigs = [
    'site_name' => 'SEO积分系统',
    'site_logo' => '/static/images/logo.png',
    'contact_phone' => '400-xxx-xxxx',
    'contact_email' => '<EMAIL>',
    'icp_number' => '京ICP备xxxxxxxx号',
    'copyright' => '© 2024 SEO积分系统 版权所有'
];
```

##### 业务配置
```php
$businessConfigs = [
    'default_score_cost' => 1.0, // 默认积分单价
    'min_recharge_amount' => 100, // 最小充值金额
    'max_daily_tasks' => 50, // 每日最大任务数
    'task_review_timeout' => 24, // 任务审核超时时间（小时）
    'score_refund_rate' => 0.8, // 积分退款比例
    'sensitive_word_check' => true, // 是否启用敏感词检测
];
```

#### 2. 用户管理模块

##### 用户列表
- **搜索筛选**: 手机号、昵称、用户组、状态
- **批量操作**: 批量启用/禁用、批量充值
- **详细信息**: 注册时间、最后登录、积分余额、消费总额
- **操作记录**: 登录日志、操作日志、充值记录

##### 用户充值
```php
// 充值记录表结构
$rechargeLog = [
    'user_id' => '用户ID',
    'amount' => '充值金额',
    'score' => '获得积分',
    'payment_method' => '支付方式（支付宝/微信/银行卡）',
    'trade_no' => '交易号',
    'status' => '状态（1待支付/2已支付/3已退款）',
    'create_time' => '创建时间',
    'pay_time' => '支付时间'
];
```

#### 3. 任务管理模块

##### 任务审核
- **待审核列表**: 显示所有待审核任务
- **审核操作**: 通过/拒绝，可添加审核备注
- **批量审核**: 支持批量通过符合条件的任务
- **敏感词检测**: 自动标记包含敏感词的任务

##### 任务监控
- **执行状态**: 实时监控任务执行情况
- **异常处理**: 处理执行失败的任务
- **效果统计**: 统计任务完成率、用户满意度

#### 4. 财务管理模块

##### 收入统计
```php
// 财务报表数据结构
$financeReport = [
    'daily_income' => '日收入统计',
    'monthly_income' => '月收入统计',
    'user_recharge' => '用户充值统计',
    'score_consumption' => '积分消费统计',
    'refund_amount' => '退款金额统计',
    'profit_analysis' => '利润分析'
];
```

##### 对账管理
- **支付对账**: 与第三方支付平台对账
- **积分对账**: 积分发放与消费对账
- **异常处理**: 处理对账差异

## 数据库设计详细说明

### 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE `users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `mobile` varchar(20) NOT NULL COMMENT '手机号（加密存储）',
  `password` varchar(255) NOT NULL COMMENT '密码（bcrypt加密）',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `group_id` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '用户组ID',
  `score` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '积分余额',
  `total_recharge` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计充值',
  `total_consumption` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计消费',
  `today_consumption` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '今日消费',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1正常 2锁定 3禁用',
  `last_login_time` int(11) unsigned DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` int(11) unsigned NOT NULL COMMENT '注册时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_mobile` (`mobile`),
  KEY `idx_group_status` (`group_id`, `status`),
  KEY `idx_status_create_time` (`status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 任务表 (tasks)
```sql
CREATE TABLE `tasks` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `type` tinyint(3) unsigned NOT NULL COMMENT '任务类型：1关键词排名 2下拉词',
  `keyword` varchar(100) NOT NULL COMMENT '关键词',
  `url` varchar(500) DEFAULT NULL COMMENT '目标URL（排名任务）',
  `dropdown_words` text DEFAULT NULL COMMENT '下拉词列表（JSON格式）',
  `search_engine` varchar(20) NOT NULL COMMENT '搜索引擎',
  `online_days` tinyint(3) unsigned NOT NULL COMMENT '上线天数',
  `daily_clicks` smallint(5) unsigned DEFAULT NULL COMMENT '每日点击量',
  `daily_searches` smallint(5) unsigned DEFAULT NULL COMMENT '每日搜索量',
  `click_time_range` varchar(50) DEFAULT NULL COMMENT '点击时间段',
  `region` varchar(50) DEFAULT NULL COMMENT '地域设置',
  `pre_deduct_score` decimal(10,2) unsigned NOT NULL COMMENT '预扣积分',
  `actual_cost_score` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '实际消费积分',
  `score_cost` decimal(4,2) unsigned NOT NULL COMMENT '积分单价',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1待审核 2优化中 3已完成 4已暂停 5已取消',
  `admin_id` int(11) unsigned DEFAULT NULL COMMENT '审核管理员ID',
  `review_time` int(11) unsigned DEFAULT NULL COMMENT '审核时间',
  `review_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `start_time` int(11) unsigned DEFAULT NULL COMMENT '开始执行时间',
  `end_time` int(11) unsigned DEFAULT NULL COMMENT '结束时间',
  `completion_rate` decimal(5,2) unsigned DEFAULT '0.00' COMMENT '完成率',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_status_type` (`user_id`, `status`, `type`),
  KEY `idx_status_create_time` (`status`, `create_time`),
  KEY `idx_search_engine_status` (`search_engine`, `status`),
  KEY `idx_admin_review` (`admin_id`, `review_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';
```

#### 积分记录表 (score_logs)
```sql
CREATE TABLE `score_logs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `task_id` int(11) unsigned DEFAULT NULL COMMENT '关联任务ID',
  `type` tinyint(3) unsigned NOT NULL COMMENT '类型：1充值 2消费 3退款 4奖励 5扣除',
  `amount` decimal(10,2) NOT NULL COMMENT '积分数量（正数为增加，负数为减少）',
  `balance_before` decimal(10,2) unsigned NOT NULL COMMENT '操作前余额',
  `balance_after` decimal(10,2) unsigned NOT NULL COMMENT '操作后余额',
  `description` varchar(255) NOT NULL COMMENT '操作描述',
  `admin_id` int(11) unsigned DEFAULT NULL COMMENT '操作管理员ID',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_type_time` (`user_id`, `type`, `create_time`),
  KEY `idx_task_type` (`task_id`, `type`),
  KEY `idx_type_create_time` (`type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';
```

### 其他重要表结构

#### 用户组表 (user_groups)
```sql
CREATE TABLE `user_groups` (
  `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户组ID',
  `name` varchar(50) NOT NULL COMMENT '用户组名称',
  `score_cost` decimal(4,2) unsigned NOT NULL DEFAULT '1.00' COMMENT '积分单价系数',
  `promotion_condition` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '升级条件（累计消费）',
  `max_daily_tasks` smallint(5) unsigned NOT NULL DEFAULT '50' COMMENT '每日最大任务数',
  `benefits` text DEFAULT NULL COMMENT '用户组权益（JSON格式）',
  `sort` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status_sort` (`status`, `sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组表';
```

#### 敏感词表 (sensitive_words)
```sql
CREATE TABLE `sensitive_words` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
  `word` varchar(100) NOT NULL COMMENT '敏感词',
  `category` varchar(50) DEFAULT NULL COMMENT '分类',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '级别：1一般 2严重 3禁止',
  `action` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '处理方式：1警告 2拒绝 3替换',
  `replacement` varchar(100) DEFAULT NULL COMMENT '替换词',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_word` (`word`),
  KEY `idx_category_status` (`category`, `status`),
  KEY `idx_level_status` (`level`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='敏感词表';
```

## API接口设计

### 用户端API

#### 认证相关
```php
// 用户登录
POST /api/user/login
{
    "mobile": "13800138000",
    "password": "password123",
    "remember": true
}

// 发送验证码
POST /api/user/send-sms
{
    "mobile": "13800138000",
    "type": "login" // login|register|reset
}

// 验证码登录
POST /api/user/sms-login
{
    "mobile": "13800138000",
    "code": "123456"
}

// 用户注册
POST /api/user/register
{
    "mobile": "13800138000",
    "password": "password123",
    "code": "123456",
    "agree_terms": true
}

// 刷新Token
POST /api/user/refresh-token
{
    "refresh_token": "refresh_token_string"
}
```

#### 任务管理
```php
// 创建任务
POST /api/user/tasks
{
    "type": 1,
    "keyword": "SEO优化",
    "url": "https://example.com",
    "search_engine": "baidu",
    "online_days": 15,
    "daily_clicks": 50,
    "region": "北京"
}

// 获取任务列表
GET /api/user/tasks?page=1&limit=20&status=1&type=1

// 获取任务详情
GET /api/user/tasks/{id}

// 暂停任务
PUT /api/user/tasks/{id}/pause

// 恢复任务
PUT /api/user/tasks/{id}/resume

// 取消任务
DELETE /api/user/tasks/{id}

// 批量添加任务
POST /api/user/tasks/batch
{
    "tasks": [
        {
            "type": 1,
            "keyword": "关键词1",
            "url": "https://example1.com",
            "search_engine": "baidu",
            "online_days": 10,
            "daily_clicks": 30
        },
        {
            "type": 1,
            "keyword": "关键词2",
            "url": "https://example2.com",
            "search_engine": "360",
            "online_days": 15,
            "daily_clicks": 40
        }
    ]
}
```

#### 积分管理
```php
// 获取积分余额
GET /api/user/score/balance

// 获取积分记录
GET /api/user/score/logs?page=1&limit=20&type=1

// 积分充值
POST /api/user/score/recharge
{
    "amount": 1000,
    "payment_method": "alipay"
}

// 获取充值记录
GET /api/user/score/recharge-logs?page=1&limit=20

// 积分统计
GET /api/user/score/statistics?period=month
```

### 管理员端API

#### 用户管理
```php
// 获取用户列表
GET /api/admin/users?page=1&limit=20&group_id=1&status=1&keyword=search

// 获取用户详情
GET /api/admin/users/{id}

// 更新用户状态
PUT /api/admin/users/{id}/status
{
    "status": 2
}

// 用户充值
POST /api/admin/users/{id}/recharge
{
    "amount": 1000,
    "remark": "管理员充值"
}

// 批量操作用户
POST /api/admin/users/batch
{
    "user_ids": [1, 2, 3],
    "action": "enable", // enable|disable|recharge
    "data": {
        "amount": 500
    }
}
```

#### 任务管理
```php
// 获取待审核任务
GET /api/admin/tasks/pending?page=1&limit=20

// 审核任务
PUT /api/admin/tasks/{id}/review
{
    "action": "approve", // approve|reject
    "remark": "审核通过"
}

// 批量审核任务
POST /api/admin/tasks/batch-review
{
    "task_ids": [1, 2, 3],
    "action": "approve",
    "remark": "批量审核通过"
}

// 获取任务统计
GET /api/admin/tasks/statistics?period=today
```

## 定时任务设计

### 核心定时任务

#### 1. 每日随机点击数更新
```php
// app/command/UpdateRandomClicks.php
class UpdateRandomClicks extends Command
{
    protected function execute(Input $input, Output $output)
    {
        $tasks = Db::name('tasks')
            ->where('status', 2) // 优化中的任务
            ->where('end_time', '>', time())
            ->select();
            
        foreach ($tasks as $task) {
            // 生成随机点击数（基础点击数的80%-120%）
            $baseClicks = $task['daily_clicks'];
            $randomClicks = rand($baseClicks * 0.8, $baseClicks * 1.2);
            
            // 更新任务执行日志
            Db::name('task_execution_logs')->insert([
                'task_id' => $task['id'],
                'user_id' => $task['user_id'],
                'execution_date' => date('Y-m-d'),
                'planned_clicks' => $baseClicks,
                'actual_clicks' => $randomClicks,
                'completion_rate' => min(100, ($randomClicks / $baseClicks) * 100),
                'create_time' => time()
            ]);
            
            $output->writeln("Task {$task['id']}: {$randomClicks} clicks generated");
        }
        
        $output->writeln('Random clicks update completed');
    }
}
```

#### 2. 每日积分重置
```php
// app/command/ResetDailyConsumption.php
class ResetDailyConsumption extends Command
{
    protected function execute(Input $input, Output $output)
    {
        // 重置所有用户的今日消费
        $result = Db::name('users')->update([
            'today_consumption' => 0,
            'update_time' => time()
        ]);
        
        $output->writeln("Reset daily consumption for {$result} users");
        
        // 清除相关缓存
        Cache::tag('user')->clear();
        Cache::tag('stats')->clear();
        
        $output->writeln('Daily consumption reset completed');
    }
}
```

#### 3. 过期任务检查
```php
// app/command/CheckExpiredTasks.php
class CheckExpiredTasks extends Command
{
    protected function execute(Input $input, Output $output)
    {
        $expiredTasks = Db::name('tasks')
            ->where('status', 2) // 优化中
            ->where('end_time', '<', time())
            ->select();
            
        foreach ($expiredTasks as $task) {
            // 更新任务状态为已完成
            Db::name('tasks')->where('id', $task['id'])->update([
                'status' => 3,
                'update_time' => time()
            ]);
            
            // 计算实际消费积分
            $executionDays = Db::name('task_execution_logs')
                ->where('task_id', $task['id'])
                ->count();
                
            $actualCost = $executionDays * $task['score_cost'] * $task['daily_clicks'];
            
            // 更新实际消费
            Db::name('tasks')->where('id', $task['id'])->update([
                'actual_cost_score' => $actualCost
            ]);
            
            // 如果预扣积分大于实际消费，退还差额
            if ($task['pre_deduct_score'] > $actualCost) {
                $refundAmount = $task['pre_deduct_score'] - $actualCost;
                
                // 退还积分
                Db::name('users')
                    ->where('id', $task['user_id'])
                    ->inc('score', $refundAmount);
                    
                // 记录积分日志
                Db::name('score_logs')->insert([
                    'user_id' => $task['user_id'],
                    'task_id' => $task['id'],
                    'type' => 3, // 退款
                    'amount' => $refundAmount,
                    'description' => "任务完成退还积分",
                    'create_time' => time()
                ]);
            }
            
            $output->writeln("Task {$task['id']} completed, refund: {$refundAmount}");
        }
        
        $output->writeln('Expired tasks check completed');
    }
}
```

#### 4. 积分退款处理
```php
// app/command/ProcessScoreRefunds.php
class ProcessScoreRefunds extends Command
{
    protected function execute(Input $input, Output $output)
    {
        // 处理被拒绝的任务退款
        $rejectedTasks = Db::name('tasks')
            ->where('status', 5) // 已取消
            ->where('actual_cost_score', 0) // 未处理退款
            ->select();
            
        foreach ($rejectedTasks as $task) {
            // 计算退款金额（80%退款）
            $refundAmount = $task['pre_deduct_score'] * 0.8;
            
            // 退还积分
            Db::name('users')
                ->where('id', $task['user_id'])
                ->inc('score', $refundAmount);
                
            // 更新任务实际消费
            Db::name('tasks')->where('id', $task['id'])->update([
                'actual_cost_score' => $task['pre_deduct_score'] - $refundAmount,
                'update_time' => time()
            ]);
            
            // 记录积分日志
            Db::name('score_logs')->insert([
                'user_id' => $task['user_id'],
                'task_id' => $task['id'],
                'type' => 3, // 退款
                'amount' => $refundAmount,
                'description' => "任务取消退还积分（80%）",
                'create_time' => time()
            ]);
            
            $output->writeln("Task {$task['id']} refunded: {$refundAmount}");
        }
        
        $output->writeln('Score refunds processing completed');
    }
}
```

## 部署说明

### 环境要求
- **PHP**: 8.1+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Nginx**: 1.18+
- **Composer**: 2.0+

### 部署步骤

#### 1. 代码部署
```bash
# 克隆代码
git clone https://github.com/your-repo/seo-score-system.git
cd seo-score-system

# 安装依赖
composer install --no-dev --optimize-autoloader

# 复制配置文件
cp .env.example .env

# 设置目录权限
chmod -R 755 storage/
chmod -R 755 runtime/
chown -R www-data:www-data storage/
chown -R www-data:www-data runtime/
```

#### 2. 数据库配置
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE seo_score_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库结构
mysql -u root -p seo_score_system < database/install.sql

# 运行数据迁移
php think migrate:run

# 初始化数据
php think seed:run
```

#### 3. 配置定时任务
```bash
# 编辑crontab
crontab -e

# 添加定时任务
# 每日凌晨1点更新随机点击数
0 1 * * * cd /path/to/project && php think update:random-clicks

# 每日凌晨2点重置今日消费
0 2 * * * cd /path/to/project && php think reset:daily-consumption

# 每日凌晨3点检查过期任务
0 3 * * * cd /path/to/project && php think check:expired-tasks

# 每小时处理积分退款
0 * * * * cd /path/to/project && php think process:score-refunds

# 每10分钟清理过期缓存
*/10 * * * * cd /path/to/project && php think cache:clear-expired
```

### 维护指南

#### 日志管理
```bash
# 日志轮转配置
/etc/logrotate.d/seo-system
/path/to/project/runtime/log/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        /usr/sbin/service nginx reload > /dev/null 2>&1 || true
    endscript
}
```

#### 数据库维护
```sql
-- 定期清理过期数据
DELETE FROM task_execution_logs WHERE create_time < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 3 MONTH));
DELETE FROM user_login_logs WHERE login_time < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 6 MONTH));
DELETE FROM security_logs WHERE create_time < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH));

-- 优化表结构
OPTIMIZE TABLE tasks, score_logs, user_login_logs;

-- 分析表统计信息
ANALYZE TABLE tasks, score_logs, users;
```

#### 性能监控
```bash
# 监控脚本
#!/bin/bash
# monitor.sh

# 检查MySQL连接数
mysql_connections=$(mysql -u monitor -p'password' -e "SHOW STATUS LIKE 'Threads_connected';" | awk 'NR==2{print $2}')
echo "MySQL Connections: $mysql_connections"

# 检查Redis内存使用
redis_memory=$(redis-cli info memory | grep used_memory_human | cut -d: -f2)
echo "Redis Memory: $redis_memory"

# 检查磁盘空间
disk_usage=$(df -h /path/to/project | awk 'NR==2{print $5}')
echo "Disk Usage: $disk_usage"

# 检查PHP-FPM进程
php_fpm_processes=$(ps aux | grep php-fpm | grep -v grep | wc -l)
echo "PHP-FPM Processes: $php_fpm_processes"
```

---

**总结**: 完善版3.2文档包含了详细的功能模块设计、数据库结构、API接口、定时任务和部署维护说明，与完善版3.1的性能和安全优化建议相结合，形成了完整的SEO积分系统开发指南。