TYPE=VIEW
query=select cast(`douyin_comments`.`comments`.`collection_time` as date) AS `collection_date`,count(distinct `douyin_comments`.`comments`.`video_id`) AS `videos_collected`,count(0) AS `total_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'primary\') then 1 else 0 end)) AS `primary_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'reply\') then 1 else 0 end)) AS `reply_comments`,count(distinct `douyin_comments`.`comments`.`uid`) AS `unique_users`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes`,round(avg(`douyin_comments`.`comments`.`digg_count`),2) AS `avg_likes`,min(`douyin_comments`.`comments`.`collection_time`) AS `first_collection`,max(`douyin_comments`.`comments`.`collection_time`) AS `last_collection` from `douyin_comments`.`comments` where (`douyin_comments`.`comments`.`collection_time` is not null) group by cast(`douyin_comments`.`comments`.`collection_time` as date) order by `collection_date` desc
md5=a750d2b36abbc8237b9735ccd1366dc2
updatable=0
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=2025-06-23 12:49:47
create-version=1
source=SELECT 
\n    DATE(collection_time) as collection_date,
\n    COUNT(DISTINCT video_id) as videos_collected,
\n    COUNT(*) as total_comments,
\n    SUM(CASE WHEN comment_type = \'primary\' THEN 1 ELSE 0 END) as primary_comments,
\n    SUM(CASE WHEN comment_type = \'reply\' THEN 1 ELSE 0 END) as reply_comments,
\n    COUNT(DISTINCT uid) as unique_users,
\n    SUM(digg_count) as total_likes,
\n    ROUND(AVG(digg_count), 2) as avg_likes,
\n    -- 时间分布
\n    MIN(collection_time) as first_collection,
\n    MAX(collection_time) as last_collection
\nFROM comments 
\nWHERE collection_time IS NOT NULL
\nGROUP BY DATE(collection_time)
\nORDER BY collection_date DESC
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_general_ci
view_body_utf8=select cast(`douyin_comments`.`comments`.`collection_time` as date) AS `collection_date`,count(distinct `douyin_comments`.`comments`.`video_id`) AS `videos_collected`,count(0) AS `total_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'primary\') then 1 else 0 end)) AS `primary_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'reply\') then 1 else 0 end)) AS `reply_comments`,count(distinct `douyin_comments`.`comments`.`uid`) AS `unique_users`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes`,round(avg(`douyin_comments`.`comments`.`digg_count`),2) AS `avg_likes`,min(`douyin_comments`.`comments`.`collection_time`) AS `first_collection`,max(`douyin_comments`.`comments`.`collection_time`) AS `last_collection` from `douyin_comments`.`comments` where (`douyin_comments`.`comments`.`collection_time` is not null) group by cast(`douyin_comments`.`comments`.`collection_time` as date) order by `collection_date` desc
