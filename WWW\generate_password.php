<?php
/**
 * 密码生成工具
 * 用于生成正确的bcrypt密码哈希
 */

// 管理员密码: admin123456
$admin_password = 'admin123456';
$admin_hash = password_hash($admin_password, PASSWORD_BCRYPT);

// 用户密码: password123
$user_password = 'password123';
$user_hash = password_hash($user_password, PASSWORD_BCRYPT);

echo "管理员密码哈希 (admin123456):\n";
echo $admin_hash . "\n\n";

echo "用户密码哈希 (password123):\n";
echo $user_hash . "\n\n";

// 验证密码
echo "验证管理员密码: " . (password_verify($admin_password, $admin_hash) ? '正确' : '错误') . "\n";
echo "验证用户密码: " . (password_verify($user_password, $user_hash) ? '正确' : '错误') . "\n";

// 生成SQL语句
echo "\n-- 更新SQL语句:\n";
echo "-- 管理员账号 (admin / admin123456)\n";
echo "UPDATE admins SET password = '{$admin_hash}' WHERE username = 'admin';\n\n";

echo "-- 测试用户 (13800138000 / password123)\n";
echo "UPDATE users SET password = '{$user_hash}' WHERE mobile_hash = MD5('13800138000');\n";
?>
