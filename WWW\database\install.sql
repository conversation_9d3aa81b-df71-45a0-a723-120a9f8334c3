-- SEO积分系统数据库结构
-- 创建时间: 2024-12-25

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `mobile` varchar(20) NOT NULL COMMENT '手机号（加密存储）',
  `mobile_hash` varchar(64) NOT NULL COMMENT '手机号哈希(用于查询)',
  `password` varchar(255) NOT NULL COMMENT '密码（bcrypt加密）',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `group_id` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '用户组ID',
  `score` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '积分余额',
  `total_recharge` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计充值',
  `total_consumption` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计消费',
  `today_consumption` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '今日消费',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1正常 2锁定 3禁用',
  `last_login_time` int(11) unsigned DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` int(11) unsigned NOT NULL COMMENT '注册时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_mobile_hash` (`mobile_hash`),
  KEY `idx_group_status` (`group_id`, `status`),
  KEY `idx_status_create` (`status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 用户组表
-- ----------------------------
DROP TABLE IF EXISTS `user_groups`;
CREATE TABLE `user_groups` (
  `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户组ID',
  `name` varchar(50) NOT NULL COMMENT '用户组名称',
  `score_cost` decimal(4,2) unsigned NOT NULL DEFAULT '1.00' COMMENT '积分单价系数',
  `promotion_condition` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '升级条件（累计消费）',
  `max_daily_tasks` smallint(5) unsigned NOT NULL DEFAULT '50' COMMENT '每日最大任务数',
  `benefits` text DEFAULT NULL COMMENT '用户组权益（JSON格式）',
  `sort` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status_sort` (`status`, `sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组表';

-- ----------------------------
-- 任务表
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `type` tinyint(3) unsigned NOT NULL COMMENT '任务类型：1关键词排名 2下拉词',
  `keyword` varchar(100) NOT NULL COMMENT '关键词',
  `url` varchar(500) DEFAULT NULL COMMENT '目标URL（排名任务）',
  `dropdown_words` text DEFAULT NULL COMMENT '下拉词列表（JSON格式）',
  `search_engine` varchar(20) NOT NULL COMMENT '搜索引擎',
  `online_days` tinyint(3) unsigned NOT NULL COMMENT '上线天数',
  `daily_clicks` smallint(5) unsigned DEFAULT NULL COMMENT '每日点击量',
  `daily_searches` smallint(5) unsigned DEFAULT NULL COMMENT '每日搜索量',
  `click_time_range` varchar(50) DEFAULT NULL COMMENT '点击时间段',
  `region` varchar(50) DEFAULT NULL COMMENT '地域设置',
  `pre_deduct_score` decimal(10,2) unsigned NOT NULL COMMENT '预扣积分',
  `actual_cost_score` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '实际消费积分',
  `score_cost` decimal(4,2) unsigned NOT NULL COMMENT '积分单价',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1待审核 2优化中 3已完成 4已暂停 5已取消',
  `admin_id` int(11) unsigned DEFAULT NULL COMMENT '审核管理员ID',
  `review_time` int(11) unsigned DEFAULT NULL COMMENT '审核时间',
  `review_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `start_time` int(11) unsigned DEFAULT NULL COMMENT '开始执行时间',
  `end_time` int(11) unsigned DEFAULT NULL COMMENT '结束时间',
  `completion_rate` decimal(5,2) unsigned DEFAULT '0.00' COMMENT '完成率',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_status_type` (`user_id`, `status`, `type`),
  KEY `idx_status_create_time` (`status`, `create_time`),
  KEY `idx_search_engine_status` (`search_engine`, `status`),
  KEY `idx_admin_review` (`admin_id`, `review_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- ----------------------------
-- 积分记录表（分区表）
-- ----------------------------
DROP TABLE IF EXISTS `score_logs`;
CREATE TABLE `score_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `task_id` int(11) unsigned DEFAULT NULL COMMENT '关联任务ID',
  `type` tinyint(3) unsigned NOT NULL COMMENT '类型：1充值 2消费 3退款 4奖励 5扣除',
  `amount` decimal(10,2) NOT NULL COMMENT '积分数量（正数为增加，负数为减少）',
  `balance_before` decimal(12,2) unsigned NOT NULL COMMENT '操作前余额',
  `balance_after` decimal(12,2) unsigned NOT NULL COMMENT '操作后余额',
  `description` varchar(255) NOT NULL COMMENT '操作描述',
  `admin_id` int(11) unsigned DEFAULT NULL COMMENT '操作管理员ID',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`, `create_time`),
  KEY `idx_user_type_time` (`user_id`, `type`, `create_time`),
  KEY `idx_task_type` (`task_id`, `type`),
  KEY `idx_type_create_time` (`type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表'
PARTITION BY RANGE (create_time) (
    PARTITION p_default VALUES LESS THAN (1704067200),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- ----------------------------
-- 敏感词表
-- ----------------------------
DROP TABLE IF EXISTS `sensitive_words`;
CREATE TABLE `sensitive_words` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
  `word` varchar(100) NOT NULL COMMENT '敏感词',
  `category` varchar(50) DEFAULT NULL COMMENT '分类',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '级别：1一般 2严重 3禁止',
  `action` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '处理方式：1警告 2拒绝 3替换',
  `replacement` varchar(100) DEFAULT NULL COMMENT '替换词',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_word` (`word`),
  KEY `idx_category_status` (`category`, `status`),
  KEY `idx_level_status` (`level`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='敏感词表';

-- ----------------------------
-- 任务执行日志表
-- ----------------------------
DROP TABLE IF EXISTS `task_execution_logs`;
CREATE TABLE `task_execution_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `task_id` int(11) unsigned NOT NULL COMMENT '任务ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `execution_date` date NOT NULL COMMENT '执行日期',
  `planned_amount` smallint(5) unsigned NOT NULL COMMENT '计划数量',
  `actual_amount` smallint(5) unsigned NOT NULL COMMENT '实际数量',
  `completion_rate` decimal(5,2) unsigned DEFAULT '0.00' COMMENT '完成率',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`, `create_time`),
  KEY `idx_task_date` (`task_id`, `execution_date`),
  KEY `idx_user_date` (`user_id`, `execution_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务执行日志表'
PARTITION BY RANGE (create_time) (
    PARTITION p_default VALUES LESS THAN (1704067200),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- ----------------------------
-- 用户登录日志表
-- ----------------------------
DROP TABLE IF EXISTS `user_login_logs`;
CREATE TABLE `user_login_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `login_ip` varchar(45) NOT NULL COMMENT '登录IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `device_fingerprint` varchar(64) DEFAULT NULL COMMENT '设备指纹',
  `login_time` int(11) unsigned NOT NULL COMMENT '登录时间',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1成功 2失败',
  PRIMARY KEY (`id`),
  KEY `idx_user_login_time` (`user_id`, `login_time`),
  KEY `idx_login_time` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表';

-- ----------------------------
-- 安全日志表
-- ----------------------------
DROP TABLE IF EXISTS `security_logs`;
CREATE TABLE `security_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(11) unsigned DEFAULT NULL COMMENT '用户ID',
  `type` varchar(50) NOT NULL COMMENT '安全事件类型',
  `operation` varchar(100) DEFAULT NULL COMMENT '操作类型',
  `risk_level` tinyint(3) unsigned DEFAULT '0' COMMENT '风险等级',
  `ip` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `data` text DEFAULT NULL COMMENT '相关数据（JSON格式）',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_type` (`user_id`, `type`),
  KEY `idx_type_time` (`type`, `create_time`),
  KEY `idx_risk_level` (`risk_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全日志表';

-- ----------------------------
-- 用户组表
-- ----------------------------
DROP TABLE IF EXISTS `user_groups`;
CREATE TABLE `user_groups` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户组ID',
  `name` varchar(50) NOT NULL COMMENT '用户组名称',
  `score_cost` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '积分消费倍率',
  `promotion_condition` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '升级条件（消费金额）',
  `max_daily_tasks` int(11) unsigned NOT NULL DEFAULT '10' COMMENT '每日最大任务数',
  `benefits` text DEFAULT NULL COMMENT '用户组权益（JSON格式）',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序权重',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_sort_status` (`sort`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组表';

-- ----------------------------
-- 管理员表
-- ----------------------------
DROP TABLE IF EXISTS `admins`;
CREATE TABLE `admins` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码（bcrypt加密）',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `role` varchar(50) NOT NULL DEFAULT 'admin' COMMENT '角色：super_admin超级管理员 admin普通管理员',
  `permissions` text DEFAULT NULL COMMENT '权限列表（JSON格式）',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1正常 2锁定 3禁用',
  `last_login_time` int(11) unsigned DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_role_status` (`role`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- ----------------------------
-- 系统配置表
-- ----------------------------
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `key` varchar(100) NOT NULL COMMENT '配置键',
  `value` text NOT NULL COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '数据类型',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key` (`key`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ----------------------------
-- 性能日志表
-- ----------------------------
DROP TABLE IF EXISTS `performance_logs`;
CREATE TABLE `performance_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `type` varchar(50) NOT NULL COMMENT '性能类型',
  `content` text NOT NULL COMMENT '日志内容（JSON格式）',
  `execution_time` int(11) unsigned DEFAULT NULL COMMENT '执行时间（毫秒）',
  `memory_usage` int(11) unsigned DEFAULT NULL COMMENT '内存使用（字节）',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_type_time` (`type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='性能日志表';

-- ----------------------------
-- 插入默认数据
-- ----------------------------

-- 插入用户组数据
INSERT INTO `user_groups` (`id`, `name`, `score_cost`, `promotion_condition`, `max_daily_tasks`, `benefits`, `sort`, `status`, `create_time`, `update_time`) VALUES
(1, '新手用户', 1.00, 0.00, 10, '{"description": "新用户默认等级"}', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '普通用户', 0.95, 100.00, 20, '{"description": "消费满100元升级", "discount": "5%"}', 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '高级用户', 0.90, 500.00, 50, '{"description": "消费满500元升级", "discount": "10%"}', 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, '资深用户', 0.85, 1000.00, 100, '{"description": "消费满1000元升级", "discount": "15%"}', 4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(5, 'VIP用户', 0.80, 5000.00, 200, '{"description": "消费满5000元升级", "discount": "20%"}', 5, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入默认管理员账号 (密码: admin123456)
INSERT INTO `admins` (`username`, `password`, `email`, `nickname`, `role`, `permissions`, `status`, `create_time`, `update_time`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '超级管理员', 'super_admin', '[\"*\"]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入测试用户 (密码: password123)
INSERT INTO `users` (`mobile`, `mobile_hash`, `password`, `nickname`, `group_id`, `score`, `status`, `create_time`, `update_time`) VALUES
('***********', MD5('***********'), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户', 1, 100.00, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入系统配置
INSERT INTO `system_configs` (`key`, `value`, `description`, `type`, `status`, `create_time`, `update_time`) VALUES
('site_name', 'SEO积分系统', '网站名称', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('site_description', '专业的SEO优化积分管理平台', '网站描述', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('contact_email', '<EMAIL>', '联系邮箱', 'string', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('min_recharge_amount', '10', '最小充值金额', 'number', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('max_recharge_amount', '10000', '最大充值金额', 'number', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('task_review_required', '1', '任务是否需要审核', 'boolean', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('auto_settlement_enabled', '1', '是否启用自动结算', 'boolean', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('maintenance_mode', '0', '维护模式', 'boolean', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

SET FOREIGN_KEY_CHECKS = 1;

-- 安装完成提示
SELECT 'SEO积分系统数据库初始化完成！' as message,
       '管理员账号: admin / admin123456' as admin_account,
       '测试用户: *********** / password123' as test_account;
