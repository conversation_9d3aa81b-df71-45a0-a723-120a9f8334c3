-- SEO积分系统数据库结构
-- 创建时间: 2024-12-25

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `mobile` varchar(20) NOT NULL COMMENT '手机号（加密存储）',
  `mobile_hash` varchar(64) NOT NULL COMMENT '手机号哈希(用于查询)',
  `password` varchar(255) NOT NULL COMMENT '密码（bcrypt加密）',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `group_id` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '用户组ID',
  `score` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '积分余额',
  `total_recharge` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计充值',
  `total_consumption` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计消费',
  `today_consumption` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '今日消费',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1正常 2锁定 3禁用',
  `last_login_time` int(11) unsigned DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` int(11) unsigned NOT NULL COMMENT '注册时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_mobile_hash` (`mobile_hash`),
  KEY `idx_group_status` (`group_id`, `status`),
  KEY `idx_status_create` (`status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 用户组表
-- ----------------------------
DROP TABLE IF EXISTS `user_groups`;
CREATE TABLE `user_groups` (
  `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户组ID',
  `name` varchar(50) NOT NULL COMMENT '用户组名称',
  `score_cost` decimal(4,2) unsigned NOT NULL DEFAULT '1.00' COMMENT '积分单价系数',
  `promotion_condition` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '升级条件（累计消费）',
  `max_daily_tasks` smallint(5) unsigned NOT NULL DEFAULT '50' COMMENT '每日最大任务数',
  `benefits` text DEFAULT NULL COMMENT '用户组权益（JSON格式）',
  `sort` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status_sort` (`status`, `sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组表';

-- ----------------------------
-- 任务表
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `type` tinyint(3) unsigned NOT NULL COMMENT '任务类型：1关键词排名 2下拉词',
  `keyword` varchar(100) NOT NULL COMMENT '关键词',
  `url` varchar(500) DEFAULT NULL COMMENT '目标URL（排名任务）',
  `dropdown_words` text DEFAULT NULL COMMENT '下拉词列表（JSON格式）',
  `search_engine` varchar(20) NOT NULL COMMENT '搜索引擎',
  `online_days` tinyint(3) unsigned NOT NULL COMMENT '上线天数',
  `daily_clicks` smallint(5) unsigned DEFAULT NULL COMMENT '每日点击量',
  `daily_searches` smallint(5) unsigned DEFAULT NULL COMMENT '每日搜索量',
  `click_time_range` varchar(50) DEFAULT NULL COMMENT '点击时间段',
  `region` varchar(50) DEFAULT NULL COMMENT '地域设置',
  `pre_deduct_score` decimal(10,2) unsigned NOT NULL COMMENT '预扣积分',
  `actual_cost_score` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '实际消费积分',
  `score_cost` decimal(4,2) unsigned NOT NULL COMMENT '积分单价',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1待审核 2优化中 3已完成 4已暂停 5已取消',
  `admin_id` int(11) unsigned DEFAULT NULL COMMENT '审核管理员ID',
  `review_time` int(11) unsigned DEFAULT NULL COMMENT '审核时间',
  `review_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `start_time` int(11) unsigned DEFAULT NULL COMMENT '开始执行时间',
  `end_time` int(11) unsigned DEFAULT NULL COMMENT '结束时间',
  `completion_rate` decimal(5,2) unsigned DEFAULT '0.00' COMMENT '完成率',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_status_type` (`user_id`, `status`, `type`),
  KEY `idx_status_create_time` (`status`, `create_time`),
  KEY `idx_search_engine_status` (`search_engine`, `status`),
  KEY `idx_admin_review` (`admin_id`, `review_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- ----------------------------
-- 积分记录表（分区表）
-- ----------------------------
DROP TABLE IF EXISTS `score_logs`;
CREATE TABLE `score_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `task_id` int(11) unsigned DEFAULT NULL COMMENT '关联任务ID',
  `type` tinyint(3) unsigned NOT NULL COMMENT '类型：1充值 2消费 3退款 4奖励 5扣除',
  `amount` decimal(10,2) NOT NULL COMMENT '积分数量（正数为增加，负数为减少）',
  `balance_before` decimal(12,2) unsigned NOT NULL COMMENT '操作前余额',
  `balance_after` decimal(12,2) unsigned NOT NULL COMMENT '操作后余额',
  `description` varchar(255) NOT NULL COMMENT '操作描述',
  `admin_id` int(11) unsigned DEFAULT NULL COMMENT '操作管理员ID',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`, `create_time`),
  KEY `idx_user_type_time` (`user_id`, `type`, `create_time`),
  KEY `idx_task_type` (`task_id`, `type`),
  KEY `idx_type_create_time` (`type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表'
PARTITION BY RANGE (create_time) (
    PARTITION p_default VALUES LESS THAN (1704067200),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- ----------------------------
-- 敏感词表
-- ----------------------------
DROP TABLE IF EXISTS `sensitive_words`;
CREATE TABLE `sensitive_words` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
  `word` varchar(100) NOT NULL COMMENT '敏感词',
  `category` varchar(50) DEFAULT NULL COMMENT '分类',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '级别：1一般 2严重 3禁止',
  `action` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '处理方式：1警告 2拒绝 3替换',
  `replacement` varchar(100) DEFAULT NULL COMMENT '替换词',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_word` (`word`),
  KEY `idx_category_status` (`category`, `status`),
  KEY `idx_level_status` (`level`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='敏感词表';

-- ----------------------------
-- 任务执行日志表
-- ----------------------------
DROP TABLE IF EXISTS `task_execution_logs`;
CREATE TABLE `task_execution_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `task_id` int(11) unsigned NOT NULL COMMENT '任务ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `execution_date` date NOT NULL COMMENT '执行日期',
  `planned_amount` smallint(5) unsigned NOT NULL COMMENT '计划数量',
  `actual_amount` smallint(5) unsigned NOT NULL COMMENT '实际数量',
  `completion_rate` decimal(5,2) unsigned DEFAULT '0.00' COMMENT '完成率',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`, `create_time`),
  KEY `idx_task_date` (`task_id`, `execution_date`),
  KEY `idx_user_date` (`user_id`, `execution_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务执行日志表'
PARTITION BY RANGE (create_time) (
    PARTITION p_default VALUES LESS THAN (1704067200),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- ----------------------------
-- 用户登录日志表
-- ----------------------------
DROP TABLE IF EXISTS `user_login_logs`;
CREATE TABLE `user_login_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `login_ip` varchar(45) NOT NULL COMMENT '登录IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `device_fingerprint` varchar(64) DEFAULT NULL COMMENT '设备指纹',
  `login_time` int(11) unsigned NOT NULL COMMENT '登录时间',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1成功 2失败',
  PRIMARY KEY (`id`),
  KEY `idx_user_login_time` (`user_id`, `login_time`),
  KEY `idx_login_time` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表';

-- ----------------------------
-- 安全日志表
-- ----------------------------
DROP TABLE IF EXISTS `security_logs`;
CREATE TABLE `security_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(11) unsigned DEFAULT NULL COMMENT '用户ID',
  `type` varchar(50) NOT NULL COMMENT '安全事件类型',
  `operation` varchar(100) DEFAULT NULL COMMENT '操作类型',
  `risk_level` tinyint(3) unsigned DEFAULT '0' COMMENT '风险等级',
  `ip` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `data` text DEFAULT NULL COMMENT '相关数据（JSON格式）',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_type` (`user_id`, `type`),
  KEY `idx_type_time` (`type`, `create_time`),
  KEY `idx_risk_level` (`risk_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全日志表';

-- ----------------------------
-- 系统配置表
-- ----------------------------
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `key` varchar(100) NOT NULL COMMENT '配置键',
  `value` text NOT NULL COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '数据类型',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key` (`key`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ----------------------------
-- 性能日志表
-- ----------------------------
DROP TABLE IF EXISTS `performance_logs`;
CREATE TABLE `performance_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `type` varchar(50) NOT NULL COMMENT '性能类型',
  `content` text NOT NULL COMMENT '日志内容（JSON格式）',
  `execution_time` int(11) unsigned DEFAULT NULL COMMENT '执行时间（毫秒）',
  `memory_usage` int(11) unsigned DEFAULT NULL COMMENT '内存使用（字节）',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_type_time` (`type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='性能日志表';

SET FOREIGN_KEY_CHECKS = 1;
