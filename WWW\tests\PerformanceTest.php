<?php

namespace tests;

use PHPUnit\Framework\TestCase;
use think\facade\Db;
use think\facade\Cache;
use app\service\UserService;
use app\service\TaskService;
use app\service\ScoreService;

/**
 * 性能测试
 */
class PerformanceTest extends TestCase
{
    /**
     * 测试数据库查询性能
     */
    public function testDatabasePerformance()
    {
        $startTime = microtime(true);
        
        // 执行一系列数据库查询
        for ($i = 0; $i < 100; $i++) {
            Db::name('users')->where('id', '>', 0)->limit(10)->select();
        }
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000; // 转换为毫秒
        
        echo "数据库查询性能测试: {$executionTime}ms\n";
        
        // 断言执行时间应该在合理范围内（例如小于5秒）
        $this->assertLessThan(5000, $executionTime, '数据库查询性能过慢');
    }
    
    /**
     * 测试缓存性能
     */
    public function testCachePerformance()
    {
        $startTime = microtime(true);
        
        // 测试缓存写入性能
        for ($i = 0; $i < 1000; $i++) {
            Cache::set("test_key_{$i}", "test_value_{$i}", 60);
        }
        
        $writeTime = microtime(true);
        
        // 测试缓存读取性能
        for ($i = 0; $i < 1000; $i++) {
            Cache::get("test_key_{$i}");
        }
        
        $readTime = microtime(true);
        
        // 清理测试数据
        for ($i = 0; $i < 1000; $i++) {
            Cache::delete("test_key_{$i}");
        }
        
        $writeExecutionTime = ($writeTime - $startTime) * 1000;
        $readExecutionTime = ($readTime - $writeTime) * 1000;
        
        echo "缓存写入性能测试: {$writeExecutionTime}ms\n";
        echo "缓存读取性能测试: {$readExecutionTime}ms\n";
        
        // 断言缓存操作应该很快
        $this->assertLessThan(1000, $writeExecutionTime, '缓存写入性能过慢');
        $this->assertLessThan(500, $readExecutionTime, '缓存读取性能过慢');
    }
    
    /**
     * 测试用户服务性能
     */
    public function testUserServicePerformance()
    {
        $startTime = microtime(true);
        
        // 模拟用户注册
        for ($i = 0; $i < 10; $i++) {
            $userData = [
                'username' => 'perf_test_' . $i . '_' . time(),
                'password' => 'test123456',
                'email' => 'perf_test_' . $i . '_' . time() . '@example.com'
            ];
            
            try {
                UserService::register($userData);
            } catch (\Exception $e) {
                // 忽略注册失败（可能是用户名重复等）
            }
        }
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;
        
        echo "用户服务性能测试: {$executionTime}ms\n";
        
        // 清理测试数据
        Db::name('users')->where('username', 'like', 'perf_test_%')->delete();
        
        $this->assertLessThan(5000, $executionTime, '用户服务性能过慢');
    }
    
    /**
     * 测试任务服务性能
     */
    public function testTaskServicePerformance()
    {
        // 先创建一个测试用户
        $userData = [
            'username' => 'task_perf_test_' . time(),
            'password' => 'test123456',
            'email' => 'task_perf_test_' . time() . '@example.com'
        ];
        
        $user = UserService::register($userData);
        if (!$user) {
            $this->markTestSkipped('无法创建测试用户');
            return;
        }
        
        $startTime = microtime(true);
        
        // 模拟任务创建
        for ($i = 0; $i < 10; $i++) {
            $taskData = [
                'user_id' => $user['id'],
                'keyword' => '性能测试关键词' . $i,
                'url' => 'https://example.com',
                'type' => TaskService::TYPE_KEYWORD_RANKING,
                'search_engine' => 'baidu',
                'daily_clicks' => 10,
                'online_days' => 7,
                'click_time_range' => 'all_day'
            ];
            
            try {
                TaskService::create($taskData);
            } catch (\Exception $e) {
                // 忽略创建失败
            }
        }
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;
        
        echo "任务服务性能测试: {$executionTime}ms\n";
        
        // 清理测试数据
        Db::name('tasks')->where('user_id', $user['id'])->delete();
        Db::name('users')->where('id', $user['id'])->delete();
        
        $this->assertLessThan(3000, $executionTime, '任务服务性能过慢');
    }
    
    /**
     * 测试积分服务性能
     */
    public function testScoreServicePerformance()
    {
        // 先创建一个测试用户
        $userData = [
            'username' => 'score_perf_test_' . time(),
            'password' => 'test123456',
            'email' => 'score_perf_test_' . time() . '@example.com'
        ];
        
        $user = UserService::register($userData);
        if (!$user) {
            $this->markTestSkipped('无法创建测试用户');
            return;
        }
        
        $startTime = microtime(true);
        
        // 模拟积分操作
        for ($i = 0; $i < 50; $i++) {
            try {
                ScoreService::recharge($user['id'], 10, '性能测试充值');
                ScoreService::consume($user['id'], 5, '性能测试消费');
            } catch (\Exception $e) {
                // 忽略操作失败
            }
        }
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;
        
        echo "积分服务性能测试: {$executionTime}ms\n";
        
        // 清理测试数据
        Db::name('score_logs')->where('user_id', $user['id'])->delete();
        Db::name('users')->where('id', $user['id'])->delete();
        
        $this->assertLessThan(2000, $executionTime, '积分服务性能过慢');
    }
    
    /**
     * 测试并发性能
     */
    public function testConcurrentPerformance()
    {
        $processes = [];
        $startTime = microtime(true);
        
        // 模拟并发请求（使用curl）
        for ($i = 0; $i < 5; $i++) {
            $cmd = "curl -s http://localhost/api/system/config > /dev/null 2>&1 &";
            exec($cmd);
        }
        
        // 等待所有请求完成
        sleep(2);
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;
        
        echo "并发性能测试: {$executionTime}ms\n";
        
        $this->assertLessThan(5000, $executionTime, '并发处理性能过慢');
    }
    
    /**
     * 测试内存使用
     */
    public function testMemoryUsage()
    {
        $startMemory = memory_get_usage();
        
        // 执行一些内存密集型操作
        $data = [];
        for ($i = 0; $i < 10000; $i++) {
            $data[] = [
                'id' => $i,
                'name' => 'test_' . $i,
                'data' => str_repeat('x', 100)
            ];
        }
        
        $peakMemory = memory_get_peak_usage();
        $endMemory = memory_get_usage();
        
        $memoryUsed = ($endMemory - $startMemory) / 1024 / 1024; // MB
        $peakMemoryUsed = $peakMemory / 1024 / 1024; // MB
        
        echo "内存使用测试: {$memoryUsed}MB (峰值: {$peakMemoryUsed}MB)\n";
        
        // 清理内存
        unset($data);
        
        // 断言内存使用应该在合理范围内
        $this->assertLessThan(100, $peakMemoryUsed, '内存使用过多');
    }
    
    /**
     * 测试大数据量查询性能
     */
    public function testLargeDataQueryPerformance()
    {
        // 检查是否有足够的测试数据
        $userCount = Db::name('users')->count();
        
        if ($userCount < 100) {
            $this->markTestSkipped('测试数据不足，跳过大数据量查询测试');
            return;
        }
        
        $startTime = microtime(true);
        
        // 执行复杂查询
        $result = Db::name('users')
            ->alias('u')
            ->leftJoin('tasks t', 'u.id = t.user_id')
            ->leftJoin('score_logs s', 'u.id = s.user_id')
            ->field('u.id, u.username, COUNT(t.id) as task_count, SUM(s.amount) as total_score')
            ->group('u.id')
            ->order('u.id', 'desc')
            ->limit(50)
            ->select();
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;
        
        echo "大数据量查询性能测试: {$executionTime}ms (返回 " . count($result) . " 条记录)\n";
        
        $this->assertLessThan(2000, $executionTime, '大数据量查询性能过慢');
    }
    
    /**
     * 生成性能报告
     */
    public function testGeneratePerformanceReport()
    {
        $report = [
            'test_time' => date('Y-m-d H:i:s'),
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'database_version' => $this->getDatabaseVersion(),
            'cache_driver' => config('cache.default'),
            'system_info' => [
                'os' => PHP_OS,
                'server' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
            ]
        ];
        
        $reportFile = runtime_path() . 'performance_report_' . date('Ymd_His') . '.json';
        file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        echo "性能报告已生成: {$reportFile}\n";
        
        $this->assertTrue(file_exists($reportFile), '性能报告生成失败');
    }
    
    /**
     * 获取数据库版本
     */
    private function getDatabaseVersion()
    {
        try {
            $result = Db::query('SELECT VERSION() as version');
            return $result[0]['version'] ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
}
