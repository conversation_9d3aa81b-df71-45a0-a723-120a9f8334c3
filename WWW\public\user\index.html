<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心 - SEO积分系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 15px 0;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            font-size: 1.5em;
            color: #667eea;
            font-weight: bold;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .login-form {
            background: white;
            border-radius: 10px;
            padding: 40px;
            max-width: 400px;
            margin: 50px auto;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .dashboard {
            display: none;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card h3 {
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .task-item, .score-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .task-item:last-child, .score-item:last-child {
            border-bottom: none;
        }
        
        .status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .status.running {
            background: #d4edda;
            color: #155724;
        }
        
        .status.completed {
            background: #cce5ff;
            color: #004085;
        }
        
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .error {
            color: #dc3545;
            margin-top: 10px;
            font-size: 0.9em;
        }
        
        .success {
            color: #28a745;
            margin-top: 10px;
            font-size: 0.9em;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .logout-btn:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">SEO积分系统 - 用户中心</div>
            <div class="user-info" id="userInfo" style="display: none;">
                <span id="username"></span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <!-- 登录表单 -->
    <div id="loginForm" class="login-form">
        <h2 style="text-align: center; margin-bottom: 30px; color: #333;">用户登录</h2>
        <form onsubmit="login(event)">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required placeholder="请输入用户名">
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required placeholder="请输入密码">
            </div>
            <button type="submit" class="btn" id="loginBtn">登录</button>
            <div id="loginError" class="error"></div>
        </form>
        <div style="text-align: center; margin-top: 20px; color: #666;">
            <p>测试账号：testuser / password123</p>
            <p><a href="/" style="color: #667eea;">返回首页</a></p>
        </div>
    </div>

    <!-- 用户仪表盘 -->
    <div id="dashboard" class="container dashboard">
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="scoreBalance">0</div>
                <div class="stat-label">积分余额</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalTasks">0</div>
                <div class="stat-label">总任务数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="runningTasks">0</div>
                <div class="stat-label">运行中任务</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="todayConsumption">0</div>
                <div class="stat-label">今日消费</div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-grid">
            <div class="card">
                <div class="tabs">
                    <div class="tab active" onclick="switchTab('tasks')">我的任务</div>
                    <div class="tab" onclick="switchTab('scores')">积分记录</div>
                    <div class="tab" onclick="switchTab('profile')">个人信息</div>
                </div>

                <!-- 任务列表 -->
                <div id="tasksContent" class="tab-content active">
                    <div id="tasksList">
                        <p style="text-align: center; color: #666; padding: 20px;">加载中...</p>
                    </div>
                </div>

                <!-- 积分记录 -->
                <div id="scoresContent" class="tab-content">
                    <div id="scoresList">
                        <p style="text-align: center; color: #666; padding: 20px;">加载中...</p>
                    </div>
                </div>

                <!-- 个人信息 -->
                <div id="profileContent" class="tab-content">
                    <div id="profileInfo">
                        <p style="text-align: center; color: #666; padding: 20px;">加载中...</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>系统通知</h3>
                <div id="notifications">
                    <p style="text-align: center; color: #666; padding: 20px;">暂无通知</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        let authToken = localStorage.getItem('authToken');

        // 页面加载时检查登录状态
        window.onload = function() {
            if (authToken) {
                checkAuth();
            }
        };

        // 登录
        async function login(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const errorDiv = document.getElementById('loginError');
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            errorDiv.textContent = '';
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    authToken = result.data.token;
                    localStorage.setItem('authToken', authToken);
                    showDashboard();
                    loadDashboardData();
                } else {
                    errorDiv.textContent = result.message || '登录失败';
                }
            } catch (error) {
                errorDiv.textContent = '网络错误，请重试';
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        }

        // 检查认证状态
        async function checkAuth() {
            try {
                const response = await fetch(`${API_BASE}/auth/profile`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.status === 200) {
                    showDashboard();
                    loadDashboardData();
                } else {
                    logout();
                }
            } catch (error) {
                logout();
            }
        }

        // 显示仪表盘
        function showDashboard() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('dashboard').style.display = 'block';
            document.getElementById('userInfo').style.display = 'flex';
        }

        // 加载仪表盘数据
        async function loadDashboardData() {
            try {
                const response = await fetch(`${API_BASE}/user/dashboard`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const data = result.data;
                    
                    // 更新统计数据
                    document.getElementById('scoreBalance').textContent = data.user.score_balance;
                    document.getElementById('totalTasks').textContent = data.task_stats.total;
                    document.getElementById('runningTasks').textContent = data.task_stats.running;
                    document.getElementById('todayConsumption').textContent = data.score_stats.today_consumption;
                    document.getElementById('username').textContent = data.user.username;
                    
                    // 加载任务列表
                    loadTasks(data.recent_tasks);
                    
                    // 加载积分记录
                    loadScores(data.recent_scores);
                    
                    // 加载个人信息
                    loadProfile(data.user);
                    
                    // 加载通知
                    loadNotifications(data.notifications);
                }
            } catch (error) {
                console.error('加载数据失败:', error);
            }
        }

        // 加载任务列表
        function loadTasks(tasks) {
            const tasksList = document.getElementById('tasksList');
            
            if (tasks && tasks.length > 0) {
                tasksList.innerHTML = tasks.map(task => `
                    <div class="task-item">
                        <div>
                            <strong>${task.keyword}</strong>
                            <div style="font-size: 0.9em; color: #666;">${task.type === 'keyword_ranking' ? '关键词排名' : '下拉词'}</div>
                        </div>
                        <div class="status ${task.status}">${getStatusText(task.status)}</div>
                    </div>
                `).join('');
            } else {
                tasksList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无任务</p>';
            }
        }

        // 加载积分记录
        function loadScores(scores) {
            const scoresList = document.getElementById('scoresList');
            
            if (scores && scores.length > 0) {
                scoresList.innerHTML = scores.map(score => `
                    <div class="score-item">
                        <div>
                            <strong>${score.description}</strong>
                            <div style="font-size: 0.9em; color: #666;">${new Date(score.create_time * 1000).toLocaleString()}</div>
                        </div>
                        <div style="color: ${score.type === 'recharge' ? '#28a745' : '#dc3545'};">
                            ${score.type === 'recharge' ? '+' : '-'}${score.amount}
                        </div>
                    </div>
                `).join('');
            } else {
                scoresList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无记录</p>';
            }
        }

        // 加载个人信息
        function loadProfile(user) {
            const profileInfo = document.getElementById('profileInfo');
            profileInfo.innerHTML = `
                <div style="line-height: 2;">
                    <p><strong>用户名：</strong>${user.username}</p>
                    <p><strong>昵称：</strong>${user.nickname || '未设置'}</p>
                    <p><strong>用户等级：</strong>${getUserGroupText(user.user_group)}</p>
                    <p><strong>积分余额：</strong>${user.score_balance}</p>
                    <p><strong>总充值：</strong>${user.total_recharge}</p>
                    <p><strong>总消费：</strong>${user.total_consumption}</p>
                    <p><strong>注册时间：</strong>${new Date(user.register_time * 1000).toLocaleString()}</p>
                </div>
            `;
        }

        // 加载通知
        function loadNotifications(notifications) {
            const notificationsDiv = document.getElementById('notifications');
            
            if (notifications && notifications.length > 0) {
                notificationsDiv.innerHTML = notifications.map(notification => `
                    <div class="task-item">
                        <div>
                            <strong>${notification.title}</strong>
                            <div style="font-size: 0.9em; color: #666;">${notification.content}</div>
                        </div>
                        <div style="font-size: 0.8em; color: #999;">
                            ${new Date(notification.create_time * 1000).toLocaleDateString()}
                        </div>
                    </div>
                `).join('');
            } else {
                notificationsDiv.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无通知</p>';
            }
        }

        // 切换标签
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // 激活当前标签
            event.target.classList.add('active');
            document.getElementById(tabName + 'Content').classList.add('active');
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('authToken');
            authToken = null;
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('dashboard').style.display = 'none';
            document.getElementById('userInfo').style.display = 'none';
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待审核',
                'running': '运行中',
                'paused': '已暂停',
                'completed': '已完成',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        // 获取用户组文本
        function getUserGroupText(group) {
            const groupMap = {
                'new_user': '新手用户',
                'regular_user': '普通用户',
                'advanced_user': '高级用户',
                'senior_user': '资深用户',
                'vip_user': 'VIP用户'
            };
            return groupMap[group] || group;
        }
    </script>
</body>
</html>
