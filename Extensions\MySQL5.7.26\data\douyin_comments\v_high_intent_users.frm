TYPE=VIEW
query=select distinct `c`.`user_sec_uid` AS `user_sec_uid`,`c`.`user_nickname` AS `user_nickname`,`c`.`comment_text` AS `comment_text`,`c`.`ip_location` AS `ip_location`,`c`.`message_sent_status` AS `message_sent_status`,`c`.`task_id` AS `task_id`,`c`.`video_id` AS `video_id`,`t`.`video_title` AS `video_title`,`t`.`account_id` AS `account_id`,`a`.`account_name` AS `account_name`,`a`.`sec_uid` AS `account_sec_uid`,`c`.`comment_time` AS `comment_time`,`c`.`collection_time` AS `collection_time` from ((`douyin_comments`.`dy_comments` `c` join `douyin_comments`.`dy_monitor_tasks` `t` on((`c`.`task_id` = `t`.`id`))) join `douyin_comments`.`dy_accounts` `a` on((`t`.`account_id` = `a`.`id`))) where ((`c`.`is_high_intent` = TRUE) and (`c`.`message_sent_status` = 0) and (`a`.`status` = \'active\') and (`c`.`user_sec_uid` <> `a`.`sec_uid`)) order by `c`.`comment_time` desc
md5=40d4a9838caff54d9af44a8e51944823
updatable=0
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=2025-06-25 12:11:06
create-version=1
source=SELECT DISTINCT
\n    c.user_sec_uid,
\n    c.user_nickname,
\n    c.comment_text,
\n    c.ip_location,
\n    c.message_sent_status,
\n    c.task_id,
\n    c.video_id,
\n    t.video_title,
\n    t.account_id,
\n    a.account_name,
\n    a.sec_uid as account_sec_uid,
\n    c.comment_time,
\n    c.collection_time
\nFROM dy_comments c
\nJOIN dy_monitor_tasks t ON c.task_id = t.id
\nJOIN dy_accounts a ON t.account_id = a.id
\nWHERE c.is_high_intent = TRUE
\n  AND c.message_sent_status = 0
\n  AND a.status = \'active\'
\n  AND c.user_sec_uid != a.sec_uid
\nORDER BY c.comment_time DESC
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_general_ci
view_body_utf8=select distinct `c`.`user_sec_uid` AS `user_sec_uid`,`c`.`user_nickname` AS `user_nickname`,`c`.`comment_text` AS `comment_text`,`c`.`ip_location` AS `ip_location`,`c`.`message_sent_status` AS `message_sent_status`,`c`.`task_id` AS `task_id`,`c`.`video_id` AS `video_id`,`t`.`video_title` AS `video_title`,`t`.`account_id` AS `account_id`,`a`.`account_name` AS `account_name`,`a`.`sec_uid` AS `account_sec_uid`,`c`.`comment_time` AS `comment_time`,`c`.`collection_time` AS `collection_time` from ((`douyin_comments`.`dy_comments` `c` join `douyin_comments`.`dy_monitor_tasks` `t` on((`c`.`task_id` = `t`.`id`))) join `douyin_comments`.`dy_accounts` `a` on((`t`.`account_id` = `a`.`id`))) where ((`c`.`is_high_intent` = TRUE) and (`c`.`message_sent_status` = 0) and (`a`.`status` = \'active\') and (`c`.`user_sec_uid` <> `a`.`sec_uid`)) order by `c`.`comment_time` desc
