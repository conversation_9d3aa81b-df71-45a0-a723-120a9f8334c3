TYPE=VIEW
query=select `douyin_comments`.`comments`.`video_id` AS `video_id`,`douyin_comments`.`comments`.`comment_type` AS `comment_type`,`douyin_comments`.`comments`.`cid` AS `cid`,`douyin_comments`.`comments`.`nickname` AS `nickname`,substr(`douyin_comments`.`comments`.`text`,1,100) AS `text_preview`,`douyin_comments`.`comments`.`digg_count` AS `digg_count`,`douyin_comments`.`comments`.`reply_comment_total` AS `reply_comment_total`,`douyin_comments`.`comments`.`collection_time` AS `collection_time`,`douyin_comments`.`comments`.`ip_label` AS `ip_label`,(case when (`douyin_comments`.`comments`.`digg_count` >= 1000) then \'超热门\' when (`douyin_comments`.`comments`.`digg_count` >= 100) then \'热门\' when (`douyin_comments`.`comments`.`digg_count` >= 10) then \'受欢迎\' else \'普通\' end) AS `popularity_level` from `douyin_comments`.`comments` where (`douyin_comments`.`comments`.`digg_count` > 0) order by `douyin_comments`.`comments`.`digg_count` desc
md5=a4a3814487d0fd151d3db2fcb552f5a1
updatable=1
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=2025-06-23 12:49:47
create-version=1
source=SELECT 
\n    video_id,
\n    comment_type,
\n    cid,
\n    nickname,
\n    SUBSTRING(text, 1, 100) as text_preview,
\n    digg_count,
\n    reply_comment_total,
\n    collection_time,
\n    ip_label,
\n    CASE 
\n        WHEN digg_count >= 1000 THEN \'超热门\'
\n        WHEN digg_count >= 100 THEN \'热门\'
\n        WHEN digg_count >= 10 THEN \'受欢迎\'
\n        ELSE \'普通\'
\n    END as popularity_level
\nFROM comments 
\nWHERE digg_count > 0
\nORDER BY digg_count DESC
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_general_ci
view_body_utf8=select `douyin_comments`.`comments`.`video_id` AS `video_id`,`douyin_comments`.`comments`.`comment_type` AS `comment_type`,`douyin_comments`.`comments`.`cid` AS `cid`,`douyin_comments`.`comments`.`nickname` AS `nickname`,substr(`douyin_comments`.`comments`.`text`,1,100) AS `text_preview`,`douyin_comments`.`comments`.`digg_count` AS `digg_count`,`douyin_comments`.`comments`.`reply_comment_total` AS `reply_comment_total`,`douyin_comments`.`comments`.`collection_time` AS `collection_time`,`douyin_comments`.`comments`.`ip_label` AS `ip_label`,(case when (`douyin_comments`.`comments`.`digg_count` >= 1000) then \'超热门\' when (`douyin_comments`.`comments`.`digg_count` >= 100) then \'热门\' when (`douyin_comments`.`comments`.`digg_count` >= 10) then \'受欢迎\' else \'普通\' end) AS `popularity_level` from `douyin_comments`.`comments` where (`douyin_comments`.`comments`.`digg_count` > 0) order by `douyin_comments`.`comments`.`digg_count` desc
