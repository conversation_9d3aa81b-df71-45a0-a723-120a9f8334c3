<?php

namespace app\validate;

use think\Validate;

/**
 * 用户验证器
 */
class UserValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'mobile' => 'require|mobile|length:11',
        'password' => 'require|length:6,20',
        'old_password' => 'require|length:6,20',
        'new_password' => 'require|length:6,20|different:old_password',
        'confirm_password' => 'require|confirm:new_password',
        'nickname' => 'length:2,20|chsAlphaNum',
        'avatar' => 'url',
        'code' => 'require|number|length:4,6'
    ];

    /**
     * 错误消息
     */
    protected $message = [
        'mobile.require' => '手机号不能为空',
        'mobile.mobile' => '手机号格式不正确',
        'mobile.length' => '手机号长度必须为11位',
        'password.require' => '密码不能为空',
        'password.length' => '密码长度必须在6-20位之间',
        'old_password.require' => '原密码不能为空',
        'old_password.length' => '原密码长度必须在6-20位之间',
        'new_password.require' => '新密码不能为空',
        'new_password.length' => '新密码长度必须在6-20位之间',
        'new_password.different' => '新密码不能与原密码相同',
        'confirm_password.require' => '确认密码不能为空',
        'confirm_password.confirm' => '确认密码与新密码不一致',
        'nickname.length' => '昵称长度必须在2-20位之间',
        'nickname.chsAlphaNum' => '昵称只能包含中文、字母和数字',
        'avatar.url' => '头像必须是有效的URL地址',
        'code.require' => '验证码不能为空',
        'code.number' => '验证码必须是数字',
        'code.length' => '验证码长度必须在4-6位之间'
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'register' => ['mobile', 'password', 'nickname'],
        'login' => ['mobile', 'password'],
        'update' => ['nickname', 'avatar'],
        'changePassword' => ['old_password', 'new_password', 'confirm_password'],
        'resetPassword' => ['mobile', 'code', 'new_password', 'confirm_password'],
        'sendCode' => ['mobile']
    ];

    /**
     * 自定义验证规则：手机号格式
     */
    protected function mobile($value, $rule, $data = [])
    {
        return preg_match('/^1[3-9]\d{9}$/', $value);
    }

    /**
     * 自定义验证规则：密码强度
     */
    protected function strongPassword($value, $rule, $data = [])
    {
        // 密码必须包含字母和数字
        if (!preg_match('/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{6,20}$/', $value)) {
            return '密码必须包含字母和数字，长度6-20位';
        }
        return true;
    }

    /**
     * 自定义验证规则：昵称唯一性
     */
    protected function uniqueNickname($value, $rule, $data = [])
    {
        $userId = $data['user_id'] ?? 0;
        
        $count = \think\facade\Db::name('users')
            ->where('nickname', $value)
            ->where('id', '<>', $userId)
            ->count();
            
        return $count == 0 ? true : '昵称已被使用';
    }

    /**
     * 自定义验证规则：手机号唯一性
     */
    protected function uniqueMobile($value, $rule, $data = [])
    {
        $userId = $data['user_id'] ?? 0;
        $mobileHash = hash('sha256', $value . config('app.salt', ''));
        
        $count = \think\facade\Db::name('users')
            ->where('mobile_hash', $mobileHash)
            ->where('id', '<>', $userId)
            ->count();
            
        return $count == 0 ? true : '手机号已被注册';
    }

    /**
     * 验证注册场景
     */
    public function sceneRegister()
    {
        return $this->only(['mobile', 'password', 'nickname'])
                   ->append('mobile', 'uniqueMobile')
                   ->append('nickname', 'uniqueNickname');
    }

    /**
     * 验证登录场景
     */
    public function sceneLogin()
    {
        return $this->only(['mobile', 'password']);
    }

    /**
     * 验证更新场景
     */
    public function sceneUpdate()
    {
        return $this->only(['nickname', 'avatar'])
                   ->append('nickname', 'uniqueNickname');
    }

    /**
     * 验证修改密码场景
     */
    public function sceneChangePassword()
    {
        return $this->only(['old_password', 'new_password', 'confirm_password'])
                   ->append('new_password', 'strongPassword');
    }

    /**
     * 验证重置密码场景
     */
    public function sceneResetPassword()
    {
        return $this->only(['mobile', 'code', 'new_password', 'confirm_password'])
                   ->append('new_password', 'strongPassword');
    }

    /**
     * 验证发送验证码场景
     */
    public function sceneSendCode()
    {
        return $this->only(['mobile']);
    }
}
