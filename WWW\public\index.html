<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO积分系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            font-size: 2.5em;
            color: #667eea;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .feature h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #666;
            font-size: 0.9em;
        }
        
        .buttons {
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            margin: 0 10px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #5a6268;
        }
        
        .status {
            margin-top: 30px;
            padding: 15px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            color: #155724;
        }
        
        .api-info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            text-align: left;
        }
        
        .api-info h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .api-info code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀 SEO积分系统</div>
        <div class="subtitle">专业的SEO优化积分管理平台</div>
        
        <div class="features">
            <div class="feature">
                <h3>关键词排名</h3>
                <p>智能关键词排名优化，提升网站搜索引擎排名</p>
            </div>
            <div class="feature">
                <h3>下拉词优化</h3>
                <p>搜索引擎下拉词推广，增加品牌曝光度</p>
            </div>
            <div class="feature">
                <h3>积分管理</h3>
                <p>灵活的积分充值消费系统，透明的费用结算</p>
            </div>
            <div class="feature">
                <h3>数据统计</h3>
                <p>详细的任务执行报告和效果分析</p>
            </div>
        </div>
        
        <div class="buttons">
            <a href="/user/" class="btn">用户中心</a>
            <a href="/admin/" class="btn secondary">管理后台</a>
        </div>
        
        <div class="status">
            <strong>系统状态：</strong> 正常运行中 ✅
        </div>
        
        <div class="api-info">
            <h4>API接口地址：</h4>
            <p><strong>用户API：</strong> <code>http://localhost/api/</code></p>
            <p><strong>管理API：</strong> <code>http://localhost/api/admin/</code></p>
            <p><strong>健康检查：</strong> <code>http://localhost/health</code></p>
            <p><strong>系统配置：</strong> <code>http://localhost/api/system/config</code></p>
        </div>
        
        <div class="api-info">
            <h4>默认账号信息：</h4>
            <p><strong>管理员账号：</strong> admin / admin123456</p>
            <p><strong>测试用户：</strong> testuser / password123</p>
            <p><strong>数据库：</strong> seo_points_system</p>
        </div>
        
        <div class="footer">
            <p>SEO积分系统 v1.0.0 | 基于ThinkPHP 8.0开发</p>
            <p>请先导入数据库文件：<code>/database/install.sql</code></p>
        </div>
    </div>
    
    <script>
        // 检查API状态
        fetch('/health')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'ok') {
                    console.log('API服务正常');
                } else {
                    console.log('API服务异常');
                }
            })
            .catch(error => {
                console.log('API连接失败:', error);
            });
    </script>
</body>
</html>
