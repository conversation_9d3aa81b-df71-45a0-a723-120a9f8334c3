<?php

namespace app\validate;

use think\Validate;

/**
 * 积分验证器
 */
class ScoreValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'amount' => 'require|float|gt:0|max:10000',
        'payment_method' => 'require|in:alipay,wechat,bank',
        'description' => 'length:0,200',
        'task_id' => 'integer|gt:0',
        'user_id' => 'require|integer|gt:0',
        'type' => 'require|in:1,2,3,4,5',
        'admin_id' => 'integer|gt:0'
    ];

    /**
     * 错误消息
     */
    protected $message = [
        'amount.require' => '金额不能为空',
        'amount.float' => '金额必须是数字',
        'amount.gt' => '金额必须大于0',
        'amount.max' => '单次充值金额不能超过10000',
        'payment_method.require' => '支付方式不能为空',
        'payment_method.in' => '支付方式无效',
        'description.length' => '描述长度不能超过200字符',
        'task_id.integer' => '任务ID必须是整数',
        'task_id.gt' => '任务ID必须大于0',
        'user_id.require' => '用户ID不能为空',
        'user_id.integer' => '用户ID必须是整数',
        'user_id.gt' => '用户ID必须大于0',
        'type.require' => '积分类型不能为空',
        'type.in' => '积分类型无效',
        'admin_id.integer' => '管理员ID必须是整数',
        'admin_id.gt' => '管理员ID必须大于0'
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'recharge' => ['amount', 'payment_method'],
        'consume' => ['user_id', 'task_id', 'amount', 'description'],
        'refund' => ['user_id', 'task_id', 'amount', 'description', 'admin_id'],
        'reward' => ['user_id', 'amount', 'description', 'admin_id'],
        'deduct' => ['user_id', 'amount', 'description', 'admin_id'],
        'admin_recharge' => ['user_id', 'amount', 'description', 'admin_id']
    ];

    /**
     * 充值场景
     */
    public function sceneRecharge()
    {
        return $this->only(['amount', 'payment_method'])
                   ->append('amount', 'checkRechargeAmount');
    }

    /**
     * 消费场景
     */
    public function sceneConsume()
    {
        return $this->only(['user_id', 'task_id', 'amount', 'description'])
                   ->append('user_id', 'checkUserExists')
                   ->append('task_id', 'checkTaskExists')
                   ->append('amount', 'checkUserBalance');
    }

    /**
     * 退款场景
     */
    public function sceneRefund()
    {
        return $this->only(['user_id', 'task_id', 'amount', 'description', 'admin_id'])
                   ->append('user_id', 'checkUserExists')
                   ->append('task_id', 'checkTaskExists')
                   ->append('admin_id', 'checkAdminExists');
    }

    /**
     * 奖励场景
     */
    public function sceneReward()
    {
        return $this->only(['user_id', 'amount', 'description', 'admin_id'])
                   ->append('user_id', 'checkUserExists')
                   ->append('admin_id', 'checkAdminExists');
    }

    /**
     * 扣除场景
     */
    public function sceneDeduct()
    {
        return $this->only(['user_id', 'amount', 'description', 'admin_id'])
                   ->append('user_id', 'checkUserExists')
                   ->append('amount', 'checkUserBalance')
                   ->append('admin_id', 'checkAdminExists');
    }

    /**
     * 管理员充值场景
     */
    public function sceneAdminRecharge()
    {
        return $this->only(['user_id', 'amount', 'description', 'admin_id'])
                   ->append('user_id', 'checkUserExists')
                   ->append('admin_id', 'checkAdminExists');
    }

    /**
     * 自定义验证：检查充值金额
     */
    protected function checkRechargeAmount($value, $rule, $data = [])
    {
        // 检查最小充值金额
        $minAmount = config('system.min_recharge_amount', 10);
        if ($value < $minAmount) {
            return "最小充值金额为{$minAmount}元";
        }

        // 检查最大充值金额
        $maxAmount = config('system.max_recharge_amount', 10000);
        if ($value > $maxAmount) {
            return "最大充值金额为{$maxAmount}元";
        }

        return true;
    }

    /**
     * 自定义验证：检查用户是否存在
     */
    protected function checkUserExists($value, $rule, $data = [])
    {
        $user = \think\facade\Db::name('users')->where('id', $value)->find();
        
        if (!$user) {
            return '用户不存在';
        }

        if ($user['status'] != 1) {
            return '用户状态异常';
        }

        return true;
    }

    /**
     * 自定义验证：检查任务是否存在
     */
    protected function checkTaskExists($value, $rule, $data = [])
    {
        if (empty($value)) {
            return true; // 任务ID可以为空
        }

        $task = \think\facade\Db::name('tasks')->where('id', $value)->find();
        
        if (!$task) {
            return '任务不存在';
        }

        // 如果指定了用户ID，检查任务是否属于该用户
        if (isset($data['user_id']) && $task['user_id'] != $data['user_id']) {
            return '任务不属于指定用户';
        }

        return true;
    }

    /**
     * 自定义验证：检查管理员是否存在
     */
    protected function checkAdminExists($value, $rule, $data = [])
    {
        if (empty($value)) {
            return true; // 管理员ID可以为空
        }

        $admin = \think\facade\Db::name('admins')->where('id', $value)->find();
        
        if (!$admin) {
            return '管理员不存在';
        }

        if ($admin['status'] != 1) {
            return '管理员状态异常';
        }

        return true;
    }

    /**
     * 自定义验证：检查用户余额是否足够
     */
    protected function checkUserBalance($value, $rule, $data = [])
    {
        if (!isset($data['user_id'])) {
            return true; // 如果没有用户ID，跳过检查
        }

        $user = \think\facade\Db::name('users')->where('id', $data['user_id'])->find();
        
        if (!$user) {
            return '用户不存在';
        }

        if ($user['score'] < $value) {
            return '用户积分余额不足';
        }

        return true;
    }

    /**
     * 自定义验证：检查充值频率限制
     */
    protected function checkRechargeFrequency($value, $rule, $data = [])
    {
        $userId = $data['user_id'] ?? 0;
        
        if (empty($userId)) {
            return true;
        }

        // 检查1小时内充值次数
        $hourStart = time() - 3600;
        $count = \think\facade\Db::name('score_logs')
            ->where('user_id', $userId)
            ->where('type', \app\service\ScoreService::TYPE_RECHARGE)
            ->where('create_time', '>=', $hourStart)
            ->count();

        $maxHourlyRecharge = config('system.max_hourly_recharge', 5);
        if ($count >= $maxHourlyRecharge) {
            return "1小时内最多充值{$maxHourlyRecharge}次";
        }

        // 检查每日充值金额
        $dayStart = strtotime(date('Y-m-d'));
        $dailyAmount = \think\facade\Db::name('score_logs')
            ->where('user_id', $userId)
            ->where('type', \app\service\ScoreService::TYPE_RECHARGE)
            ->where('create_time', '>=', $dayStart)
            ->sum('amount');

        $maxDailyAmount = config('system.max_daily_recharge_amount', 50000);
        if (($dailyAmount + $value) > $maxDailyAmount) {
            return "每日充值金额不能超过{$maxDailyAmount}元";
        }

        return true;
    }

    /**
     * 自定义验证：检查退款金额是否合理
     */
    protected function checkRefundAmount($value, $rule, $data = [])
    {
        if (!isset($data['task_id']) || empty($data['task_id'])) {
            return true; // 如果没有任务ID，跳过检查
        }

        $task = \think\facade\Db::name('tasks')->where('id', $data['task_id'])->find();
        
        if (!$task) {
            return '任务不存在';
        }

        // 检查退款金额是否超过预扣金额
        if ($value > $task['pre_deduct_score']) {
            return '退款金额不能超过预扣金额';
        }

        // 检查是否已经有退款记录
        $refundAmount = \think\facade\Db::name('score_logs')
            ->where('task_id', $data['task_id'])
            ->where('type', \app\service\ScoreService::TYPE_REFUND)
            ->sum('amount');

        if (($refundAmount + $value) > $task['pre_deduct_score']) {
            return '累计退款金额不能超过预扣金额';
        }

        return true;
    }
}
