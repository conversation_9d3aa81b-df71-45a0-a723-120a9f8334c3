# SEO积分系统 - 部署运维指南

## 🚀 部署环境准备

### 服务器配置要求
```yaml
生产环境推荐配置:
  CPU: 8核心 2.4GHz+
  内存: 16GB+
  硬盘: SSD 200GB+
  带宽: 20Mbps+
  操作系统: CentOS 7+ / Ubuntu 18.04+

测试环境最低配置:
  CPU: 4核心
  内存: 8GB
  硬盘: SSD 100GB
  带宽: 10Mbps
```

### 软件环境安装
```bash
# 1. 更新系统
sudo yum update -y  # CentOS
# sudo apt update && sudo apt upgrade -y  # Ubuntu

# 2. 安装基础软件
sudo yum install -y wget curl git vim unzip

# 3. 安装PHP 8.1
sudo yum install -y epel-release
sudo yum install -y https://rpms.remirepo.net/enterprise/remi-release-7.rpm
sudo yum-config-manager --enable remi-php81
sudo yum install -y php php-fpm php-mysql php-redis php-json php-mbstring php-xml php-curl php-zip php-gd

# 4. 安装MySQL 8.0
sudo yum install -y mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld
sudo mysql_secure_installation

# 5. 安装Redis
sudo yum install -y redis
sudo systemctl start redis
sudo systemctl enable redis

# 6. 安装Nginx
sudo yum install -y nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 7. 安装Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

## 📦 应用部署

### 1. 代码部署
```bash
# 创建项目目录
sudo mkdir -p /var/www/seo-system
cd /var/www/seo-system

# 克隆代码（或上传代码包）
git clone https://github.com/your-repo/seo-score-system.git .

# 安装依赖
composer install --no-dev --optimize-autoloader

# 设置权限
sudo chown -R nginx:nginx /var/www/seo-system
sudo chmod -R 755 /var/www/seo-system
sudo chmod -R 777 /var/www/seo-system/runtime
sudo chmod -R 777 /var/www/seo-system/public/uploads
```

### 2. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

```env
# .env 生产环境配置
APP_DEBUG=false
APP_TRACE=false

# 数据库配置
DATABASE_TYPE=mysql
DATABASE_HOSTNAME=127.0.0.1
DATABASE_DATABASE=seo_score_system
DATABASE_USERNAME=seo_user
DATABASE_PASSWORD=your_strong_password
DATABASE_HOSTPORT=3306
DATABASE_CHARSET=utf8mb4

# Redis配置
REDIS_HOSTNAME=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_SELECT=0

# 安全配置
JWT_SECRET=your_jwt_secret_key_32_characters
APP_ENCRYPT_KEY=your_encrypt_key_32_characters
APP_SALT=your_salt_string

# 文件上传
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_EXT=jpg,jpeg,png,gif,pdf,doc,docx

# 短信配置
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY=your_access_key
SMS_ACCESS_SECRET=your_access_secret
SMS_SIGN_NAME=your_sign_name

# 支付配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_private_key
ALIPAY_PUBLIC_KEY=alipay_public_key
```

### 3. 数据库初始化
```bash
# 创建数据库
mysql -u root -p
```

```sql
-- 创建数据库
CREATE DATABASE seo_score_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'seo_user'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON seo_score_system.* TO 'seo_user'@'localhost';
FLUSH PRIVILEGES;
```

```bash
# 导入数据库结构
mysql -u seo_user -p seo_score_system < database/install.sql

# 运行数据迁移
php think migrate:run

# 初始化基础数据
php think seed:run
```

### 4. Nginx配置
```nginx
# /etc/nginx/conf.d/seo-system.conf
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    root /var/www/seo-system/public;
    index index.php index.html;
    
    # SSL证书配置
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # 安全头设置
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 限制请求大小
    client_max_body_size 10M;
    client_body_buffer_size 128k;
    
    # 隐藏服务器信息
    server_tokens off;
    
    # 防止访问敏感文件
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(env|log|ini|sql)$ {
        deny all;
    }
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        fastcgi_hide_header X-Powered-By;
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # API路由
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # 主要路由
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # 日志配置
    access_log /var/log/nginx/seo_access.log;
    error_log /var/log/nginx/seo_error.log;
}
```

### 5. PHP-FPM配置
```ini
# /etc/php-fpm.d/www.conf
[www]
user = nginx
group = nginx
listen = 127.0.0.1:9000
listen.owner = nginx
listen.group = nginx
listen.mode = 0660

pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 1000

php_admin_value[error_log] = /var/log/php-fpm/www-error.log
php_admin_flag[log_errors] = on
php_value[session.save_handler] = files
php_value[session.save_path] = /var/lib/php/session
php_value[soap.wsdl_cache_dir] = /var/lib/php/wsdlcache

# 安全设置
php_admin_value[disable_functions] = exec,passthru,shell_exec,system,proc_open,popen
php_admin_value[open_basedir] = /var/www/seo-system:/tmp
```

## ⏰ 定时任务配置

### Crontab设置
```bash
# 编辑定时任务
crontab -e

# 添加以下任务
# 每日凌晨1点更新任务执行数据
0 1 * * * cd /var/www/seo-system && php think task:update-daily >> /var/log/cron.log 2>&1

# 每日凌晨2点检查过期任务
0 2 * * * cd /var/www/seo-system && php think task:check-expired >> /var/log/cron.log 2>&1

# 每日凌晨3点处理积分退款
0 3 * * * cd /var/www/seo-system && php think score:process-refunds >> /var/log/cron.log 2>&1

# 每小时清理过期缓存
0 * * * * cd /var/www/seo-system && php think cache:clear-expired >> /var/log/cron.log 2>&1

# 每10分钟系统健康检查
*/10 * * * * cd /var/www/seo-system && php think system:health-check >> /var/log/cron.log 2>&1

# 每月1号凌晨4点维护数据库分区
0 4 1 * * cd /var/www/seo-system && php think partition:maintenance >> /var/log/cron.log 2>&1
```

## 📊 监控配置

### 1. 系统监控脚本
```bash
#!/bin/bash
# /usr/local/bin/system-monitor.sh

LOG_FILE="/var/log/system-monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查服务状态
check_service() {
    local service=$1
    if systemctl is-active --quiet $service; then
        echo "[$DATE] $service: OK" >> $LOG_FILE
    else
        echo "[$DATE] $service: FAILED" >> $LOG_FILE
        # 发送告警
        curl -X POST "https://your-webhook-url" \
             -H "Content-Type: application/json" \
             -d "{\"text\":\"服务异常: $service 已停止\"}"
    fi
}

# 检查磁盘空间
check_disk() {
    local usage=$(df -h / | awk 'NR==2{print $5}' | sed 's/%//')
    if [ $usage -gt 80 ]; then
        echo "[$DATE] Disk usage: ${usage}% - WARNING" >> $LOG_FILE
        curl -X POST "https://your-webhook-url" \
             -H "Content-Type: application/json" \
             -d "{\"text\":\"磁盘空间不足: ${usage}%\"}"
    else
        echo "[$DATE] Disk usage: ${usage}% - OK" >> $LOG_FILE
    fi
}

# 检查内存使用
check_memory() {
    local usage=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
    if [ $usage -gt 90 ]; then
        echo "[$DATE] Memory usage: ${usage}% - WARNING" >> $LOG_FILE
        curl -X POST "https://your-webhook-url" \
             -H "Content-Type: application/json" \
             -d "{\"text\":\"内存使用过高: ${usage}%\"}"
    else
        echo "[$DATE] Memory usage: ${usage}% - OK" >> $LOG_FILE
    fi
}

# 执行检查
check_service nginx
check_service php-fpm
check_service mysql
check_service redis
check_disk
check_memory
```

### 2. 应用监控
```php
<?php
// app/command/HealthCheckCommand.php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Cache;

class HealthCheckCommand extends Command
{
    protected function configure()
    {
        $this->setName('system:health-check')
             ->setDescription('System health check');
    }
    
    protected function execute(Input $input, Output $output)
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'redis' => $this->checkRedis(),
            'disk_space' => $this->checkDiskSpace(),
            'queue' => $this->checkQueue(),
        ];
        
        $allHealthy = true;
        foreach ($checks as $name => $result) {
            if (!$result['status']) {
                $allHealthy = false;
                $this->sendAlert($name, $result['message']);
            }
            $output->writeln("{$name}: " . ($result['status'] ? 'OK' : 'FAILED'));
        }
        
        if ($allHealthy) {
            $output->writeln('All systems healthy');
        }
    }
    
    private function checkDatabase()
    {
        try {
            Db::query('SELECT 1');
            return ['status' => true, 'message' => 'Database connection OK'];
        } catch (\Exception $e) {
            return ['status' => false, 'message' => 'Database connection failed: ' . $e->getMessage()];
        }
    }
    
    private function checkRedis()
    {
        try {
            Cache::set('health_check', time(), 60);
            $value = Cache::get('health_check');
            return ['status' => true, 'message' => 'Redis connection OK'];
        } catch (\Exception $e) {
            return ['status' => false, 'message' => 'Redis connection failed: ' . $e->getMessage()];
        }
    }
    
    private function checkDiskSpace()
    {
        $freeBytes = disk_free_space('/');
        $totalBytes = disk_total_space('/');
        $usedPercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;
        
        if ($usedPercent > 90) {
            return ['status' => false, 'message' => "Disk usage: {$usedPercent}%"];
        }
        
        return ['status' => true, 'message' => "Disk usage: {$usedPercent}%"];
    }
    
    private function checkQueue()
    {
        // 检查队列积压情况
        $queueLength = Cache::lLen('queue:default');
        if ($queueLength > 1000) {
            return ['status' => false, 'message' => "Queue backlog: {$queueLength}"];
        }
        
        return ['status' => true, 'message' => "Queue length: {$queueLength}"];
    }
    
    private function sendAlert($component, $message)
    {
        // 发送告警通知
        $webhook = config('monitor.webhook_url');
        if ($webhook) {
            $data = [
                'text' => "系统异常: {$component} - {$message}",
                'timestamp' => time()
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $webhook);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_exec($ch);
            curl_close($ch);
        }
    }
}
```

## 🔧 维护操作

### 日志管理
```bash
# 配置日志轮转
sudo vim /etc/logrotate.d/seo-system

# 内容如下:
/var/www/seo-system/runtime/log/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 nginx nginx
    postrotate
        /usr/bin/systemctl reload nginx > /dev/null 2>&1 || true
    endscript
}

/var/log/nginx/seo_*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 nginx nginx
    postrotate
        /usr/bin/systemctl reload nginx > /dev/null 2>&1 || true
    endscript
}
```

### 数据库维护
```sql
-- 定期清理过期数据
DELETE FROM task_execution_logs WHERE create_time < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 3 MONTH));
DELETE FROM user_login_logs WHERE login_time < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 6 MONTH));
DELETE FROM security_logs WHERE create_time < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH));

-- 优化表
OPTIMIZE TABLE tasks, score_logs, users, user_login_logs;

-- 分析表统计信息
ANALYZE TABLE tasks, score_logs, users;
```

### 备份策略
```bash
#!/bin/bash
# /usr/local/bin/backup.sh

BACKUP_DIR="/backup/seo-system"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="seo_score_system"
DB_USER="seo_user"
DB_PASS="your_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/db_$DATE.sql.gz

# 代码备份
tar -czf $BACKUP_DIR/code_$DATE.tar.gz -C /var/www seo-system

# 清理7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

---

**运维要点**:
1. **监控告警**: 建立完善的监控体系，及时发现问题
2. **日志管理**: 合理配置日志轮转，避免磁盘空间不足
3. **定期备份**: 制定备份策略，确保数据安全
4. **性能优化**: 定期检查和优化数据库性能
5. **安全更新**: 及时更新系统和软件补丁
6. **容量规划**: 根据业务增长预估资源需求
