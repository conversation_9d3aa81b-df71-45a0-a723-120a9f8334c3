server {
    listen 80;
    server_name localhost;
    root d:/phpstudy_pro/WWW/public;
    index index.php index.html index.htm;

    # 字符集
    charset utf-8;

    # 访问日志
    access_log d:/phpstudy_pro/Extensions/Nginx1.15.11/logs/seo_access.log;
    error_log d:/phpstudy_pro/Extensions/Nginx1.15.11/logs/seo_error.log;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 隐藏Nginx版本
    server_tokens off;

    # 文件上传大小限制
    client_max_body_size 10M;

    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /(config|database|runtime|vendor|tests|\.env|composer\.|\.git) {
        deny all;
        access_log off;
        log_not_found off;
    }

    # API接口路由
    location /api/auth/login {
        limit_req zone=login burst=3 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }

    location /api/ {
        limit_req zone=api burst=20 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 管理后台路由
    location /admin/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 用户后台路由
    location /user/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # WebHook路由
    location /webhook/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 开放API路由
    location /open-api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 健康检查
    location /health {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 主要路由规则
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # PHP安全设置
        fastcgi_param PHP_VALUE "open_basedir=d:/phpstudy_pro/WWW/";
        fastcgi_hide_header X-Powered-By;
        
        # 超时设置
        fastcgi_connect_timeout 60s;
        fastcgi_send_timeout 60s;
        fastcgi_read_timeout 60s;
        
        # 缓冲设置
        fastcgi_buffer_size 64k;
        fastcgi_buffers 4 64k;
        fastcgi_busy_buffers_size 128k;
    }

    # 防止直接访问PHP文件
    location ~ ^/(app|config|database|runtime|vendor|tests)/.+\.php$ {
        deny all;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root d:/phpstudy_pro/WWW/public;
        internal;
    }
    
    location = /50x.html {
        root d:/phpstudy_pro/WWW/public;
        internal;
    }

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 限制请求方法
    if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE|OPTIONS)$ ) {
        return 405;
    }

    # 防止SQL注入和XSS攻击
    if ($args ~ "union.*select.*\(") { return 403; }
    if ($args ~ "union.*all.*select.*") { return 403; }
    if ($args ~ "concat.*\(") { return 403; }
    if ($query_string ~ "(<|%3C).*script.*(>|%3E)") { return 403; }
    if ($query_string ~ "GLOBALS(=|\[|\%[0-9A-Z]{0,2})") { return 403; }
    if ($query_string ~ "_REQUEST(=|\[|\%[0-9A-Z]{0,2})") { return 403; }
    if ($query_string ~ "proc/self/environ") { return 403; }
    if ($query_string ~ "mosConfig_[a-zA-Z_]{1,21}(=|\%3D)") { return 403; }
    if ($query_string ~ "base64_(en|de)code\(.*\)") { return 403; }

    # 限制User-Agent
    if ($http_user_agent ~* "sqlmap|nikto|wikto|sf|sqlninja|fimap|nessus|whatweb|Openvas|jbrofuzz|libwhisker|webshag") {
        return 403;
    }


}
