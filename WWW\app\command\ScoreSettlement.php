<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\service\ScoreService;
use app\service\TaskService;

/**
 * 积分结算命令
 */
class ScoreSettlement extends Command
{
    protected function configure()
    {
        $this->setName('score:settlement')
            ->setDescription('积分结算处理');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始积分结算处理...');
        
        try {
            // 处理预扣积分结算
            $this->processPreDeductSettlement($output);
            
            // 处理过期任务退款
            $this->processExpiredTaskRefund($output);
            
            // 处理用户等级升级
            $this->processUserLevelUpgrade($output);
            
            // 生成积分统计报告
            $this->generateScoreReport($output);
            
            $output->writeln('积分结算处理完成');
            
        } catch (\Exception $e) {
            $output->writeln('积分结算失败：' . $e->getMessage());
        }
    }
    
    /**
     * 处理预扣积分结算
     */
    private function processPreDeductSettlement(Output $output)
    {
        $output->writeln('处理预扣积分结算...');
        
        // 获取已完成但未结算的任务
        $tasks = Db::name('tasks')
            ->where('status', TaskService::STATUS_COMPLETED)
            ->where('settlement_status', 0)
            ->select();
        
        $settledCount = 0;
        $totalRefund = 0;
        
        foreach ($tasks as $task) {
            try {
                // 计算实际消费积分
                $actualCost = $this->calculateActualCost($task);
                
                // 如果预扣积分大于实际消费，退还差额
                if ($task['pre_deduct_score'] > $actualCost) {
                    $refundAmount = $task['pre_deduct_score'] - $actualCost;
                    
                    $result = ScoreService::refund(
                        $task['user_id'], 
                        $refundAmount, 
                        "任务完成结算退款 - 任务ID: {$task['id']}", 
                        $task['id']
                    );
                    
                    if ($result) {
                        $totalRefund += $refundAmount;
                    }
                }
                
                // 更新任务结算状态
                Db::name('tasks')
                    ->where('id', $task['id'])
                    ->update([
                        'actual_cost_score' => $actualCost,
                        'settlement_status' => 1,
                        'settlement_time' => time(),
                        'update_time' => time()
                    ]);
                
                $settledCount++;
                
            } catch (\Exception $e) {
                $output->writeln("任务 #{$task['id']} 结算失败: " . $e->getMessage());
            }
        }
        
        $output->writeln("结算了 {$settledCount} 个任务，退还积分 {$totalRefund}");
    }
    
    /**
     * 处理过期任务退款
     */
    private function processExpiredTaskRefund(Output $output)
    {
        $output->writeln('处理过期任务退款...');
        
        // 获取超过7天未执行的运行中任务
        $expireTime = time() - (7 * 24 * 3600);
        
        $expiredTasks = Db::name('tasks')
            ->where('status', TaskService::STATUS_RUNNING)
            ->where('create_time', '<', $expireTime)
            ->where('last_execute_time', 0) // 从未执行过
            ->select();
        
        $refundCount = 0;
        $totalRefund = 0;
        
        foreach ($expiredTasks as $task) {
            try {
                // 退还预扣积分
                $result = ScoreService::refund(
                    $task['user_id'], 
                    $task['pre_deduct_score'], 
                    "过期任务退款 - 任务ID: {$task['id']}", 
                    $task['id']
                );
                
                if ($result) {
                    // 更新任务状态为已取消
                    Db::name('tasks')
                        ->where('id', $task['id'])
                        ->update([
                            'status' => TaskService::STATUS_CANCELLED,
                            'update_time' => time(),
                            'remark' => '任务过期自动取消'
                        ]);
                    
                    $totalRefund += $task['pre_deduct_score'];
                    $refundCount++;
                }
                
            } catch (\Exception $e) {
                $output->writeln("过期任务 #{$task['id']} 退款失败: " . $e->getMessage());
            }
        }
        
        $output->writeln("处理了 {$refundCount} 个过期任务，退还积分 {$totalRefund}");
    }
    
    /**
     * 处理用户等级升级
     */
    private function processUserLevelUpgrade(Output $output)
    {
        $output->writeln('处理用户等级升级...');
        
        // 获取所有用户的消费统计
        $users = Db::name('users')
            ->field('id, user_group, total_recharge, total_consumption')
            ->select();
        
        $upgradeCount = 0;
        
        foreach ($users as $user) {
            try {
                $newGroup = $this->calculateUserGroup($user['total_consumption']);
                
                if ($newGroup != $user['user_group']) {
                    // 升级用户等级
                    Db::name('users')
                        ->where('id', $user['id'])
                        ->update([
                            'user_group' => $newGroup,
                            'update_time' => time()
                        ]);
                    
                    // 记录升级日志
                    ScoreService::addLog(
                        $user['id'],
                        0,
                        ScoreService::TYPE_UPGRADE,
                        "用户等级升级: {$user['user_group']} -> {$newGroup}",
                        0
                    );
                    
                    $upgradeCount++;
                }
                
            } catch (\Exception $e) {
                $output->writeln("用户 #{$user['id']} 等级升级失败: " . $e->getMessage());
            }
        }
        
        $output->writeln("升级了 {$upgradeCount} 个用户等级");
    }
    
    /**
     * 生成积分统计报告
     */
    private function generateScoreReport(Output $output)
    {
        $output->writeln('生成积分统计报告...');
        
        try {
            $today = date('Y-m-d');
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            
            // 统计昨日数据
            $report = [
                'date' => $yesterday,
                'recharge' => $this->getScoreStats($yesterday, ScoreService::TYPE_RECHARGE),
                'consumption' => $this->getScoreStats($yesterday, ScoreService::TYPE_CONSUMPTION),
                'refund' => $this->getScoreStats($yesterday, ScoreService::TYPE_REFUND),
                'user_stats' => $this->getUserStats(),
                'task_stats' => $this->getTaskStats($yesterday)
            ];
            
            // 保存报告
            $reportFile = runtime_path() . 'score_report_' . str_replace('-', '', $yesterday) . '.json';
            file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            
            $output->writeln("积分统计报告已保存到: {$reportFile}");
            
        } catch (\Exception $e) {
            $output->writeln('生成积分统计报告失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 计算实际消费积分
     */
    private function calculateActualCost($task)
    {
        $baseCost = 0;
        
        switch ($task['type']) {
            case TaskService::TYPE_KEYWORD_RANKING:
                $baseCost = $task['daily_clicks'] * 0.1; // 每次点击0.1积分
                break;
            case TaskService::TYPE_DROPDOWN_WORDS:
                $baseCost = $task['daily_searches'] * 0.05; // 每次搜索0.05积分
                break;
        }
        
        return $baseCost * $task['executed_days'];
    }
    
    /**
     * 根据消费金额计算用户等级
     */
    private function calculateUserGroup($totalConsumption)
    {
        if ($totalConsumption >= 10000) {
            return 'vip_user';
        } elseif ($totalConsumption >= 5000) {
            return 'senior_user';
        } elseif ($totalConsumption >= 2000) {
            return 'advanced_user';
        } elseif ($totalConsumption >= 500) {
            return 'regular_user';
        } else {
            return 'new_user';
        }
    }
    
    /**
     * 获取积分统计
     */
    private function getScoreStats($date, $type)
    {
        $startTime = strtotime($date);
        $endTime = $startTime + 86400;
        
        $result = Db::name('score_logs')
            ->where('type', $type)
            ->where('create_time', '>=', $startTime)
            ->where('create_time', '<', $endTime)
            ->field('COUNT(*) as count, SUM(amount) as total')
            ->find();
        
        return [
            'count' => $result['count'] ?: 0,
            'total' => $result['total'] ?: 0
        ];
    }
    
    /**
     * 获取用户统计
     */
    private function getUserStats()
    {
        $stats = [];
        
        $groups = ['new_user', 'regular_user', 'advanced_user', 'senior_user', 'vip_user'];
        
        foreach ($groups as $group) {
            $count = Db::name('users')->where('user_group', $group)->count();
            $stats[$group] = $count;
        }
        
        return $stats;
    }
    
    /**
     * 获取任务统计
     */
    private function getTaskStats($date)
    {
        $startTime = strtotime($date);
        $endTime = $startTime + 86400;
        
        $stats = [
            'created' => Db::name('tasks')
                ->where('create_time', '>=', $startTime)
                ->where('create_time', '<', $endTime)
                ->count(),
            'completed' => Db::name('tasks')
                ->where('complete_time', '>=', $startTime)
                ->where('complete_time', '<', $endTime)
                ->count(),
            'cancelled' => Db::name('tasks')
                ->where('status', TaskService::STATUS_CANCELLED)
                ->where('update_time', '>=', $startTime)
                ->where('update_time', '<', $endTime)
                ->count()
        ];
        
        return $stats;
    }
}
