<FileZillaServer>
    <Settings>
        <Item name="Serverports" type="string">21</Item>
        <Item name="Number of Threads" type="numeric">2</Item>
        <Item name="Maximum user count" type="numeric">0</Item>
        <Item name="Timeout" type="numeric">120</Item>
        <Item name="No Transfer Timeout" type="numeric">600</Item>
        <Item name="Allow Incoming FXP" type="numeric">0</Item>
        <Item name="Allow outgoing FXP" type="numeric">0</Item>
        <Item name="No Strict In FXP" type="numeric">0</Item>
        <Item name="No Strict Out FXP" type="numeric">0</Item>
        <Item name="Login Timeout" type="numeric">60</Item>
        <Item name="Show Pass in Log" type="numeric">0</Item>
        <Item name="Custom PASV IP type" type="numeric">0</Item>
        <Item name="Custom PASV IP" type="string" />
        <Item name="Custom PASV min port" type="numeric">3000</Item>
        <Item name="Custom PASV max port" type="numeric">4000</Item>
        <Item name="Initial Welcome Message" type="string">%v written by Tim Kosse (<EMAIL>) Please visit http://sourceforge.net/projects/filezilla/</Item>
        <Item name="Admin port" type="numeric">14147</Item>
        <Item name="Admin Password" type="string" />
        <Item name="Admin IP Bindings" type="string" />
        <Item name="Admin IP Addresses" type="string" />
        <Item name="Enable logging" type="numeric">0</Item>
        <Item name="Logsize limit" type="numeric">0</Item>
        <Item name="Logfile type" type="numeric">0</Item>
        <Item name="Logfile delete time" type="numeric">0</Item>
        <Item name="Use GSS Support" type="numeric">0</Item>
        <Item name="GSS Prompt for Password" type="numeric">0</Item>
        <Item name="Download Speedlimit Type" type="numeric">0</Item>
        <Item name="Upload Speedlimit Type" type="numeric">0</Item>
        <Item name="Download Speedlimit" type="numeric">10</Item>
        <Item name="Upload Speedlimit" type="numeric">10</Item>
        <Item name="Buffer Size" type="numeric">32768</Item>
        <Item name="Custom PASV IP server" type="string">http://ip.filezilla-project.org/ip.php</Item>
        <Item name="Use custom PASV ports" type="numeric">1</Item>
        <Item name="Mode Z Use" type="numeric">0</Item>
        <Item name="Mode Z min level" type="numeric">1</Item>
        <Item name="Mode Z max level" type="numeric">9</Item>
        <Item name="Mode Z allow local" type="numeric">0</Item>
        <Item name="Mode Z disallowed IPs" type="string" />
        <Item name="IP Bindings" type="string">*</Item>
        <Item name="IP Filter Allowed" type="string" />
        <Item name="IP Filter Disallowed" type="string" />
        <Item name="Hide Welcome Message" type="numeric">0</Item>
        <Item name="Enable SSL" type="numeric">0</Item>
        <Item name="Allow explicit SSL" type="numeric">1</Item>
        <Item name="SSL Key file" type="string" />
        <Item name="SSL Certificate file" type="string" />
        <Item name="Implicit SSL ports" type="string">990</Item>
        <Item name="Force explicit SSL" type="numeric">0</Item>
        <Item name="Network Buffer Size" type="numeric">65536</Item>
        <Item name="Force PROT P" type="numeric">0</Item>
        <Item name="SSL Key Password" type="string" />
        <Item name="Allow shared write" type="numeric">0</Item>
        <Item name="No External IP On Local" type="numeric">1</Item>
        <Item name="Active ignore local" type="numeric">1</Item>
        <Item name="Autoban enable" type="numeric">0</Item>
        <Item name="Autoban attempts" type="numeric">10</Item>
        <Item name="Autoban type" type="numeric">0</Item>
        <Item name="Autoban time" type="numeric">1</Item>
        <Item name="Service name" type="string" />
        <Item name="Service display name" type="string" />
        <Item name="Enable HASH" type="numeric">0</Item>
        <Item name="Disable IPv6" type="numeric">0</Item>
        <SpeedLimits>
            <Download />
            <Upload />
        </SpeedLimits>
    </Settings>
    <Users>
    </Users>
</FileZillaServer>
