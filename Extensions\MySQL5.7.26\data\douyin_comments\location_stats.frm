TYPE=VIEW
query=select coalesce(`douyin_comments`.`comments`.`ip_label`,\'未知\') AS `location`,count(0) AS `total_comments`,count(distinct `douyin_comments`.`comments`.`uid`) AS `unique_users`,count(distinct `douyin_comments`.`comments`.`video_id`) AS `videos_commented`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes`,round(avg(`douyin_comments`.`comments`.`digg_count`),2) AS `avg_likes_per_comment`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'primary\') then 1 else 0 end)) AS `primary_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'reply\') then 1 else 0 end)) AS `reply_comments`,round((count(0) / count(distinct `douyin_comments`.`comments`.`uid`)),2) AS `comments_per_user` from `douyin_comments`.`comments` group by `douyin_comments`.`comments`.`ip_label` having (`total_comments` >= 5) order by `total_comments` desc
md5=f0fe9c37fbd0ae72b1c73214aaa82c4f
updatable=0
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=2025-06-23 12:49:47
create-version=1
source=SELECT 
\n    COALESCE(ip_label, \'未知\') as location,
\n    COUNT(*) as total_comments,
\n    COUNT(DISTINCT uid) as unique_users,
\n    COUNT(DISTINCT video_id) as videos_commented,
\n    SUM(digg_count) as total_likes,
\n    ROUND(AVG(digg_count), 2) as avg_likes_per_comment,
\n    SUM(CASE WHEN comment_type = \'primary\' THEN 1 ELSE 0 END) as primary_comments,
\n    SUM(CASE WHEN comment_type = \'reply\' THEN 1 ELSE 0 END) as reply_comments,
\n    -- 活跃度
\n    ROUND(COUNT(*) / COUNT(DISTINCT uid), 2) as comments_per_user
\nFROM comments 
\nGROUP BY ip_label
\nHAVING total_comments >= 5  -- 只显示评论数>=5的地区
\nORDER BY total_comments DESC
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_general_ci
view_body_utf8=select coalesce(`douyin_comments`.`comments`.`ip_label`,\'未知\') AS `location`,count(0) AS `total_comments`,count(distinct `douyin_comments`.`comments`.`uid`) AS `unique_users`,count(distinct `douyin_comments`.`comments`.`video_id`) AS `videos_commented`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes`,round(avg(`douyin_comments`.`comments`.`digg_count`),2) AS `avg_likes_per_comment`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'primary\') then 1 else 0 end)) AS `primary_comments`,sum((case when (`douyin_comments`.`comments`.`comment_type` = \'reply\') then 1 else 0 end)) AS `reply_comments`,round((count(0) / count(distinct `douyin_comments`.`comments`.`uid`)),2) AS `comments_per_user` from `douyin_comments`.`comments` group by `douyin_comments`.`comments`.`ip_label` having (`total_comments` >= 5) order by `total_comments` desc
