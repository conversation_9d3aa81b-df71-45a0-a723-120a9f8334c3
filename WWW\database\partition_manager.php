<?php
/**
 * 数据库分区管理器
 * 用于自动创建和维护分区表
 */

class PartitionManager
{
    private $db;
    
    public function __construct($db)
    {
        $this->db = $db;
    }
    
    /**
     * 创建月度分区
     */
    public function createMonthlyPartitions($tableName, $months = 12)
    {
        $currentTime = time();
        
        for ($i = 0; $i < $months; $i++) {
            $partitionTime = strtotime("+{$i} month", $currentTime);
            $partitionName = 'p_' . date('Y_m', $partitionTime);
            $partitionValue = strtotime(date('Y-m-01', strtotime("+1 month", $partitionTime)));
            
            $sql = "ALTER TABLE `{$tableName}` ADD PARTITION (
                PARTITION {$partitionName} VALUES LESS THAN ({$partitionValue})
            )";
            
            try {
                $this->db->execute($sql);
                echo "Created partition {$partitionName} for table {$tableName}\n";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate partition name') === false) {
                    echo "Error creating partition {$partitionName}: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    /**
     * 删除过期分区
     */
    public function dropExpiredPartitions($tableName, $keepMonths = 6)
    {
        $expireTime = strtotime("-{$keepMonths} month");
        $expirePartition = 'p_' . date('Y_m', $expireTime);
        
        // 获取所有分区
        $sql = "SELECT PARTITION_NAME 
                FROM INFORMATION_SCHEMA.PARTITIONS 
                WHERE TABLE_NAME = '{$tableName}' 
                AND PARTITION_NAME IS NOT NULL 
                AND PARTITION_NAME != 'p_default' 
                AND PARTITION_NAME != 'p_future'
                AND PARTITION_NAME < '{$expirePartition}'";
        
        $partitions = $this->db->query($sql);
        
        foreach ($partitions as $partition) {
            $partitionName = $partition['PARTITION_NAME'];
            
            try {
                $sql = "ALTER TABLE `{$tableName}` DROP PARTITION {$partitionName}";
                $this->db->execute($sql);
                echo "Dropped expired partition {$partitionName} from table {$tableName}\n";
            } catch (Exception $e) {
                echo "Error dropping partition {$partitionName}: " . $e->getMessage() . "\n";
            }
        }
    }
    
    /**
     * 维护分区表
     */
    public function maintainPartitions()
    {
        $partitionTables = [
            'score_logs',
            'task_execution_logs'
        ];
        
        foreach ($partitionTables as $table) {
            echo "Maintaining partitions for table: {$table}\n";
            
            // 创建未来12个月的分区
            $this->createMonthlyPartitions($table, 12);
            
            // 删除6个月前的分区
            $this->dropExpiredPartitions($table, 6);
            
            echo "Partition maintenance completed for table: {$table}\n\n";
        }
    }
    
    /**
     * 获取分区信息
     */
    public function getPartitionInfo($tableName)
    {
        $sql = "SELECT 
                    PARTITION_NAME,
                    PARTITION_DESCRIPTION,
                    TABLE_ROWS,
                    AVG_ROW_LENGTH,
                    DATA_LENGTH,
                    INDEX_LENGTH,
                    CREATE_TIME
                FROM INFORMATION_SCHEMA.PARTITIONS 
                WHERE TABLE_NAME = '{$tableName}' 
                AND PARTITION_NAME IS NOT NULL
                ORDER BY PARTITION_ORDINAL_POSITION";
        
        return $this->db->query($sql);
    }
    
    /**
     * 优化分区表
     */
    public function optimizePartitions($tableName)
    {
        $partitions = $this->getPartitionInfo($tableName);
        
        foreach ($partitions as $partition) {
            $partitionName = $partition['PARTITION_NAME'];
            
            try {
                $sql = "ALTER TABLE `{$tableName}` OPTIMIZE PARTITION {$partitionName}";
                $this->db->execute($sql);
                echo "Optimized partition {$partitionName} in table {$tableName}\n";
            } catch (Exception $e) {
                echo "Error optimizing partition {$partitionName}: " . $e->getMessage() . "\n";
            }
        }
    }
    
    /**
     * 分析分区表
     */
    public function analyzePartitions($tableName)
    {
        $partitions = $this->getPartitionInfo($tableName);
        
        foreach ($partitions as $partition) {
            $partitionName = $partition['PARTITION_NAME'];
            
            try {
                $sql = "ALTER TABLE `{$tableName}` ANALYZE PARTITION {$partitionName}";
                $this->db->execute($sql);
                echo "Analyzed partition {$partitionName} in table {$tableName}\n";
            } catch (Exception $e) {
                echo "Error analyzing partition {$partitionName}: " . $e->getMessage() . "\n";
            }
        }
    }
    
    /**
     * 检查分区健康状态
     */
    public function checkPartitionHealth()
    {
        $partitionTables = ['score_logs', 'task_execution_logs'];
        
        foreach ($partitionTables as $table) {
            echo "Checking partition health for table: {$table}\n";
            
            $partitions = $this->getPartitionInfo($table);
            $totalRows = 0;
            $totalSize = 0;
            
            foreach ($partitions as $partition) {
                $rows = $partition['TABLE_ROWS'];
                $size = $partition['DATA_LENGTH'] + $partition['INDEX_LENGTH'];
                $totalRows += $rows;
                $totalSize += $size;
                
                echo sprintf(
                    "  Partition %s: %d rows, %s\n",
                    $partition['PARTITION_NAME'],
                    $rows,
                    $this->formatBytes($size)
                );
            }
            
            echo sprintf(
                "  Total: %d rows, %s\n\n",
                $totalRows,
                $this->formatBytes($totalSize)
            );
        }
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    require_once __DIR__ . '/../vendor/autoload.php';
    
    // 这里需要根据实际情况配置数据库连接
    $config = [
        'hostname' => '127.0.0.1',
        'database' => 'seo_score_system',
        'username' => 'root',
        'password' => '',
        'hostport' => '3306',
        'charset'  => 'utf8mb4',
    ];
    
    try {
        $pdo = new PDO(
            "mysql:host={$config['hostname']};port={$config['hostport']};dbname={$config['database']};charset={$config['charset']}",
            $config['username'],
            $config['password'],
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        $manager = new PartitionManager($pdo);
        
        $action = $argv[1] ?? 'maintain';
        
        switch ($action) {
            case 'maintain':
                $manager->maintainPartitions();
                break;
            case 'health':
                $manager->checkPartitionHealth();
                break;
            case 'optimize':
                $table = $argv[2] ?? 'score_logs';
                $manager->optimizePartitions($table);
                break;
            case 'analyze':
                $table = $argv[2] ?? 'score_logs';
                $manager->analyzePartitions($table);
                break;
            default:
                echo "Usage: php partition_manager.php [maintain|health|optimize|analyze] [table_name]\n";
        }
        
    } catch (Exception $e) {
        echo "Database connection error: " . $e->getMessage() . "\n";
    }
}
