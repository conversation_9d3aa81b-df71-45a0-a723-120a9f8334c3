<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <title>404 错误 - phpstudy</title>
  <meta name="keywords" content="">
  <meta name="description" content="">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="apple-mobile-web-app-status-bar-style" content="black"> 
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="format-detection" content="telephone=no">
  <meta HTTP-EQUIV="pragma" CONTENT="no-cache"> 
  <meta HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
  <meta HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
  <meta HTTP-EQUIV="expires" CONTENT="0">
  <style>
    body{
      font: 16px arial,'Microsoft Yahei','Hiragino Sans GB',sans-serif;
    }
    h1{
      margin: 0;
      color:#3a87ad;
      font-size: 26px;
    }
    .content{
      width: 45%;
      margin: 0 auto;
     
    }
    .content >div{
      margin-top: 50px;
      padding: 20px;
      background: #d9edf7;  
      border-radius: 12px;
    }
    .content dl{
      color: #2d6a88;
      line-height: 40px;
    } 
    .content div div {
      padding-bottom: 20px;
      text-align:center;
    }
  </style>
</head>
<body>
  <div class="content">
      <div>
           <h1>404 - Page Not Found 未找到</h1>
        <dl>
          <dt>错误说明：请求的页面不存在</dt>
          <dt>原因1：访问的文档权限不够</dt>
		  <dd>解决办法：</dd>
          <dd>修改文件权限为755，windos系统修改目录权限为可写可读。</dd>
          <dt>原因2：防火墙的原因</dt>
		  <dd>解决办法：</dd>
          <dd>先关闭让防火墙通过WWW服务。</dd>
		  <dt>原因3：站点根目录无默认访问文件</dt>
		  <dd>解决办法：</dd>
          <dd>在根目录中创建index.html或者创建index.php。</dd>
		  <dt>原因4：站点配置目录不正确</dt>
		  <dd>解决办法：</dd>
          <dd>将网站应用程序复制到站点目录中，或者修改站点配置目录指定到应用程序目录中。</dd>
		  <dt>原因5：站点使用了伪静态</dt>
		  <dd>解决办法：</dd>
          <dd>将伪静态规则删除，或者重新编写正确的伪静态规则，或关闭伪静态配置。</dd>
        </dl>
        <div>使用手册，视频教程，BUG反馈，官网地址： <a href="https://www.xp.cn"  target="_blank">www.xp.cn</a> </div>
    
      </div>
    </div> 
</body>
</html>