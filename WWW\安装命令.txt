# SEO积分系统安装指南

## 1. 环境要求
- PHP 8.0+
- MySQL 8.0+
- Redis 6.0+
- Nginx 1.15+
- Composer

## 2. 安装步骤

### 2.1 安装PHP依赖
composer install
composer require predis/predis

### 2.2 配置数据库
1. 创建数据库：seo_points_system
2. 导入数据库结构：
   mysql -u root -p seo_points_system < database/install.sql

### 2.3 配置环境文件
复制 .env.example 到 .env 并修改配置：
- 数据库连接信息
- Redis连接信息
- JWT密钥

### 2.4 配置Nginx
将 d:\phpstudy_pro\Extensions\Nginx1.15.11\conf\vhosts\seo_points_system.conf
添加到Nginx配置中，并重启Nginx

### 2.5 设置目录权限
确保以下目录可写：
- runtime/
- public/uploads/

## 3. 默认账号信息

### 管理员账号
用户名: admin
密码: admin123456
登录地址: http://localhost/admin/

### 测试用户账号
用户名: testuser
密码: password123
登录地址: http://localhost/user/

## 4. 系统地址

### 前端地址
- 首页: http://localhost/
- 用户中心: http://localhost/user/
- 管理后台: http://localhost/admin/

### API地址
- 用户API: http://localhost/api/
- 管理API: http://localhost/api/admin/
- 健康检查: http://localhost/health
- 系统配置: http://localhost/api/system/config

## 5. 定时任务设置
添加以下定时任务到系统crontab：

# 任务执行（每分钟）
* * * * * cd /path/to/project && php think task:execute

# 积分结算（每小时）
0 * * * * cd /path/to/project && php think score:settlement

# 数据清理（每天凌晨2点）
0 2 * * * cd /path/to/project && php think data:cleanup

# 系统优化（每周日凌晨3点）
0 3 * * 0 cd /path/to/project && php think system:optimize

## 6. 测试验证
1. 访问 http://localhost/ 查看首页
2. 访问 http://localhost/health 检查API状态
3. 使用默认账号登录管理后台和用户中心

## 7. 常见问题

### 7.1 404错误
检查Nginx配置是否正确，确保伪静态规则生效

### 7.2 数据库连接失败
检查.env文件中的数据库配置信息

### 7.3 Redis连接失败
确保Redis服务已启动，检查连接配置

### 7.4 权限问题
确保runtime目录有写入权限

## 8. 开发调试
开启调试模式：
在.env文件中设置 APP_DEBUG=true