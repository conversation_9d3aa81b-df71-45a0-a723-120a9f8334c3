# SEO积分系统开发文档 - 优化版

## 📋 项目概述

### 系统简介
SEO积分系统是一个基于积分消费的SEO优化服务平台，用户通过充值获得积分，消费积分来提交关键词排名和下拉词优化任务。

### 核心功能
- **用户管理**: 注册登录、用户分组、权限控制
- **任务管理**: 关键词排名、下拉词优化、任务审核
- **积分系统**: 充值消费、等级折扣、自动结算
- **管理后台**: 用户管理、任务审核、财务统计

## 🏗️ 技术架构

### 开发方案对比

| 方案 | 简化版 | 完整版 |
|------|--------|--------|
| **适用场景** | 快速上线、小规模用户 | 大规模商用、高并发 |
| **开发周期** | 4-6周 | 8-12周 |
| **技术栈** | ThinkPHP8 + MySQL + Redis | ThinkPHP8 + MySQL + Redis + 队列 |
| **缓存策略** | 基础缓存 | 多层缓存 + 主从复制 |
| **安全等级** | 基础安全 | 企业级安全 |
| **监控体系** | 基础日志 | 完整监控 + 告警 |
| **预估成本** | 5-8万 | 15-25万 |

### 推荐技术栈
```yaml
后端框架: ThinkPHP 8.0
PHP版本: 8.1+
数据库: MySQL 8.0 (InnoDB引擎)
缓存: Redis 6.0+ (支持主从)
Web服务器: Nginx 1.18+
队列: Redis队列 (完整版)
监控: 自研监控系统
```

## 🗄️ 数据库设计

### 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE `users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `mobile` varchar(20) NOT NULL COMMENT '手机号',
  `mobile_hash` varchar(64) NOT NULL COMMENT '手机号哈希(用于查询)',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `group_id` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '用户组ID',
  `score` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '积分余额',
  `total_recharge` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计充值',
  `total_consumption` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计消费',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '1正常 2锁定 3禁用',
  `last_login_time` int(11) unsigned DEFAULT NULL COMMENT '最后登录时间',
  `create_time` int(11) unsigned NOT NULL COMMENT '注册时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_mobile_hash` (`mobile_hash`),
  KEY `idx_group_status` (`group_id`, `status`),
  KEY `idx_status_create` (`status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 任务表 (tasks)
```sql
CREATE TABLE `tasks` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `type` tinyint(3) unsigned NOT NULL COMMENT '1关键词排名 2下拉词',
  `keyword` varchar(100) NOT NULL COMMENT '关键词',
  `url` varchar(500) DEFAULT NULL COMMENT '目标URL',
  `search_engine` varchar(20) NOT NULL COMMENT '搜索引擎',
  `online_days` tinyint(3) unsigned NOT NULL COMMENT '上线天数',
  `daily_amount` smallint(5) unsigned NOT NULL COMMENT '每日数量',
  `pre_deduct_score` decimal(10,2) unsigned NOT NULL COMMENT '预扣积分',
  `actual_cost_score` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '实际消费',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '1待审核 2执行中 3已完成 4已暂停 5已取消',
  `start_time` int(11) unsigned DEFAULT NULL COMMENT '开始时间',
  `end_time` int(11) unsigned DEFAULT NULL COMMENT '结束时间',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_status` (`user_id`, `status`),
  KEY `idx_status_create` (`status`, `create_time`),
  KEY `idx_engine_status` (`search_engine`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';
```

#### 积分记录表 (score_logs) - 按月分区
```sql
CREATE TABLE `score_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `task_id` int(11) unsigned DEFAULT NULL COMMENT '关联任务ID',
  `type` tinyint(3) unsigned NOT NULL COMMENT '1充值 2消费 3退款 4奖励',
  `amount` decimal(10,2) NOT NULL COMMENT '积分数量',
  `balance_before` decimal(12,2) unsigned NOT NULL COMMENT '操作前余额',
  `balance_after` decimal(12,2) unsigned NOT NULL COMMENT '操作后余额',
  `description` varchar(255) NOT NULL COMMENT '操作描述',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`, `create_time`),
  KEY `idx_user_type` (`user_id`, `type`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表'
PARTITION BY RANGE (create_time) (
    PARTITION p_default VALUES LESS THAN (1704067200),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 索引优化策略
```sql
-- 任务表复合索引
ALTER TABLE tasks ADD INDEX idx_user_status_type (user_id, status, type);
ALTER TABLE tasks ADD INDEX idx_status_time (status, create_time);

-- 积分记录表索引
ALTER TABLE score_logs ADD INDEX idx_user_type_time (user_id, type, create_time);

-- 用户表索引
ALTER TABLE users ADD INDEX idx_group_status (group_id, status);
```

## 🔧 核心功能实现

### 用户认证系统
```php
// app/service/AuthService.php
class AuthService
{
    // 手机号加密存储
    public static function hashMobile($mobile)
    {
        return hash('sha256', $mobile . config('app.salt'));
    }
    
    // 安全登录验证
    public function login($mobile, $password, $remember = false)
    {
        $mobileHash = self::hashMobile($mobile);
        $user = Db::name('users')->where('mobile_hash', $mobileHash)->find();
        
        if (!$user || !password_verify($password, $user['password'])) {
            throw new Exception('手机号或密码错误');
        }
        
        if ($user['status'] != 1) {
            throw new Exception('账户已被禁用');
        }
        
        // 生成JWT Token
        $payload = [
            'user_id' => $user['id'],
            'mobile' => $mobile,
            'group_id' => $user['group_id'],
            'exp' => time() + ($remember ? 7*24*3600 : 2*3600)
        ];
        
        $token = JWT::encode($payload, config('jwt.secret'), 'HS256');
        
        // 更新登录信息
        Db::name('users')->where('id', $user['id'])->update([
            'last_login_time' => time(),
            'update_time' => time()
        ]);
        
        return [
            'token' => $token,
            'user_info' => [
                'id' => $user['id'],
                'nickname' => $user['nickname'],
                'group_id' => $user['group_id'],
                'score' => $user['score']
            ]
        ];
    }
}
```

### 任务管理系统
```php
// app/service/TaskService.php
class TaskService
{
    // 创建任务
    public function createTask($userId, $taskData)
    {
        $user = CacheService::getUserInfo($userId);
        
        // 计算所需积分
        $requiredScore = $this->calculateScore($taskData, $user);
        
        // 检查积分余额
        if ($user['score'] < $requiredScore) {
            throw new Exception('积分余额不足');
        }
        
        // 敏感词检测
        if ($this->containsSensitiveWords($taskData['keyword'])) {
            throw new Exception('关键词包含敏感词');
        }
        
        Db::transaction(function() use ($userId, $taskData, $requiredScore) {
            // 预扣积分
            Db::name('users')->where('id', $userId)->dec('score', $requiredScore);
            
            // 创建任务
            $taskId = Db::name('tasks')->insertGetId(array_merge($taskData, [
                'user_id' => $userId,
                'pre_deduct_score' => $requiredScore,
                'create_time' => time(),
                'update_time' => time()
            ]));
            
            // 记录积分日志
            ScoreService::addLog($userId, $taskId, 2, -$requiredScore, '创建任务预扣积分');
        });
        
        return true;
    }
    
    // 积分计算
    private function calculateScore($taskData, $user)
    {
        $baseScore = $taskData['online_days'] * $taskData['daily_amount'];
        $userGroup = CacheService::getUserGroup($user['group_id']);
        
        return $baseScore * $userGroup['score_cost'];
    }
}
```

### 缓存服务优化
```php
// app/service/CacheService.php
class CacheService
{
    // 用户信息缓存（带防穿透）
    public static function getUserInfo($userId)
    {
        $key = 'user_info_' . $userId;
        
        return Cache::remember($key, function() use ($userId) {
            $user = Db::name('users')->where('id', $userId)->find();
            return $user ?: ['id' => 0]; // 防止缓存穿透
        }, 1800); // 30分钟
    }
    
    // 用户组配置缓存
    public static function getUserGroup($groupId)
    {
        return Cache::remember('user_group_' . $groupId, function() use ($groupId) {
            return Db::name('user_groups')->where('id', $groupId)->find();
        }, 3600);
    }
    
    // 缓存预热
    public static function warmUp()
    {
        // 预热用户组配置
        $groups = Db::name('user_groups')->where('status', 1)->select();
        foreach ($groups as $group) {
            Cache::set('user_group_' . $group['id'], $group, 3600);
        }
        
        // 预热敏感词
        $sensitiveWords = Db::name('sensitive_words')->where('status', 1)->column('word');
        Cache::set('sensitive_words', $sensitiveWords, 7200);
    }
}
```

## 🔒 安全防护机制

### 1. 数据安全
- 手机号哈希存储，原文加密
- 密码bcrypt加密
- 敏感操作签名验证
- SQL注入防护

### 2. 接口安全
- JWT Token认证
- 请求频率限制
- IP白名单机制
- 参数签名验证

### 3. 业务安全
- 积分操作双重验证
- 异常行为监控
- 自动风控机制
- 操作日志记录

## 📊 性能优化方案

### 1. 数据库优化
- 合理索引设计
- 分区表策略
- 读写分离
- 连接池优化

### 2. 缓存优化
- 多层缓存架构
- 缓存预热机制
- 防雪崩策略
- 自动过期清理

### 3. 代码优化
- 批量操作优化
- 异步队列处理
- 内存使用优化
- 慢查询监控

## 🚀 部署方案

### 环境要求
```yaml
服务器配置:
  CPU: 4核心以上
  内存: 8GB以上
  硬盘: SSD 100GB以上
  带宽: 10Mbps以上

软件环境:
  操作系统: CentOS 7+ / Ubuntu 18+
  PHP: 8.1+
  MySQL: 8.0+
  Redis: 6.0+
  Nginx: 1.18+
```

### 部署步骤
1. **环境准备**: 安装基础软件环境
2. **代码部署**: 上传代码并安装依赖
3. **数据库初始化**: 创建数据库和表结构
4. **配置文件**: 设置数据库和缓存连接
5. **权限设置**: 设置文件和目录权限
6. **定时任务**: 配置系统定时任务
7. **监控配置**: 设置日志和监控
8. **测试验证**: 功能测试和性能测试

## 📈 运维监控

### 关键指标监控
- 系统性能: CPU、内存、磁盘、网络
- 数据库性能: 连接数、慢查询、锁等待
- 应用性能: 响应时间、错误率、并发数
- 业务指标: 用户活跃度、任务成功率、收入统计

### 告警机制
- 系统异常自动告警
- 业务指标异常提醒
- 安全事件实时通知
- 性能瓶颈预警

## 🔄 定时任务设计

### 核心定时任务
```bash
# 每日凌晨1点 - 更新任务执行数据
0 1 * * * cd /path/to/project && php think task:update-daily

# 每日凌晨2点 - 检查过期任务
0 2 * * * cd /path/to/project && php think task:check-expired

# 每日凌晨3点 - 处理积分退款
0 3 * * * cd /path/to/project && php think score:process-refunds

# 每小时 - 清理过期缓存
0 * * * * cd /path/to/project && php think cache:clear-expired

# 每10分钟 - 系统健康检查
*/10 * * * * cd /path/to/project && php think system:health-check
```

### 任务执行逻辑
```php
// app/command/UpdateDailyTask.php
class UpdateDailyTask extends Command
{
    protected function execute(Input $input, Output $output)
    {
        $runningTasks = Db::name('tasks')
            ->where('status', 2)
            ->where('end_time', '>', time())
            ->select();

        foreach ($runningTasks as $task) {
            // 生成随机执行数据
            $baseAmount = $task['daily_amount'];
            $actualAmount = rand($baseAmount * 0.8, $baseAmount * 1.2);

            // 记录执行日志
            Db::name('task_execution_logs')->insert([
                'task_id' => $task['id'],
                'user_id' => $task['user_id'],
                'execution_date' => date('Y-m-d'),
                'planned_amount' => $baseAmount,
                'actual_amount' => $actualAmount,
                'create_time' => time()
            ]);
        }

        $output->writeln('Daily task update completed');
    }
}
```

## 📱 API接口设计

### 用户端核心接口
```php
// 用户认证
POST /api/user/login
{
    "mobile": "13800138000",
    "password": "password123",
    "remember": true
}

// 创建任务
POST /api/user/tasks
{
    "type": 1,
    "keyword": "SEO优化",
    "url": "https://example.com",
    "search_engine": "baidu",
    "online_days": 15,
    "daily_amount": 50
}

// 获取任务列表
GET /api/user/tasks?page=1&limit=20&status=1

// 积分充值
POST /api/user/score/recharge
{
    "amount": 1000,
    "payment_method": "alipay"
}
```

### 管理端核心接口
```php
// 任务审核
PUT /api/admin/tasks/{id}/review
{
    "action": "approve",
    "remark": "审核通过"
}

// 用户管理
GET /api/admin/users?page=1&limit=20&group_id=1

// 财务统计
GET /api/admin/finance/statistics?period=month
```

## 🛡️ 风控系统设计

### 异常检测规则
```php
// app/service/RiskControlService.php
class RiskControlService
{
    // 检测异常操作
    public static function detectAbnormal($userId, $operation, $data = [])
    {
        $risks = [
            'high_frequency' => self::checkHighFrequency($userId, $operation),
            'large_amount' => self::checkLargeAmount($operation, $data),
            'unusual_time' => self::checkUnusualTime(),
            'ip_change' => self::checkIpChange($userId)
        ];

        $riskLevel = 0;
        foreach ($risks as $type => $detected) {
            if ($detected) {
                $riskLevel += self::getRiskWeight($type);
                self::logRisk($userId, $operation, $type, $data);
            }
        }

        // 根据风险等级采取措施
        if ($riskLevel >= 80) {
            self::lockAccount($userId, '高风险操作');
        } elseif ($riskLevel >= 50) {
            self::requireVerification($userId);
        } elseif ($riskLevel >= 30) {
            self::limitOperations($userId);
        }

        return $riskLevel;
    }

    // 高频操作检测
    private static function checkHighFrequency($userId, $operation)
    {
        $key = "operation_freq:{$operation}:{$userId}";
        $count = Cache::get($key, 0);
        Cache::set($key, $count + 1, 300); // 5分钟窗口

        $thresholds = [
            'create_task' => 20,
            'recharge' => 5,
            'login' => 10
        ];

        return $count > ($thresholds[$operation] ?? 15);
    }
}
```

## 📊 数据统计分析

### 业务指标统计
```php
// app/service/StatisticsService.php
class StatisticsService
{
    // 用户统计
    public static function getUserStats($period = 'today')
    {
        $timeRange = self::getTimeRange($period);

        return [
            'new_users' => Db::name('users')
                ->where('create_time', 'between', $timeRange)
                ->count(),
            'active_users' => Db::name('users')
                ->where('last_login_time', 'between', $timeRange)
                ->count(),
            'recharge_users' => Db::name('score_logs')
                ->where('type', 1)
                ->where('create_time', 'between', $timeRange)
                ->group('user_id')
                ->count()
        ];
    }

    // 任务统计
    public static function getTaskStats($period = 'today')
    {
        $timeRange = self::getTimeRange($period);

        return [
            'total_tasks' => Db::name('tasks')
                ->where('create_time', 'between', $timeRange)
                ->count(),
            'approved_tasks' => Db::name('tasks')
                ->where('status', 2)
                ->where('create_time', 'between', $timeRange)
                ->count(),
            'completed_tasks' => Db::name('tasks')
                ->where('status', 3)
                ->where('update_time', 'between', $timeRange)
                ->count()
        ];
    }

    // 收入统计
    public static function getIncomeStats($period = 'today')
    {
        $timeRange = self::getTimeRange($period);

        return [
            'total_recharge' => Db::name('score_logs')
                ->where('type', 1)
                ->where('create_time', 'between', $timeRange)
                ->sum('amount'),
            'total_consumption' => Db::name('score_logs')
                ->where('type', 2)
                ->where('create_time', 'between', $timeRange)
                ->sum('amount'),
            'total_refund' => Db::name('score_logs')
                ->where('type', 3)
                ->where('create_time', 'between', $timeRange)
                ->sum('amount')
        ];
    }
}
```

## 🔧 系统配置管理

### 动态配置系统
```php
// app/service/ConfigService.php
class ConfigService
{
    // 获取系统配置
    public static function get($key, $default = null)
    {
        $config = Cache::remember('system_config', function() {
            return Db::name('system_configs')
                ->where('status', 1)
                ->column('value', 'key');
        }, 3600);

        return $config[$key] ?? $default;
    }

    // 更新配置
    public static function set($key, $value)
    {
        Db::name('system_configs')->where('key', $key)->update([
            'value' => $value,
            'update_time' => time()
        ]);

        // 清除缓存
        Cache::delete('system_config');
    }
}
```

### 核心配置项
```php
$systemConfigs = [
    'site_name' => 'SEO积分系统',
    'default_score_cost' => '1.0',
    'min_recharge_amount' => '100',
    'max_daily_tasks' => '50',
    'task_review_timeout' => '24',
    'score_refund_rate' => '0.8',
    'sensitive_word_check' => 'true',
    'auto_approve_keywords' => 'false',
    'max_keyword_length' => '50',
    'min_online_days' => '1',
    'max_online_days' => '30'
];
```

## 🚨 错误处理和日志

### 统一异常处理
```php
// app/exception/ApiException.php
class ApiException extends Exception
{
    protected $statusCode;
    protected $errorCode;

    public function __construct($message, $errorCode = 0, $statusCode = 400)
    {
        parent::__construct($message);
        $this->errorCode = $errorCode;
        $this->statusCode = $statusCode;
    }

    public function render()
    {
        return json([
            'code' => $this->errorCode,
            'message' => $this->getMessage(),
            'data' => null,
            'timestamp' => time()
        ], $this->statusCode);
    }
}
```

### 日志记录策略
```php
// 业务日志
Log::info('User login', ['user_id' => $userId, 'ip' => $ip]);

// 错误日志
Log::error('Task creation failed', [
    'user_id' => $userId,
    'error' => $e->getMessage(),
    'trace' => $e->getTraceAsString()
]);

// 安全日志
Log::warning('Suspicious activity detected', [
    'user_id' => $userId,
    'activity' => $activity,
    'risk_level' => $riskLevel
]);
```

---

## 📋 开发检查清单

### 功能开发
- [ ] 用户注册登录功能
- [ ] 任务创建和管理
- [ ] 积分充值和消费
- [ ] 管理员审核功能
- [ ] 定时任务处理
- [ ] API接口开发

### 安全检查
- [ ] 数据加密存储
- [ ] 接口权限验证
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] CSRF防护
- [ ] 频率限制

### 性能优化
- [ ] 数据库索引优化
- [ ] 缓存策略实施
- [ ] 慢查询优化
- [ ] 代码性能测试
- [ ] 并发测试
- [ ] 内存泄漏检查

### 部署准备
- [ ] 环境配置文档
- [ ] 数据库迁移脚本
- [ ] 配置文件模板
- [ ] 部署脚本编写
- [ ] 监控配置
- [ ] 备份策略

---

**开发建议**:
1. **分阶段开发**: 先实现MVP版本，再逐步完善功能
2. **重视测试**: 编写单元测试和集成测试，确保代码质量
3. **安全优先**: 从设计阶段就考虑安全问题，建立完善的安全体系
4. **性能监控**: 建立完善的监控体系，及时发现和解决性能问题
5. **文档维护**: 保持技术文档和API文档的及时更新
6. **代码规范**: 制定并遵循代码规范，提高代码可维护性
