<?php

namespace app\controller\admin;

use think\Request;
use think\exception\ValidateException;
use app\service\UserService;
use app\service\TaskService;
use app\service\ScoreService;
use app\service\SecurityService;
use app\controller\api\BaseController;

/**
 * 管理员控制器
 */
class AdminController extends BaseController
{
    /**
     * 获取仪表盘数据
     */
    public function dashboard(Request $request)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            $data = [
                'user_stats' => $this->getUserStats(),
                'task_stats' => $this->getTaskStats(),
                'score_stats' => $this->getScoreStats(),
                'recent_activities' => $this->getRecentActivities()
            ];
            
            return $this->success($data);
            
        } catch (\Exception $e) {
            return $this->error('获取仪表盘数据失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取用户列表
     */
    public function users(Request $request)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            [$page, $limit] = $this->getPageParams();
            
            // 获取筛选条件
            $filters = $this->filterEmpty([
                'keyword' => $request->param('keyword'),
                'status' => $request->param('status'),
                'group_id' => $request->param('group_id'),
                'start_date' => $request->param('start_date'),
                'end_date' => $request->param('end_date')
            ]);
            
            $result = $this->getUserList($page, $limit, $filters);
            
            return $this->paginate(
                $result['list'],
                $result['total'],
                $page,
                $limit
            );
            
        } catch (\Exception $e) {
            return $this->error('获取用户列表失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取用户详情
     */
    public function userDetail(Request $request, $id)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            $user = UserService::getUserById($id);
            
            if (!$user) {
                return $this->error('用户不存在', 404);
            }
            
            // 获取用户统计数据
            $userStats = [
                'total_tasks' => \think\facade\Db::name('tasks')->where('user_id', $id)->count(),
                'total_recharge' => \think\facade\Db::name('score_logs')->where('user_id', $id)->where('type', ScoreService::TYPE_RECHARGE)->sum('amount'),
                'total_consumption' => \think\facade\Db::name('score_logs')->where('user_id', $id)->where('type', ScoreService::TYPE_CONSUME)->sum('ABS(amount)'),
                'recent_login' => \think\facade\Db::name('security_logs')->where('user_id', $id)->where('action', 'login')->order('create_time', 'desc')->value('create_time')
            ];
            
            $user['stats'] = $userStats;
            
            return $this->success($user);
            
        } catch (\Exception $e) {
            return $this->error('获取用户详情失败：' . $e->getMessage());
        }
    }
    
    /**
     * 更新用户状态
     */
    public function updateUserStatus(Request $request, $id)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            $status = $request->param('status');
            $reason = $request->param('reason', '');
            
            if (!in_array($status, [0, 1])) {
                return $this->error('状态参数无效');
            }
            
            $result = \think\facade\Db::name('users')
                ->where('id', $id)
                ->update([
                    'status' => $status,
                    'update_time' => time()
                ]);
            
            if ($result) {
                // 记录操作日志
                $this->logAction('update_user_status', [
                    'user_id' => $id,
                    'status' => $status,
                    'reason' => $reason
                ]);
                
                return $this->success(null, '用户状态更新成功');
            } else {
                return $this->error('用户状态更新失败');
            }
            
        } catch (\Exception $e) {
            return $this->error('更新用户状态失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取任务列表
     */
    public function tasks(Request $request)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            [$page, $limit] = $this->getPageParams();
            
            // 获取筛选条件
            $filters = $this->filterEmpty([
                'keyword' => $request->param('keyword'),
                'status' => $request->param('status'),
                'type' => $request->param('type'),
                'user_id' => $request->param('user_id'),
                'start_date' => $request->param('start_date'),
                'end_date' => $request->param('end_date')
            ]);
            
            $result = $this->getTaskList($page, $limit, $filters);
            
            return $this->paginate(
                $result['list'],
                $result['total'],
                $page,
                $limit
            );
            
        } catch (\Exception $e) {
            return $this->error('获取任务列表失败：' . $e->getMessage());
        }
    }
    
    /**
     * 审核任务
     */
    public function reviewTask(Request $request, $id)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            $status = $request->param('status');
            $remark = $request->param('remark', '');
            
            if (!in_array($status, [TaskService::STATUS_RUNNING, TaskService::STATUS_CANCELLED])) {
                return $this->error('审核状态无效');
            }
            
            $result = TaskService::reviewTask($id, $admin['id'], $status, $remark);
            
            if ($result) {
                // 记录操作日志
                $this->logAction('review_task', [
                    'task_id' => $id,
                    'status' => $status,
                    'remark' => $remark
                ]);
                
                return $this->success(null, '任务审核成功');
            } else {
                return $this->error('任务审核失败');
            }
            
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('任务审核失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取财务统计
     */
    public function financial(Request $request)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            $startDate = $request->param('start_date', date('Y-m-01'));
            $endDate = $request->param('end_date', date('Y-m-d'));
            
            $data = [
                'recharge_stats' => $this->getRechargeStats($startDate, $endDate),
                'consumption_stats' => $this->getConsumptionStats($startDate, $endDate),
                'refund_stats' => $this->getRefundStats($startDate, $endDate),
                'daily_trend' => $this->getDailyFinancialTrend($startDate, $endDate)
            ];
            
            return $this->success($data);
            
        } catch (\Exception $e) {
            return $this->error('获取财务统计失败：' . $e->getMessage());
        }
    }
    
    /**
     * 手动充值
     */
    public function manualRecharge(Request $request)
    {
        try {
            $admin = $this->getAdmin();
            if (!$admin) {
                return $this->error('管理员未登录', 401);
            }
            
            $userId = $request->param('user_id');
            $amount = $request->param('amount');
            $description = $request->param('description', '管理员手动充值');
            
            if (empty($userId) || $amount <= 0) {
                return $this->error('参数错误');
            }
            
            $logId = ScoreService::recharge($userId, $amount, $description, $admin['id']);
            
            // 记录操作日志
            $this->logAction('manual_recharge', [
                'user_id' => $userId,
                'amount' => $amount,
                'description' => $description,
                'log_id' => $logId
            ]);
            
            return $this->success(['log_id' => $logId], '充值成功');
            
        } catch (\Exception $e) {
            return $this->error('充值失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取用户统计
     */
    private function getUserStats()
    {
        $today = strtotime(date('Y-m-d'));
        $yesterday = $today - 86400;
        $thisMonth = strtotime(date('Y-m-01'));
        
        return [
            'total_users' => \think\facade\Db::name('users')->count(),
            'active_users' => \think\facade\Db::name('users')->where('status', 1)->count(),
            'today_register' => \think\facade\Db::name('users')->where('create_time', '>=', $today)->count(),
            'yesterday_register' => \think\facade\Db::name('users')->where('create_time', '>=', $yesterday)->where('create_time', '<', $today)->count(),
            'month_register' => \think\facade\Db::name('users')->where('create_time', '>=', $thisMonth)->count()
        ];
    }
    
    /**
     * 获取任务统计
     */
    private function getTaskStats()
    {
        $today = strtotime(date('Y-m-d'));
        
        return [
            'total_tasks' => \think\facade\Db::name('tasks')->count(),
            'pending_tasks' => \think\facade\Db::name('tasks')->where('status', TaskService::STATUS_PENDING)->count(),
            'running_tasks' => \think\facade\Db::name('tasks')->where('status', TaskService::STATUS_RUNNING)->count(),
            'completed_tasks' => \think\facade\Db::name('tasks')->where('status', TaskService::STATUS_COMPLETED)->count(),
            'today_tasks' => \think\facade\Db::name('tasks')->where('create_time', '>=', $today)->count()
        ];
    }
    
    /**
     * 获取积分统计
     */
    private function getScoreStats()
    {
        $today = strtotime(date('Y-m-d'));
        
        return [
            'total_recharge' => \think\facade\Db::name('score_logs')->where('type', ScoreService::TYPE_RECHARGE)->sum('amount'),
            'total_consumption' => \think\facade\Db::name('score_logs')->where('type', ScoreService::TYPE_CONSUME)->sum('ABS(amount)'),
            'today_recharge' => \think\facade\Db::name('score_logs')->where('type', ScoreService::TYPE_RECHARGE)->where('create_time', '>=', $today)->sum('amount'),
            'today_consumption' => \think\facade\Db::name('score_logs')->where('type', ScoreService::TYPE_CONSUME)->where('create_time', '>=', $today)->sum('ABS(amount)')
        ];
    }
    
    /**
     * 获取最近活动
     */
    private function getRecentActivities()
    {
        return \think\facade\Db::name('security_logs')
            ->alias('sl')
            ->leftJoin('users u', 'sl.user_id = u.id')
            ->field('sl.*, u.nickname')
            ->order('sl.create_time', 'desc')
            ->limit(10)
            ->select();
    }

    /**
     * 获取用户列表
     */
    private function getUserList($page, $limit, $filters)
    {
        $where = [];

        if (!empty($filters['keyword'])) {
            $where[] = ['nickname|mobile_hash', 'like', '%' . $filters['keyword'] . '%'];
        }

        if (isset($filters['status']) && $filters['status'] !== '') {
            $where[] = ['status', '=', $filters['status']];
        }

        if (!empty($filters['group_id'])) {
            $where[] = ['group_id', '=', $filters['group_id']];
        }

        if (!empty($filters['start_date'])) {
            $where[] = ['create_time', '>=', strtotime($filters['start_date'])];
        }

        if (!empty($filters['end_date'])) {
            $where[] = ['create_time', '<=', strtotime($filters['end_date'] . ' 23:59:59')];
        }

        $query = \think\facade\Db::name('users')
            ->alias('u')
            ->leftJoin('user_groups ug', 'u.group_id = ug.id')
            ->field('u.*, ug.name as group_name')
            ->where($where)
            ->order('u.create_time', 'desc');

        $total = $query->count();
        $list = $query->page($page, $limit)->select();

        // 格式化数据
        foreach ($list as &$item) {
            $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['update_time_text'] = date('Y-m-d H:i:s', $item['update_time']);
            $item['mobile'] = '***' . substr($item['mobile_hash'], -4); // 脱敏显示
        }

        return [
            'total' => $total,
            'list' => $list
        ];
    }

    /**
     * 获取任务列表
     */
    private function getTaskList($page, $limit, $filters)
    {
        $where = [];

        if (!empty($filters['keyword'])) {
            $where[] = ['keyword', 'like', '%' . $filters['keyword'] . '%'];
        }

        if (isset($filters['status']) && $filters['status'] !== '') {
            $where[] = ['status', '=', $filters['status']];
        }

        if (!empty($filters['type'])) {
            $where[] = ['type', '=', $filters['type']];
        }

        if (!empty($filters['user_id'])) {
            $where[] = ['user_id', '=', $filters['user_id']];
        }

        if (!empty($filters['start_date'])) {
            $where[] = ['create_time', '>=', strtotime($filters['start_date'])];
        }

        if (!empty($filters['end_date'])) {
            $where[] = ['create_time', '<=', strtotime($filters['end_date'] . ' 23:59:59')];
        }

        $query = \think\facade\Db::name('tasks')
            ->alias('t')
            ->leftJoin('users u', 't.user_id = u.id')
            ->field('t.*, u.nickname as user_nickname')
            ->where($where)
            ->order('t.create_time', 'desc');

        $total = $query->count();
        $list = $query->page($page, $limit)->select();

        // 格式化数据
        foreach ($list as &$item) {
            $item['type_text'] = TaskService::getTypeText($item['type']);
            $item['status_text'] = TaskService::getStatusText($item['status']);
            $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['update_time_text'] = date('Y-m-d H:i:s', $item['update_time']);
        }

        return [
            'total' => $total,
            'list' => $list
        ];
    }

    /**
     * 获取充值统计
     */
    private function getRechargeStats($startDate, $endDate)
    {
        $startTime = strtotime($startDate);
        $endTime = strtotime($endDate . ' 23:59:59');

        return [
            'total_amount' => \think\facade\Db::name('score_logs')
                ->where('type', ScoreService::TYPE_RECHARGE)
                ->where('create_time', 'between', [$startTime, $endTime])
                ->sum('amount'),
            'total_count' => \think\facade\Db::name('score_logs')
                ->where('type', ScoreService::TYPE_RECHARGE)
                ->where('create_time', 'between', [$startTime, $endTime])
                ->count(),
            'avg_amount' => \think\facade\Db::name('score_logs')
                ->where('type', ScoreService::TYPE_RECHARGE)
                ->where('create_time', 'between', [$startTime, $endTime])
                ->avg('amount')
        ];
    }

    /**
     * 获取消费统计
     */
    private function getConsumptionStats($startDate, $endDate)
    {
        $startTime = strtotime($startDate);
        $endTime = strtotime($endDate . ' 23:59:59');

        return [
            'total_amount' => \think\facade\Db::name('score_logs')
                ->where('type', ScoreService::TYPE_CONSUME)
                ->where('create_time', 'between', [$startTime, $endTime])
                ->sum('ABS(amount)'),
            'total_count' => \think\facade\Db::name('score_logs')
                ->where('type', ScoreService::TYPE_CONSUME)
                ->where('create_time', 'between', [$startTime, $endTime])
                ->count()
        ];
    }

    /**
     * 获取退款统计
     */
    private function getRefundStats($startDate, $endDate)
    {
        $startTime = strtotime($startDate);
        $endTime = strtotime($endDate . ' 23:59:59');

        return [
            'total_amount' => \think\facade\Db::name('score_logs')
                ->where('type', ScoreService::TYPE_REFUND)
                ->where('create_time', 'between', [$startTime, $endTime])
                ->sum('amount'),
            'total_count' => \think\facade\Db::name('score_logs')
                ->where('type', ScoreService::TYPE_REFUND)
                ->where('create_time', 'between', [$startTime, $endTime])
                ->count()
        ];
    }

    /**
     * 获取每日财务趋势
     */
    private function getDailyFinancialTrend($startDate, $endDate)
    {
        $startTime = strtotime($startDate);
        $endTime = strtotime($endDate . ' 23:59:59');

        return \think\facade\Db::name('score_logs')
            ->field([
                'DATE(FROM_UNIXTIME(create_time)) as date',
                'type',
                'SUM(CASE WHEN type = 1 THEN amount ELSE 0 END) as recharge_amount',
                'SUM(CASE WHEN type = 2 THEN ABS(amount) ELSE 0 END) as consume_amount',
                'SUM(CASE WHEN type = 3 THEN amount ELSE 0 END) as refund_amount'
            ])
            ->where('create_time', 'between', [$startTime, $endTime])
            ->group('date')
            ->order('date', 'asc')
            ->select();
    }
}
