<?php

namespace app\validate;

use think\Validate;

/**
 * 管理员验证器
 */
class AdminValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'username' => 'require|length:3,20|alphaNum',
        'password' => 'require|length:6,20',
        'old_password' => 'require|length:6,20',
        'new_password' => 'require|length:6,20|different:old_password',
        'confirm_password' => 'require|confirm:new_password',
        'nickname' => 'length:2,20',
        'email' => 'email',
        'phone' => 'mobile',
        'role' => 'require|in:super_admin,admin,operator',
        'status' => 'in:0,1',
        'captcha' => 'length:4,6'
    ];

    /**
     * 错误消息
     */
    protected $message = [
        'username.require' => '用户名不能为空',
        'username.length' => '用户名长度必须在3-20位之间',
        'username.alphaNum' => '用户名只能包含字母和数字',
        'password.require' => '密码不能为空',
        'password.length' => '密码长度必须在6-20位之间',
        'old_password.require' => '原密码不能为空',
        'old_password.length' => '原密码长度必须在6-20位之间',
        'new_password.require' => '新密码不能为空',
        'new_password.length' => '新密码长度必须在6-20位之间',
        'new_password.different' => '新密码不能与原密码相同',
        'confirm_password.require' => '确认密码不能为空',
        'confirm_password.confirm' => '确认密码与新密码不一致',
        'nickname.length' => '昵称长度必须在2-20位之间',
        'email.email' => '邮箱格式不正确',
        'phone.mobile' => '手机号格式不正确',
        'role.require' => '角色不能为空',
        'role.in' => '角色类型无效',
        'status.in' => '状态值无效',
        'captcha.length' => '验证码长度必须在4-6位之间'
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'login' => ['username', 'password', 'captcha'],
        'create' => ['username', 'password', 'nickname', 'email', 'phone', 'role'],
        'update' => ['nickname', 'email', 'phone', 'role', 'status'],
        'changePassword' => ['old_password', 'new_password', 'confirm_password']
    ];

    /**
     * 登录场景
     */
    public function sceneLogin()
    {
        return $this->only(['username', 'password', 'captcha'])
                   ->remove('captcha', 'require'); // 验证码可选
    }

    /**
     * 创建管理员场景
     */
    public function sceneCreate()
    {
        return $this->only(['username', 'password', 'nickname', 'email', 'phone', 'role'])
                   ->append('username', 'uniqueUsername')
                   ->append('email', 'uniqueEmail')
                   ->append('phone', 'uniquePhone')
                   ->append('password', 'strongPassword');
    }

    /**
     * 更新管理员场景
     */
    public function sceneUpdate()
    {
        return $this->only(['nickname', 'email', 'phone', 'role', 'status'])
                   ->append('email', 'uniqueEmail')
                   ->append('phone', 'uniquePhone');
    }

    /**
     * 修改密码场景
     */
    public function sceneChangePassword()
    {
        return $this->only(['old_password', 'new_password', 'confirm_password'])
                   ->append('new_password', 'strongPassword');
    }

    /**
     * 自定义验证：用户名唯一性
     */
    protected function uniqueUsername($value, $rule, $data = [])
    {
        $adminId = $data['admin_id'] ?? 0;
        
        $count = \think\facade\Db::name('admins')
            ->where('username', $value)
            ->where('id', '<>', $adminId)
            ->count();
            
        return $count == 0 ? true : '用户名已被使用';
    }

    /**
     * 自定义验证：邮箱唯一性
     */
    protected function uniqueEmail($value, $rule, $data = [])
    {
        if (empty($value)) {
            return true; // 邮箱可以为空
        }
        
        $adminId = $data['admin_id'] ?? 0;
        
        $count = \think\facade\Db::name('admins')
            ->where('email', $value)
            ->where('id', '<>', $adminId)
            ->count();
            
        return $count == 0 ? true : '邮箱已被使用';
    }

    /**
     * 自定义验证：手机号唯一性
     */
    protected function uniquePhone($value, $rule, $data = [])
    {
        if (empty($value)) {
            return true; // 手机号可以为空
        }
        
        $adminId = $data['admin_id'] ?? 0;
        
        $count = \think\facade\Db::name('admins')
            ->where('phone', $value)
            ->where('id', '<>', $adminId)
            ->count();
            
        return $count == 0 ? true : '手机号已被使用';
    }

    /**
     * 自定义验证：密码强度
     */
    protected function strongPassword($value, $rule, $data = [])
    {
        // 管理员密码必须包含字母、数字和特殊字符
        if (!preg_match('/^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$/', $value)) {
            return '密码必须包含字母、数字和特殊字符，长度8-20位';
        }
        return true;
    }

    /**
     * 自定义验证：角色权限检查
     */
    protected function checkRolePermission($value, $rule, $data = [])
    {
        // 获取当前操作的管理员
        $currentAdmin = request()->admin ?? null;
        
        if (!$currentAdmin) {
            return '无权限操作';
        }
        
        // 超级管理员可以设置任何角色
        if ($currentAdmin['role'] == 'super_admin') {
            return true;
        }
        
        // 普通管理员不能设置超级管理员角色
        if ($value == 'super_admin') {
            return '无权限设置超级管理员角色';
        }
        
        // 普通管理员不能设置管理员角色
        if ($currentAdmin['role'] == 'admin' && $value == 'admin') {
            return '无权限设置管理员角色';
        }
        
        return true;
    }

    /**
     * 自定义验证：手机号格式
     */
    protected function mobile($value, $rule, $data = [])
    {
        if (empty($value)) {
            return true; // 手机号可以为空
        }
        
        return preg_match('/^1[3-9]\d{9}$/', $value);
    }

    /**
     * 自定义验证：检查管理员状态
     */
    protected function checkAdminStatus($value, $rule, $data = [])
    {
        $adminId = $data['admin_id'] ?? 0;
        
        if (empty($adminId)) {
            return true;
        }
        
        // 获取当前操作的管理员
        $currentAdmin = request()->admin ?? null;
        
        if (!$currentAdmin) {
            return '无权限操作';
        }
        
        // 不能修改自己的状态
        if ($adminId == $currentAdmin['id']) {
            return '不能修改自己的状态';
        }
        
        // 获取目标管理员信息
        $targetAdmin = \think\facade\Db::name('admins')->where('id', $adminId)->find();
        
        if (!$targetAdmin) {
            return '管理员不存在';
        }
        
        // 超级管理员可以修改任何人的状态
        if ($currentAdmin['role'] == 'super_admin') {
            return true;
        }
        
        // 不能修改超级管理员的状态
        if ($targetAdmin['role'] == 'super_admin') {
            return '无权限修改超级管理员状态';
        }
        
        // 普通管理员不能修改其他管理员的状态
        if ($currentAdmin['role'] == 'admin' && $targetAdmin['role'] == 'admin') {
            return '无权限修改其他管理员状态';
        }
        
        return true;
    }

    /**
     * 自定义验证：检查删除权限
     */
    protected function checkDeletePermission($value, $rule, $data = [])
    {
        $adminId = $data['admin_id'] ?? 0;
        
        if (empty($adminId)) {
            return '管理员ID不能为空';
        }
        
        // 获取当前操作的管理员
        $currentAdmin = request()->admin ?? null;
        
        if (!$currentAdmin) {
            return '无权限操作';
        }
        
        // 不能删除自己
        if ($adminId == $currentAdmin['id']) {
            return '不能删除自己';
        }
        
        // 获取目标管理员信息
        $targetAdmin = \think\facade\Db::name('admins')->where('id', $adminId)->find();
        
        if (!$targetAdmin) {
            return '管理员不存在';
        }
        
        // 超级管理员可以删除任何人（除了自己）
        if ($currentAdmin['role'] == 'super_admin') {
            return true;
        }
        
        // 不能删除超级管理员
        if ($targetAdmin['role'] == 'super_admin') {
            return '无权限删除超级管理员';
        }
        
        // 普通管理员不能删除其他管理员
        if ($currentAdmin['role'] == 'admin' && $targetAdmin['role'] == 'admin') {
            return '无权限删除其他管理员';
        }
        
        return true;
    }
}
