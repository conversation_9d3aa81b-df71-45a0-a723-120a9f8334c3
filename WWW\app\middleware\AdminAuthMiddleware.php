<?php

namespace app\middleware;

use think\Request;
use think\Response;
use think\exception\ValidateException;
use app\service\JwtService;
use app\service\SecurityService;
use think\facade\Db;

/**
 * 管理员认证中间件
 */
class AdminAuthMiddleware
{
    /**
     * 处理请求
     */
    public function handle(Request $request, \Closure $next)
    {
        // 获取Token
        $token = $this->getTokenFromRequest($request);
        
        if (empty($token)) {
            return $this->unauthorizedResponse('缺少认证Token');
        }
        
        try {
            // 验证Token
            $payload = JwtService::validateToken($token);
            $userData = $payload['data'] ?? [];
            
            if (empty($userData['admin_id'])) {
                return $this->unauthorizedResponse('Token数据无效');
            }
            
            // 获取管理员信息
            $admin = $this->getAdminById($userData['admin_id']);
            
            if (!$admin) {
                return $this->unauthorizedResponse('管理员不存在');
            }
            
            if ($admin['status'] != 1) {
                return $this->unauthorizedResponse('管理员账户已被禁用');
            }
            
            // 检查IP是否被封禁
            if (SecurityService::isIpBlocked($request->ip())) {
                return $this->forbiddenResponse('IP已被封禁');
            }
            
            // 检查权限
            if (!$this->checkPermission($admin, $request)) {
                return $this->forbiddenResponse('权限不足');
            }
            
            // 将管理员信息存储到请求中
            $request->admin = $admin;
            $request->token = $token;
            $request->tokenData = $userData;
            
            // 记录管理员活动
            $this->logAdminActivity($admin['id'], $request);
            
        } catch (ValidateException $e) {
            return $this->unauthorizedResponse($e->getMessage());
        } catch (\Exception $e) {
            return $this->unauthorizedResponse('Token验证失败');
        }
        
        return $next($request);
    }
    
    /**
     * 获取管理员信息
     */
    private function getAdminById($adminId)
    {
        return Db::name('admins')
            ->alias('a')
            ->leftJoin('admin_roles r', 'a.role_id = r.id')
            ->field('a.*, r.name as role_name, r.permissions')
            ->where('a.id', $adminId)
            ->find();
    }
    
    /**
     * 检查权限
     */
    private function checkPermission($admin, Request $request)
    {
        // 超级管理员拥有所有权限
        if ($admin['role_id'] == 1) {
            return true;
        }
        
        // 获取当前请求的权限标识
        $permission = $this->getRequiredPermission($request);
        
        if (empty($permission)) {
            return true; // 不需要特殊权限
        }
        
        // 检查管理员是否拥有该权限
        $permissions = json_decode($admin['permissions'] ?: '[]', true);
        
        return in_array($permission, $permissions);
    }
    
    /**
     * 获取请求需要的权限
     */
    private function getRequiredPermission(Request $request)
    {
        $route = $request->route();
        $controller = $route->getController();
        $action = $route->getAction();
        
        // 根据控制器和方法映射权限
        $permissionMap = [
            // 用户管理
            'admin.user.index' => 'user.view',
            'admin.user.show' => 'user.view',
            'admin.user.update' => 'user.edit',
            'admin.user.delete' => 'user.delete',
            
            // 任务管理
            'admin.task.index' => 'task.view',
            'admin.task.show' => 'task.view',
            'admin.task.review' => 'task.review',
            'admin.task.update' => 'task.edit',
            'admin.task.delete' => 'task.delete',
            
            // 积分管理
            'admin.score.index' => 'score.view',
            'admin.score.recharge' => 'score.recharge',
            'admin.score.refund' => 'score.refund',
            
            // 系统管理
            'admin.system.config' => 'system.config',
            'admin.system.logs' => 'system.logs',
            
            // 管理员管理
            'admin.admin.index' => 'admin.view',
            'admin.admin.create' => 'admin.create',
            'admin.admin.update' => 'admin.edit',
            'admin.admin.delete' => 'admin.delete',
        ];
        
        $routeName = strtolower($controller . '.' . $action);
        
        return $permissionMap[$routeName] ?? null;
    }
    
    /**
     * 从请求中获取Token
     */
    private function getTokenFromRequest(Request $request)
    {
        // 优先从Header中获取
        $authorization = $request->header('Authorization');
        if ($authorization && strpos($authorization, 'Bearer ') === 0) {
            return substr($authorization, 7);
        }
        
        // 从GET参数获取
        $token = $request->get('token');
        if ($token) {
            return $token;
        }
        
        // 从POST参数获取
        $token = $request->post('token');
        if ($token) {
            return $token;
        }
        
        return null;
    }
    
    /**
     * 记录管理员活动
     */
    private function logAdminActivity($adminId, Request $request)
    {
        SecurityService::logUserAction($adminId, 'admin_access', [
            'method' => $request->method(),
            'url' => $request->url(),
            'ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent')
        ]);
    }
    
    /**
     * 返回未授权响应
     */
    private function unauthorizedResponse($message = '未授权访问')
    {
        return json([
            'code' => 401,
            'message' => $message,
            'data' => null
        ], 401);
    }
    
    /**
     * 返回禁止访问响应
     */
    private function forbiddenResponse($message = '权限不足')
    {
        return json([
            'code' => 403,
            'message' => $message,
            'data' => null
        ], 403);
    }
}
