<?php

namespace app\controller\api;

use think\Request;
use think\facade\Db;
use app\service\TaskService;
use app\service\ScoreService;

/**
 * 用户中心控制器
 */
class UserController extends BaseController
{
    /**
     * 用户仪表盘
     */
    public function dashboard(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            // 获取用户基本信息
            $userInfo = [
                'id' => $user['id'],
                'username' => $user['username'],
                'nickname' => $user['nickname'],
                'user_group' => $user['user_group'],
                'score_balance' => $user['score_balance'],
                'total_recharge' => $user['total_recharge'],
                'total_consumption' => $user['total_consumption'],
                'register_time' => $user['create_time']
            ];
            
            // 获取任务统计
            $taskStats = $this->getTaskStats($user['id']);
            
            // 获取积分统计
            $scoreStats = $this->getScoreStats($user['id']);
            
            // 获取最近任务
            $recentTasks = $this->getRecentTasks($user['id']);
            
            // 获取最近积分记录
            $recentScores = $this->getRecentScores($user['id']);
            
            // 获取系统通知
            $notifications = $this->getNotifications($user['id']);
            
            return $this->success([
                'user' => $userInfo,
                'task_stats' => $taskStats,
                'score_stats' => $scoreStats,
                'recent_tasks' => $recentTasks,
                'recent_scores' => $recentScores,
                'notifications' => $notifications
            ]);
            
        } catch (\Exception $e) {
            return $this->error('获取仪表盘数据失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取通知列表
     */
    public function notifications(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $page = $request->param('page', 1);
            $limit = $request->param('limit', 20);
            $type = $request->param('type', '');
            $status = $request->param('status', '');
            
            $where = ['user_id' => $user['id']];
            
            if (!empty($type)) {
                $where['type'] = $type;
            }
            
            if ($status !== '') {
                $where['is_read'] = $status;
            }
            
            $notifications = Db::name('user_notifications')
                ->where($where)
                ->order('create_time', 'desc')
                ->paginate([
                    'list_rows' => $limit,
                    'page' => $page
                ]);
            
            return $this->success($this->paginate($notifications));
            
        } catch (\Exception $e) {
            return $this->error('获取通知列表失败：' . $e->getMessage());
        }
    }
    
    /**
     * 标记通知为已读
     */
    public function markNotificationRead(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $notificationId = $request->param('id');
            
            if (empty($notificationId)) {
                return $this->error('通知ID不能为空');
            }
            
            // 检查通知是否属于当前用户
            $notification = Db::name('user_notifications')
                ->where('id', $notificationId)
                ->where('user_id', $user['id'])
                ->find();
            
            if (!$notification) {
                return $this->error('通知不存在');
            }
            
            // 标记为已读
            $result = Db::name('user_notifications')
                ->where('id', $notificationId)
                ->update([
                    'is_read' => 1,
                    'read_time' => time(),
                    'update_time' => time()
                ]);
            
            if ($result) {
                return $this->success(null, '标记成功');
            } else {
                return $this->error('标记失败');
            }
            
        } catch (\Exception $e) {
            return $this->error('标记通知失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取任务统计
     */
    private function getTaskStats($userId)
    {
        $stats = [
            'total' => 0,
            'running' => 0,
            'completed' => 0,
            'cancelled' => 0,
            'pending_review' => 0
        ];
        
        try {
            $stats['total'] = Db::name('tasks')->where('user_id', $userId)->count();
            $stats['running'] = Db::name('tasks')->where('user_id', $userId)->where('status', TaskService::STATUS_RUNNING)->count();
            $stats['completed'] = Db::name('tasks')->where('user_id', $userId)->where('status', TaskService::STATUS_COMPLETED)->count();
            $stats['cancelled'] = Db::name('tasks')->where('user_id', $userId)->where('status', TaskService::STATUS_CANCELLED)->count();
            $stats['pending_review'] = Db::name('tasks')->where('user_id', $userId)->where('status', TaskService::STATUS_PENDING)->count();
        } catch (\Exception $e) {
            // 忽略统计错误
        }
        
        return $stats;
    }
    
    /**
     * 获取积分统计
     */
    private function getScoreStats($userId)
    {
        $stats = [
            'today_consumption' => 0,
            'month_consumption' => 0,
            'today_recharge' => 0,
            'month_recharge' => 0
        ];
        
        try {
            $todayStart = strtotime(date('Y-m-d'));
            $monthStart = strtotime(date('Y-m-01'));
            
            // 今日消费
            $stats['today_consumption'] = Db::name('score_logs')
                ->where('user_id', $userId)
                ->where('type', ScoreService::TYPE_CONSUMPTION)
                ->where('create_time', '>=', $todayStart)
                ->sum('amount');
            
            // 本月消费
            $stats['month_consumption'] = Db::name('score_logs')
                ->where('user_id', $userId)
                ->where('type', ScoreService::TYPE_CONSUMPTION)
                ->where('create_time', '>=', $monthStart)
                ->sum('amount');
            
            // 今日充值
            $stats['today_recharge'] = Db::name('score_logs')
                ->where('user_id', $userId)
                ->where('type', ScoreService::TYPE_RECHARGE)
                ->where('create_time', '>=', $todayStart)
                ->sum('amount');
            
            // 本月充值
            $stats['month_recharge'] = Db::name('score_logs')
                ->where('user_id', $userId)
                ->where('type', ScoreService::TYPE_RECHARGE)
                ->where('create_time', '>=', $monthStart)
                ->sum('amount');
                
        } catch (\Exception $e) {
            // 忽略统计错误
        }
        
        return $stats;
    }
    
    /**
     * 获取最近任务
     */
    private function getRecentTasks($userId)
    {
        try {
            return Db::name('tasks')
                ->where('user_id', $userId)
                ->field('id, keyword, type, status, daily_clicks, daily_searches, create_time, update_time')
                ->order('create_time', 'desc')
                ->limit(5)
                ->select();
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取最近积分记录
     */
    private function getRecentScores($userId)
    {
        try {
            return Db::name('score_logs')
                ->where('user_id', $userId)
                ->field('type, amount, description, create_time')
                ->order('create_time', 'desc')
                ->limit(5)
                ->select();
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取系统通知
     */
    private function getNotifications($userId)
    {
        try {
            return Db::name('user_notifications')
                ->where('user_id', $userId)
                ->where('is_read', 0)
                ->field('id, type, title, content, create_time')
                ->order('create_time', 'desc')
                ->limit(5)
                ->select();
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取用户等级信息
     */
    public function levelInfo(Request $request)
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }
            
            $userGroup = $user['user_group'];
            $totalConsumption = $user['total_consumption'];
            
            // 获取等级配置
            $levelConfig = config('system.user_groups');
            $currentLevel = $levelConfig[$userGroup] ?? null;
            
            if (!$currentLevel) {
                return $this->error('用户等级配置错误');
            }
            
            // 计算下一等级
            $nextLevel = $this->getNextLevel($userGroup, $levelConfig);
            
            // 计算升级进度
            $progress = 0;
            if ($nextLevel) {
                $currentThreshold = $currentLevel['upgrade_threshold'] ?? 0;
                $nextThreshold = $nextLevel['upgrade_threshold'] ?? 0;
                
                if ($nextThreshold > $currentThreshold) {
                    $progress = min(100, ($totalConsumption - $currentThreshold) / ($nextThreshold - $currentThreshold) * 100);
                }
            }
            
            return $this->success([
                'current_level' => [
                    'name' => $currentLevel['name'],
                    'discount' => $currentLevel['discount'],
                    'daily_task_limit' => $currentLevel['daily_task_limit'],
                    'benefits' => $currentLevel['benefits'] ?? []
                ],
                'next_level' => $nextLevel ? [
                    'name' => $nextLevel['name'],
                    'upgrade_threshold' => $nextLevel['upgrade_threshold'],
                    'discount' => $nextLevel['discount'],
                    'benefits' => $nextLevel['benefits'] ?? []
                ] : null,
                'total_consumption' => $totalConsumption,
                'upgrade_progress' => round($progress, 2)
            ]);
            
        } catch (\Exception $e) {
            return $this->error('获取等级信息失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取下一等级
     */
    private function getNextLevel($currentGroup, $levelConfig)
    {
        $levels = ['new_user', 'regular_user', 'advanced_user', 'senior_user', 'vip_user'];
        $currentIndex = array_search($currentGroup, $levels);
        
        if ($currentIndex !== false && $currentIndex < count($levels) - 1) {
            $nextGroup = $levels[$currentIndex + 1];
            return $levelConfig[$nextGroup] ?? null;
        }
        
        return null;
    }
}
