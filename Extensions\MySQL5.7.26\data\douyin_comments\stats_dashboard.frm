TYPE=VIEW
query=select \'总体统计\' AS `category`,count(0) AS `total_comments`,count(distinct `douyin_comments`.`comments`.`video_id`) AS `total_videos`,count(distinct `douyin_comments`.`comments`.`uid`) AS `total_users`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes`,min(`douyin_comments`.`comments`.`collection_time`) AS `earliest_collection`,max(`douyin_comments`.`comments`.`collection_time`) AS `latest_collection` from `douyin_comments`.`comments` union all select \'今日统计\' AS `category`,count(0) AS `total_comments`,count(distinct `douyin_comments`.`comments`.`video_id`) AS `total_videos`,count(distinct `douyin_comments`.`comments`.`uid`) AS `total_users`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes`,min(`douyin_comments`.`comments`.`collection_time`) AS `earliest_collection`,max(`douyin_comments`.`comments`.`collection_time`) AS `latest_collection` from `douyin_comments`.`comments` where (cast(`douyin_comments`.`comments`.`collection_time` as date) = curdate())
md5=03bb86d37bf99b286e23bc2ce86e8e20
updatable=0
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=2025-06-23 12:49:47
create-version=1
source=SELECT 
\n    \'总体统计\' as category,
\n    COUNT(*) as total_comments,
\n    COUNT(DISTINCT video_id) as total_videos,
\n    COUNT(DISTINCT uid) as total_users,
\n    SUM(digg_count) as total_likes,
\n    MIN(collection_time) as earliest_collection,
\n    MAX(collection_time) as latest_collection
\nFROM comments
\nUNION ALL
\nSELECT 
\n    \'今日统计\' as category,
\n    COUNT(*) as total_comments,
\n    COUNT(DISTINCT video_id) as total_videos,
\n    COUNT(DISTINCT uid) as total_users,
\n    SUM(digg_count) as total_likes,
\n    MIN(collection_time) as earliest_collection,
\n    MAX(collection_time) as latest_collection
\nFROM comments 
\nWHERE DATE(collection_time) = CURDATE()
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_general_ci
view_body_utf8=select \'总体统计\' AS `category`,count(0) AS `total_comments`,count(distinct `douyin_comments`.`comments`.`video_id`) AS `total_videos`,count(distinct `douyin_comments`.`comments`.`uid`) AS `total_users`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes`,min(`douyin_comments`.`comments`.`collection_time`) AS `earliest_collection`,max(`douyin_comments`.`comments`.`collection_time`) AS `latest_collection` from `douyin_comments`.`comments` union all select \'今日统计\' AS `category`,count(0) AS `total_comments`,count(distinct `douyin_comments`.`comments`.`video_id`) AS `total_videos`,count(distinct `douyin_comments`.`comments`.`uid`) AS `total_users`,sum(`douyin_comments`.`comments`.`digg_count`) AS `total_likes`,min(`douyin_comments`.`comments`.`collection_time`) AS `earliest_collection`,max(`douyin_comments`.`comments`.`collection_time`) AS `latest_collection` from `douyin_comments`.`comments` where (cast(`douyin_comments`.`comments`.`collection_time` as date) = curdate())
