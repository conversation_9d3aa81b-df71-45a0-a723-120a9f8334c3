<?php

namespace app\service;

use think\facade\Db;
use think\facade\Cache;
use think\exception\ValidateException;
use app\service\UserService;
use app\service\ScoreService;
use app\service\SensitiveWordService;

/**
 * 任务服务类
 */
class TaskService
{
    // 任务类型
    const TYPE_KEYWORD_RANKING = 1; // 关键词排名
    const TYPE_DROPDOWN_WORDS = 2;  // 下拉词
    
    // 任务状态
    const STATUS_PENDING = 1;    // 待审核
    const STATUS_RUNNING = 2;    // 执行中
    const STATUS_COMPLETED = 3;  // 已完成
    const STATUS_PAUSED = 4;     // 已暂停
    const STATUS_CANCELLED = 5;  // 已取消
    
    /**
     * 创建任务
     */
    public static function createTask($userId, $data)
    {
        // 验证用户
        $user = UserService::getUserById($userId);
        if (!$user) {
            throw new ValidateException('用户不存在');
        }
        
        if ($user['status'] != 1) {
            throw new ValidateException('账户状态异常');
        }
        
        // 验证任务数据
        self::validateTaskData($data);
        
        // 检查敏感词
        if (config('system.sensitive_word_check')) {
            $sensitiveResult = SensitiveWordService::check($data['keyword']);
            if (!$sensitiveResult['passed']) {
                throw new ValidateException('关键词包含敏感词：' . implode('、', $sensitiveResult['words']));
            }
        }
        
        // 检查每日任务限制
        $todayTaskCount = self::getTodayTaskCount($userId);
        if ($todayTaskCount >= $user['max_daily_tasks']) {
            throw new ValidateException('今日任务数量已达上限');
        }
        
        // 计算积分消费
        $scoreInfo = self::calculateScore($data, $user['score_cost']);
        
        // 检查积分余额
        if ($user['score'] < $scoreInfo['total_score']) {
            throw new ValidateException('积分余额不足');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 创建任务
            $taskData = [
                'user_id' => $userId,
                'type' => $data['type'],
                'keyword' => $data['keyword'],
                'url' => $data['url'] ?? null,
                'dropdown_words' => isset($data['dropdown_words']) ? json_encode($data['dropdown_words']) : null,
                'search_engine' => $data['search_engine'],
                'online_days' => $data['online_days'],
                'daily_clicks' => $data['daily_clicks'] ?? null,
                'daily_searches' => $data['daily_searches'] ?? null,
                'click_time_range' => $data['click_time_range'] ?? null,
                'region' => $data['region'] ?? null,
                'pre_deduct_score' => $scoreInfo['total_score'],
                'score_cost' => $user['score_cost'],
                'status' => self::STATUS_PENDING,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $taskId = Db::name('tasks')->insertGetId($taskData);
            
            // 预扣积分
            ScoreService::consume($userId, $taskId, $scoreInfo['total_score'], '任务预扣积分');
            
            Db::commit();
            
            // 清除相关缓存
            self::clearTaskCache($userId);
            
            return $taskId;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 审核任务
     */
    public static function reviewTask($taskId, $adminId, $status, $remark = '')
    {
        $task = self::getTaskById($taskId);
        if (!$task) {
            throw new ValidateException('任务不存在');
        }
        
        if ($task['status'] != self::STATUS_PENDING) {
            throw new ValidateException('任务状态不允许审核');
        }
        
        $updateData = [
            'status' => $status,
            'admin_id' => $adminId,
            'review_time' => time(),
            'review_remark' => $remark,
            'update_time' => time()
        ];
        
        // 如果审核通过，设置开始时间
        if ($status == self::STATUS_RUNNING) {
            $updateData['start_time'] = time();
        }
        
        Db::startTrans();
        try {
            // 更新任务状态
            Db::name('tasks')->where('id', $taskId)->update($updateData);
            
            // 如果审核不通过，退还积分
            if ($status == self::STATUS_CANCELLED) {
                $refundAmount = $task['pre_deduct_score'] * config('system.score_refund_rate', 0.8);
                ScoreService::refund($task['user_id'], $taskId, $refundAmount, '任务审核不通过退款', $adminId);
            }
            
            Db::commit();
            
            // 清除缓存
            self::clearTaskCache($task['user_id']);
            
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 暂停任务
     */
    public static function pauseTask($taskId, $userId = null, $adminId = null)
    {
        $task = self::getTaskById($taskId);
        if (!$task) {
            throw new ValidateException('任务不存在');
        }
        
        // 权限检查
        if ($userId && $task['user_id'] != $userId) {
            throw new ValidateException('无权操作此任务');
        }
        
        if ($task['status'] != self::STATUS_RUNNING) {
            throw new ValidateException('只能暂停执行中的任务');
        }
        
        $result = Db::name('tasks')->where('id', $taskId)->update([
            'status' => self::STATUS_PAUSED,
            'update_time' => time()
        ]);
        
        if ($result) {
            self::clearTaskCache($task['user_id']);
        }
        
        return $result;
    }
    
    /**
     * 恢复任务
     */
    public static function resumeTask($taskId, $userId = null, $adminId = null)
    {
        $task = self::getTaskById($taskId);
        if (!$task) {
            throw new ValidateException('任务不存在');
        }
        
        // 权限检查
        if ($userId && $task['user_id'] != $userId) {
            throw new ValidateException('无权操作此任务');
        }
        
        if ($task['status'] != self::STATUS_PAUSED) {
            throw new ValidateException('只能恢复已暂停的任务');
        }
        
        $result = Db::name('tasks')->where('id', $taskId)->update([
            'status' => self::STATUS_RUNNING,
            'update_time' => time()
        ]);
        
        if ($result) {
            self::clearTaskCache($task['user_id']);
        }
        
        return $result;
    }
    
    /**
     * 完成任务
     */
    public static function completeTask($taskId, $completionRate = 100)
    {
        $task = self::getTaskById($taskId);
        if (!$task) {
            throw new ValidateException('任务不存在');
        }
        
        if (!in_array($task['status'], [self::STATUS_RUNNING, self::STATUS_PAUSED])) {
            throw new ValidateException('任务状态不允许完成');
        }
        
        Db::startTrans();
        try {
            // 更新任务状态
            Db::name('tasks')->where('id', $taskId)->update([
                'status' => self::STATUS_COMPLETED,
                'end_time' => time(),
                'completion_rate' => $completionRate,
                'update_time' => time()
            ]);
            
            // 计算实际消费和退款
            $actualCost = $task['pre_deduct_score'] * ($completionRate / 100);
            $refundAmount = $task['pre_deduct_score'] - $actualCost;
            
            // 更新实际消费
            Db::name('tasks')->where('id', $taskId)->update([
                'actual_cost_score' => $actualCost
            ]);
            
            // 如果有退款，处理退款
            if ($refundAmount > 0) {
                ScoreService::refund($task['user_id'], $taskId, $refundAmount, '任务完成退款');
            }
            
            Db::commit();
            
            // 清除缓存
            self::clearTaskCache($task['user_id']);
            
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 获取任务详情
     */
    public static function getTaskById($taskId)
    {
        return Db::name('tasks')
            ->alias('t')
            ->leftJoin('users u', 't.user_id = u.id')
            ->field('t.*, u.nickname as user_nickname')
            ->where('t.id', $taskId)
            ->find();
    }
    
    /**
     * 获取用户任务列表
     */
    public static function getUserTasks($userId, $page = 1, $limit = 20, $filters = [])
    {
        $where = [['user_id', '=', $userId]];
        
        // 添加筛选条件
        if (isset($filters['type']) && $filters['type'] !== '') {
            $where[] = ['type', '=', $filters['type']];
        }
        
        if (isset($filters['status']) && $filters['status'] !== '') {
            $where[] = ['status', '=', $filters['status']];
        }
        
        if (isset($filters['keyword']) && $filters['keyword'] !== '') {
            $where[] = ['keyword', 'like', '%' . $filters['keyword'] . '%'];
        }
        
        $query = Db::name('tasks')->where($where)->order('create_time', 'desc');
        
        $total = $query->count();
        $list = $query->page($page, $limit)->select();
        
        // 格式化数据
        foreach ($list as &$item) {
            $item = self::formatTaskInfo($item);
        }
        
        return [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 获取今日任务数量
     */
    public static function getTodayTaskCount($userId)
    {
        $todayStart = strtotime(date('Y-m-d'));
        
        return Db::name('tasks')
            ->where('user_id', $userId)
            ->where('create_time', '>=', $todayStart)
            ->count();
    }
    
    /**
     * 计算任务积分
     */
    private static function calculateScore($data, $scoreCost)
    {
        $baseScore = $data['online_days'] * ($data['daily_clicks'] ?? $data['daily_searches'] ?? 50);
        
        // 根据任务类型调整系数
        $multiplier = 1.0;
        if ($data['type'] == self::TYPE_DROPDOWN_WORDS) {
            $multiplier = 1.5; // 下拉词任务费用更高
        }
        
        $totalScore = ceil($baseScore * $scoreCost * $multiplier);
        
        return [
            'base_score' => $baseScore,
            'multiplier' => $multiplier,
            'score_cost' => $scoreCost,
            'total_score' => $totalScore
        ];
    }
    
    /**
     * 验证任务数据
     */
    private static function validateTaskData($data)
    {
        $required = ['type', 'keyword', 'search_engine', 'online_days'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '') {
                throw new ValidateException("缺少必需参数：{$field}");
            }
        }
        
        // 验证任务类型
        if (!in_array($data['type'], [self::TYPE_KEYWORD_RANKING, self::TYPE_DROPDOWN_WORDS])) {
            throw new ValidateException('无效的任务类型');
        }
        
        // 验证关键词长度
        if (mb_strlen($data['keyword']) > config('system.max_keyword_length', 50)) {
            throw new ValidateException('关键词长度超出限制');
        }
        
        // 验证上线天数
        $minDays = config('system.min_online_days', 1);
        $maxDays = config('system.max_online_days', 30);
        
        if ($data['online_days'] < $minDays || $data['online_days'] > $maxDays) {
            throw new ValidateException("上线天数必须在{$minDays}-{$maxDays}天之间");
        }
        
        // 关键词排名任务需要URL
        if ($data['type'] == self::TYPE_KEYWORD_RANKING && empty($data['url'])) {
            throw new ValidateException('关键词排名任务必须提供目标URL');
        }
    }
    
    /**
     * 格式化任务信息
     */
    private static function formatTaskInfo($task)
    {
        $task['type_text'] = self::getTypeText($task['type']);
        $task['status_text'] = self::getStatusText($task['status']);
        $task['create_time_text'] = date('Y-m-d H:i:s', $task['create_time']);
        $task['review_time_text'] = $task['review_time'] ? date('Y-m-d H:i:s', $task['review_time']) : '';
        $task['start_time_text'] = $task['start_time'] ? date('Y-m-d H:i:s', $task['start_time']) : '';
        $task['end_time_text'] = $task['end_time'] ? date('Y-m-d H:i:s', $task['end_time']) : '';
        
        if ($task['dropdown_words']) {
            $task['dropdown_words'] = json_decode($task['dropdown_words'], true);
        }
        
        return $task;
    }
    
    /**
     * 获取任务类型文本
     */
    public static function getTypeText($type)
    {
        $types = [
            self::TYPE_KEYWORD_RANKING => '关键词排名',
            self::TYPE_DROPDOWN_WORDS => '下拉词'
        ];
        
        return $types[$type] ?? '未知';
    }
    
    /**
     * 获取任务状态文本
     */
    public static function getStatusText($status)
    {
        $statuses = [
            self::STATUS_PENDING => '待审核',
            self::STATUS_RUNNING => '执行中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_PAUSED => '已暂停',
            self::STATUS_CANCELLED => '已取消'
        ];
        
        return $statuses[$status] ?? '未知';
    }
    
    /**
     * 清除任务缓存
     */
    private static function clearTaskCache($userId)
    {
        Cache::delete("user:daily_tasks:{$userId}");
        Cache::delete("user:tasks:{$userId}");
    }
}
