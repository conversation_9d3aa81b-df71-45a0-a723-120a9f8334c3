	DeflateFilterNote Input instream
	DeflateFilterNote Output outstream
	DeflateFilterNote Ratio ratio
	LogFormat '"%r" %{outstream}n/%{instream}n (%{ratio}n%%)' deflate
	CustomLog "${SRVROOT}/logs/deflate.log" deflate

	<Location />
		AddOutputFilterByType DEFLATE text/html text/plain text/css

		# Netscape 4.x has some problems...
		BrowserMatch ^Mozilla/4 gzip-only-text/html

		# Netscape 4.06-4.08 have some more problems
		BrowserMatch ^Mozilla/4\.0[678] no-gzip

		# MSIE masquerades as Netscape, but it is fine
		BrowserMatch \bMSIE !no-gzip !gzip-only-text/html

		# Don't compress images
		SetEnvIfNoCase Request_URI \
		\.(?:gif|jpe?g|png)$ no-gzip dont-vary

		<IfModule headers_module>
			# Make sure proxies don't deliver the wrong content
			Header append Vary User-Agent env=!dont-vary
		</IfModule>
	</Location>
