TYPE=VIEW
query=select `a`.`id` AS `id`,`a`.`account_name` AS `account_name`,`a`.`sec_uid` AS `sec_uid`,`a`.`max_messages_per_hour` AS `max_messages_per_hour`,`a`.`status` AS `status`,coalesce(`s`.`total_tasks`,0) AS `total_tasks`,coalesce(`s`.`active_tasks`,0) AS `active_tasks`,coalesce(`s`.`total_comments`,0) AS `total_comments`,coalesce(`s`.`high_intent_comments`,0) AS `high_intent_comments`,coalesce(`s`.`total_messages_sent`,0) AS `total_messages_sent`,coalesce(`s`.`messages_sent_today`,0) AS `messages_sent_today`,`s`.`last_message_time` AS `last_message_time`,`a`.`created_time` AS `created_time`,`a`.`description` AS `description` from (`douyin_comments`.`dy_accounts` `a` left join `douyin_comments`.`dy_account_statistics` `s` on((`a`.`id` = `s`.`account_id`)))
md5=203507f8e4b8d814f88c7b405c5d686b
updatable=0
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=2025-06-25 12:11:06
create-version=1
source=SELECT
\n    a.id,
\n    a.account_name,
\n    a.sec_uid,
\n    a.max_messages_per_hour,
\n    a.status,
\n    COALESCE(s.total_tasks, 0) as total_tasks,
\n    COALESCE(s.active_tasks, 0) as active_tasks,
\n    COALESCE(s.total_comments, 0) as total_comments,
\n    COALESCE(s.high_intent_comments, 0) as high_intent_comments,
\n    COALESCE(s.total_messages_sent, 0) as total_messages_sent,
\n    COALESCE(s.messages_sent_today, 0) as messages_sent_today,
\n    s.last_message_time,
\n    a.created_time,
\n    a.description
\nFROM dy_accounts a
\nLEFT JOIN dy_account_statistics s ON a.id = s.account_id
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_general_ci
view_body_utf8=select `a`.`id` AS `id`,`a`.`account_name` AS `account_name`,`a`.`sec_uid` AS `sec_uid`,`a`.`max_messages_per_hour` AS `max_messages_per_hour`,`a`.`status` AS `status`,coalesce(`s`.`total_tasks`,0) AS `total_tasks`,coalesce(`s`.`active_tasks`,0) AS `active_tasks`,coalesce(`s`.`total_comments`,0) AS `total_comments`,coalesce(`s`.`high_intent_comments`,0) AS `high_intent_comments`,coalesce(`s`.`total_messages_sent`,0) AS `total_messages_sent`,coalesce(`s`.`messages_sent_today`,0) AS `messages_sent_today`,`s`.`last_message_time` AS `last_message_time`,`a`.`created_time` AS `created_time`,`a`.`description` AS `description` from (`douyin_comments`.`dy_accounts` `a` left join `douyin_comments`.`dy_account_statistics` `s` on((`a`.`id` = `s`.`account_id`)))
