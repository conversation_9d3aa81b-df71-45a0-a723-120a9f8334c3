# SEO积分系统 - 技术实现细节

## 🔧 核心服务类实现

### 积分服务 (ScoreService)
```php
<?php
namespace app\service;

use think\facade\Db;
use think\facade\Cache;
use app\exception\ApiException;

class ScoreService
{
    /**
     * 添加积分记录
     */
    public static function addLog($userId, $taskId, $type, $amount, $description, $adminId = null)
    {
        // 获取用户当前余额
        $user = Db::name('users')->where('id', $userId)->find();
        if (!$user) {
            throw new ApiException('用户不存在');
        }
        
        $balanceBefore = $user['score'];
        $balanceAfter = $balanceBefore + $amount;
        
        // 检查余额是否足够
        if ($balanceAfter < 0) {
            throw new ApiException('积分余额不足');
        }
        
        Db::transaction(function() use ($userId, $taskId, $type, $amount, $description, $adminId, $balanceBefore, $balanceAfter) {
            // 更新用户余额
            Db::name('users')->where('id', $userId)->update([
                'score' => $balanceAfter,
                'update_time' => time()
            ]);
            
            // 添加积分记录
            Db::name('score_logs')->insert([
                'user_id' => $userId,
                'task_id' => $taskId,
                'type' => $type,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description,
                'admin_id' => $adminId,
                'create_time' => time()
            ]);
            
            // 更新累计消费（如果是消费类型）
            if ($type == 2 && $amount < 0) {
                Db::name('users')->where('id', $userId)->inc('total_consumption', abs($amount));
            }
        });
        
        // 清除用户缓存
        Cache::delete('user_info_' . $userId);
        
        return true;
    }
    
    /**
     * 批量处理积分
     */
    public static function batchProcess($operations)
    {
        Db::transaction(function() use ($operations) {
            foreach ($operations as $operation) {
                self::addLog(
                    $operation['user_id'],
                    $operation['task_id'] ?? null,
                    $operation['type'],
                    $operation['amount'],
                    $operation['description'],
                    $operation['admin_id'] ?? null
                );
            }
        });
    }
    
    /**
     * 获取积分统计
     */
    public static function getStatistics($userId, $period = 'month')
    {
        $timeRange = self::getTimeRange($period);
        
        return [
            'total_recharge' => Db::name('score_logs')
                ->where('user_id', $userId)
                ->where('type', 1)
                ->where('create_time', 'between', $timeRange)
                ->sum('amount'),
            'total_consumption' => abs(Db::name('score_logs')
                ->where('user_id', $userId)
                ->where('type', 2)
                ->where('create_time', 'between', $timeRange)
                ->sum('amount')),
            'total_refund' => Db::name('score_logs')
                ->where('user_id', $userId)
                ->where('type', 3)
                ->where('create_time', 'between', $timeRange)
                ->sum('amount')
        ];
    }
    
    private static function getTimeRange($period)
    {
        switch ($period) {
            case 'today':
                return [strtotime('today'), time()];
            case 'week':
                return [strtotime('-7 days'), time()];
            case 'month':
                return [strtotime('-30 days'), time()];
            default:
                return [strtotime('-30 days'), time()];
        }
    }
}
```

### 敏感词检测服务
```php
<?php
namespace app\service;

use think\facade\Cache;
use think\facade\Db;

class SensitiveWordService
{
    /**
     * 检测敏感词
     */
    public static function check($text)
    {
        $sensitiveWords = self::getSensitiveWords();
        $foundWords = [];
        
        foreach ($sensitiveWords as $word) {
            if (stripos($text, $word['word']) !== false) {
                $foundWords[] = $word;
            }
        }
        
        return $foundWords;
    }
    
    /**
     * 获取敏感词列表
     */
    private static function getSensitiveWords()
    {
        return Cache::remember('sensitive_words', function() {
            return Db::name('sensitive_words')
                ->where('status', 1)
                ->field('word,level,action,replacement')
                ->select()
                ->toArray();
        }, 7200);
    }
    
    /**
     * 处理敏感词
     */
    public static function process($text, $foundWords)
    {
        foreach ($foundWords as $word) {
            switch ($word['action']) {
                case 1: // 警告
                    // 记录警告日志
                    break;
                case 2: // 拒绝
                    throw new ApiException('内容包含敏感词：' . $word['word']);
                case 3: // 替换
                    $text = str_ireplace($word['word'], $word['replacement'] ?: '***', $text);
                    break;
            }
        }
        
        return $text;
    }
}
```

### 队列任务处理
```php
<?php
namespace app\job;

use think\queue\Job;
use think\facade\Db;
use think\facade\Log;

class TaskProcessJob
{
    /**
     * 处理任务队列
     */
    public function fire(Job $job, $data)
    {
        try {
            $taskId = $data['task_id'];
            $task = Db::name('tasks')->where('id', $taskId)->find();
            
            if (!$task) {
                $job->delete();
                return;
            }
            
            // 模拟任务处理
            $this->processTask($task);
            
            // 更新任务状态
            Db::name('tasks')->where('id', $taskId)->update([
                'status' => 2, // 执行中
                'start_time' => time(),
                'end_time' => time() + ($task['online_days'] * 86400),
                'update_time' => time()
            ]);
            
            Log::info('Task processing started', ['task_id' => $taskId]);
            $job->delete();
            
        } catch (\Exception $e) {
            Log::error('Task processing failed', [
                'task_id' => $data['task_id'] ?? 0,
                'error' => $e->getMessage()
            ]);
            
            if ($job->attempts() > 3) {
                $job->delete();
            } else {
                $job->release(60); // 60秒后重试
            }
        }
    }
    
    private function processTask($task)
    {
        // 这里实现具体的任务处理逻辑
        // 比如调用第三方SEO服务API
        
        // 模拟处理时间
        sleep(2);
        
        return true;
    }
}
```

## 🔐 中间件实现

### JWT认证中间件
```php
<?php
namespace app\middleware;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use think\Response;

class JwtAuthMiddleware
{
    public function handle($request, \Closure $next)
    {
        $token = $request->header('Authorization');
        
        if (!$token) {
            return json(['code' => 401, 'message' => '缺少认证令牌'], 401);
        }
        
        // 移除 Bearer 前缀
        $token = str_replace('Bearer ', '', $token);
        
        try {
            $payload = JWT::decode($token, new Key(config('jwt.secret'), 'HS256'));
            
            // 检查token是否过期
            if ($payload->exp < time()) {
                return json(['code' => 401, 'message' => '令牌已过期'], 401);
            }
            
            // 将用户信息添加到请求中
            $request->user = [
                'id' => $payload->user_id,
                'mobile' => $payload->mobile,
                'group_id' => $payload->group_id
            ];
            
        } catch (\Exception $e) {
            return json(['code' => 401, 'message' => '无效的认证令牌'], 401);
        }
        
        return $next($request);
    }
}
```

### 频率限制中间件
```php
<?php
namespace app\middleware;

use think\facade\Cache;
use think\Response;

class RateLimitMiddleware
{
    public function handle($request, \Closure $next)
    {
        $key = $this->generateKey($request);
        $limit = $this->getLimit($request);
        
        $current = Cache::get($key, 0);
        
        if ($current >= $limit['max']) {
            return json([
                'code' => 429,
                'message' => '请求过于频繁，请稍后再试',
                'retry_after' => $limit['window']
            ], 429);
        }
        
        Cache::set($key, $current + 1, $limit['window']);
        
        return $next($request);
    }
    
    private function generateKey($request)
    {
        $userId = $request->user['id'] ?? 'guest';
        $ip = $request->ip();
        $route = $request->controller() . '/' . $request->action();
        
        return "rate_limit:{$route}:{$userId}:{$ip}";
    }
    
    private function getLimit($request)
    {
        $route = $request->controller() . '/' . $request->action();
        
        $limits = [
            'user/login' => ['max' => 5, 'window' => 300],
            'user/tasks' => ['max' => 100, 'window' => 3600],
            'user/recharge' => ['max' => 10, 'window' => 3600],
        ];
        
        return $limits[$route] ?? ['max' => 60, 'window' => 60];
    }
}
```

## 📊 数据库分区管理

### 自动分区脚本
```sql
-- 创建分区管理存储过程
DELIMITER $$
CREATE PROCEDURE CreateMonthlyPartition(IN table_name VARCHAR(64), IN partition_date DATE)
BEGIN
    DECLARE partition_name VARCHAR(64);
    DECLARE partition_value INT;
    
    SET partition_name = CONCAT('p_', DATE_FORMAT(partition_date, '%Y%m'));
    SET partition_value = UNIX_TIMESTAMP(DATE_ADD(partition_date, INTERVAL 1 MONTH));
    
    SET @sql = CONCAT('ALTER TABLE ', table_name, 
                     ' ADD PARTITION (PARTITION ', partition_name,
                     ' VALUES LESS THAN (', partition_value, '))');
    
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

-- 创建清理旧分区存储过程
DELIMITER $$
CREATE PROCEDURE DropOldPartitions(IN table_name VARCHAR(64), IN months_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE partition_name VARCHAR(64);
    DECLARE partition_description TEXT;
    
    DECLARE partition_cursor CURSOR FOR
        SELECT PARTITION_NAME, PARTITION_DESCRIPTION
        FROM INFORMATION_SCHEMA.PARTITIONS
        WHERE TABLE_NAME = table_name
        AND PARTITION_NAME IS NOT NULL
        AND PARTITION_NAME != 'p_future'
        AND CAST(PARTITION_DESCRIPTION AS UNSIGNED) < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL months_to_keep MONTH));
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN partition_cursor;
    
    read_loop: LOOP
        FETCH partition_cursor INTO partition_name, partition_description;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        SET @sql = CONCAT('ALTER TABLE ', table_name, ' DROP PARTITION ', partition_name);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END LOOP;
    
    CLOSE partition_cursor;
END$$
DELIMITER ;
```

### 分区维护定时任务
```php
<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

class PartitionMaintenanceCommand extends Command
{
    protected function configure()
    {
        $this->setName('partition:maintenance')
             ->setDescription('Maintain database partitions');
    }
    
    protected function execute(Input $input, Output $output)
    {
        try {
            // 创建下个月的分区
            $this->createNextMonthPartition();
            
            // 清理3个月前的分区
            $this->cleanOldPartitions(3);
            
            $output->writeln('Partition maintenance completed successfully');
            
        } catch (\Exception $e) {
            $output->writeln('Partition maintenance failed: ' . $e->getMessage());
        }
    }
    
    private function createNextMonthPartition()
    {
        $nextMonth = date('Y-m-01', strtotime('+1 month'));
        
        $tables = ['score_logs', 'task_execution_logs', 'user_login_logs'];
        
        foreach ($tables as $table) {
            Db::execute("CALL CreateMonthlyPartition(?, ?)", [$table, $nextMonth]);
        }
    }
    
    private function cleanOldPartitions($monthsToKeep)
    {
        $tables = ['score_logs', 'task_execution_logs', 'user_login_logs'];
        
        foreach ($tables as $table) {
            Db::execute("CALL DropOldPartitions(?, ?)", [$table, $monthsToKeep]);
        }
    }
}
```

## 🔍 性能监控实现

### 慢查询监控
```php
<?php
namespace app\service;

use think\facade\Db;
use think\facade\Log;

class PerformanceMonitorService
{
    /**
     * 监控数据库查询性能
     */
    public static function monitorQuery($sql, $time, $bindings = [])
    {
        // 记录慢查询（超过1秒）
        if ($time > 1000) {
            Log::warning('Slow Query Detected', [
                'sql' => $sql,
                'time' => $time . 'ms',
                'bindings' => $bindings,
                'memory' => memory_get_usage(true),
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
            
            // 存储到数据库
            Db::name('performance_logs')->insert([
                'type' => 'slow_query',
                'content' => json_encode([
                    'sql' => $sql,
                    'bindings' => $bindings,
                    'time' => $time
                ]),
                'create_time' => time()
            ]);
        }
    }
    
    /**
     * 监控内存使用
     */
    public static function monitorMemory()
    {
        $usage = memory_get_usage(true);
        $peak = memory_get_peak_usage(true);
        
        // 内存使用超过128MB时记录
        if ($usage > 128 * 1024 * 1024) {
            Log::warning('High Memory Usage', [
                'current' => self::formatBytes($usage),
                'peak' => self::formatBytes($peak),
                'limit' => ini_get('memory_limit')
            ]);
        }
    }
    
    private static function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $i = 0;
        while ($bytes >= 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
```

---

**实现要点**:
1. **服务类设计**: 采用单一职责原则，每个服务类专注特定功能
2. **事务处理**: 关键操作使用数据库事务确保数据一致性
3. **缓存策略**: 合理使用缓存提高性能，注意缓存失效处理
4. **异常处理**: 统一异常处理机制，提供友好的错误信息
5. **队列处理**: 异步处理耗时任务，提高系统响应速度
6. **监控日志**: 完善的日志记录和性能监控机制
