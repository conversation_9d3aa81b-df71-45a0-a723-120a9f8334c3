<?php

return [
    // JWT密钥
    'secret' => env('JWT_SECRET', 'your-jwt-secret-key'),
    
    // Token有效期（秒）
    'ttl' => env('JWT_TTL', 7200), // 2小时
    
    // 刷新Token有效期（分钟）
    'refresh_ttl' => env('JWT_REFRESH_TTL', 20160), // 14天
    
    // 加密算法
    'algo' => 'HS256',
    
    // 必需的声明
    'required_claims' => [
        'iss', // 签发者
        'iat', // 签发时间
        'exp', // 过期时间
        'nbf', // 生效时间
        'sub', // 主题
        'jti'  // JWT ID
    ],
    
    // 是否启用黑名单
    'blacklist_enabled' => true,
    
    // 黑名单宽限期
    'blacklist_grace_period' => 0,
    
    // 签发者
    'issuer' => 'seo-score-system',
    
    // 受众
    'audience' => 'seo-score-system-users',
];
