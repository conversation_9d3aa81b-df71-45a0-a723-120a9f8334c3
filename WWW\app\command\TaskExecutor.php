<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\service\TaskService;
use app\service\TaskExecutionService;
use app\service\ScoreService;

/**
 * 任务执行器命令
 */
class TaskExecutor extends Command
{
    protected function configure()
    {
        $this->setName('task:execute')
            ->setDescription('执行SEO任务');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始执行SEO任务...');
        
        try {
            // 获取待执行的任务
            $tasks = $this->getPendingTasks();
            
            if (empty($tasks)) {
                $output->writeln('没有待执行的任务');
                return;
            }
            
            $output->writeln('找到 ' . count($tasks) . ' 个待执行任务');
            
            foreach ($tasks as $task) {
                $this->executeTask($task, $output);
            }
            
            $output->writeln('任务执行完成');
            
        } catch (\Exception $e) {
            $output->writeln('任务执行失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取待执行的任务
     */
    private function getPendingTasks()
    {
        return Db::name('tasks')
            ->where('status', TaskService::STATUS_RUNNING)
            ->where('next_execute_time', '<=', time())
            ->limit(50) // 每次最多执行50个任务
            ->select();
    }
    
    /**
     * 执行单个任务
     */
    private function executeTask($task, Output $output)
    {
        $output->writeln("执行任务 #{$task['id']}: {$task['keyword']}");
        
        try {
            $result = false;
            
            switch ($task['type']) {
                case TaskService::TYPE_KEYWORD_RANKING:
                    $result = $this->executeKeywordRankingTask($task);
                    break;
                    
                case TaskService::TYPE_DROPDOWN_WORDS:
                    $result = $this->executeDropdownWordsTask($task);
                    break;
                    
                default:
                    $output->writeln("未知任务类型: {$task['type']}");
                    return;
            }
            
            if ($result) {
                $this->handleTaskSuccess($task, $output);
            } else {
                $this->handleTaskFailure($task, $output);
            }
            
        } catch (\Exception $e) {
            $this->handleTaskError($task, $e, $output);
        }
    }
    
    /**
     * 执行关键词排名任务
     */
    private function executeKeywordRankingTask($task)
    {
        // 模拟关键词排名任务执行
        // 实际项目中这里会调用搜索引擎API或使用爬虫
        
        $keyword = $task['keyword'];
        $url = $task['url'];
        $searchEngine = $task['search_engine'];
        $dailyClicks = $task['daily_clicks'];
        
        // 记录执行日志
        TaskExecutionService::logExecution($task['id'], TaskExecutionService::STATUS_SUCCESS, [
            'keyword' => $keyword,
            'url' => $url,
            'search_engine' => $searchEngine,
            'clicks_performed' => $dailyClicks,
            'execution_type' => 'keyword_ranking'
        ]);
        
        // 模拟成功率（实际项目中根据真实执行结果）
        return mt_rand(1, 100) <= 85; // 85%成功率
    }
    
    /**
     * 执行下拉词任务
     */
    private function executeDropdownWordsTask($task)
    {
        // 模拟下拉词任务执行
        
        $keyword = $task['keyword'];
        $dropdownWords = json_decode($task['dropdown_words'], true);
        $searchEngine = $task['search_engine'];
        $dailySearches = $task['daily_searches'];
        
        // 记录执行日志
        TaskExecutionService::logExecution($task['id'], TaskExecutionService::STATUS_SUCCESS, [
            'keyword' => $keyword,
            'dropdown_words' => $dropdownWords,
            'search_engine' => $searchEngine,
            'searches_performed' => $dailySearches,
            'execution_type' => 'dropdown_words'
        ]);
        
        // 模拟成功率
        return mt_rand(1, 100) <= 80; // 80%成功率
    }
    
    /**
     * 处理任务成功
     */
    private function handleTaskSuccess($task, Output $output)
    {
        $output->writeln("任务 #{$task['id']} 执行成功");
        
        // 更新任务执行次数和下次执行时间
        $executedDays = $task['executed_days'] + 1;
        $nextExecuteTime = $this->calculateNextExecuteTime($task);
        
        $updateData = [
            'executed_days' => $executedDays,
            'next_execute_time' => $nextExecuteTime,
            'last_execute_time' => time(),
            'update_time' => time()
        ];
        
        // 检查任务是否完成
        if ($executedDays >= $task['online_days']) {
            $updateData['status'] = TaskService::STATUS_COMPLETED;
            $updateData['complete_time'] = time();
            
            // 计算实际消费积分
            $actualCost = $this->calculateActualCost($task);
            $updateData['actual_cost_score'] = $actualCost;
            
            // 如果预扣积分大于实际消费，退还差额
            if ($task['pre_deduct_score'] > $actualCost) {
                $refundAmount = $task['pre_deduct_score'] - $actualCost;
                ScoreService::refund($task['user_id'], $refundAmount, "任务完成退款 - 任务ID: {$task['id']}", $task['id']);
            }
            
            $output->writeln("任务 #{$task['id']} 已完成");
        }
        
        Db::name('tasks')->where('id', $task['id'])->update($updateData);
    }
    
    /**
     * 处理任务失败
     */
    private function handleTaskFailure($task, Output $output)
    {
        $output->writeln("任务 #{$task['id']} 执行失败");
        
        // 记录失败日志
        TaskExecutionService::logExecution($task['id'], TaskExecutionService::STATUS_FAILED, [
            'reason' => 'execution_failed',
            'retry_count' => $task['retry_count'] + 1
        ]);
        
        $retryCount = $task['retry_count'] + 1;
        $maxRetries = 3;
        
        if ($retryCount >= $maxRetries) {
            // 超过最大重试次数，标记为失败
            Db::name('tasks')->where('id', $task['id'])->update([
                'status' => TaskService::STATUS_CANCELLED,
                'retry_count' => $retryCount,
                'update_time' => time(),
                'remark' => '执行失败次数过多，自动取消'
            ]);
            
            // 退还预扣积分
            ScoreService::refund($task['user_id'], $task['pre_deduct_score'], "任务失败退款 - 任务ID: {$task['id']}", $task['id']);
            
            $output->writeln("任务 #{$task['id']} 已取消（重试次数过多）");
        } else {
            // 增加重试次数，延后执行时间
            $nextExecuteTime = time() + 3600; // 1小时后重试
            
            Db::name('tasks')->where('id', $task['id'])->update([
                'retry_count' => $retryCount,
                'next_execute_time' => $nextExecuteTime,
                'update_time' => time()
            ]);
            
            $output->writeln("任务 #{$task['id']} 将在1小时后重试（第{$retryCount}次重试）");
        }
    }
    
    /**
     * 处理任务错误
     */
    private function handleTaskError($task, \Exception $e, Output $output)
    {
        $output->writeln("任务 #{$task['id']} 执行异常: " . $e->getMessage());
        
        // 记录异常日志
        TaskExecutionService::logExecution($task['id'], TaskExecutionService::STATUS_FAILED, [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        // 按失败处理
        $this->handleTaskFailure($task, $output);
    }
    
    /**
     * 计算下次执行时间
     */
    private function calculateNextExecuteTime($task)
    {
        // 根据点击时间段计算下次执行时间
        $timeRange = $task['click_time_range'];
        $tomorrow = strtotime(date('Y-m-d', time() + 86400));
        
        switch ($timeRange) {
            case 'morning':
                return $tomorrow + mt_rand(8 * 3600, 12 * 3600); // 8-12点
            case 'afternoon':
                return $tomorrow + mt_rand(14 * 3600, 18 * 3600); // 14-18点
            case 'evening':
                return $tomorrow + mt_rand(19 * 3600, 23 * 3600); // 19-23点
            case 'all_day':
            default:
                return $tomorrow + mt_rand(8 * 3600, 22 * 3600); // 8-22点
        }
    }
    
    /**
     * 计算实际消费积分
     */
    private function calculateActualCost($task)
    {
        // 根据任务类型和执行天数计算实际消费
        $baseCost = 0;
        
        switch ($task['type']) {
            case TaskService::TYPE_KEYWORD_RANKING:
                $baseCost = $task['daily_clicks'] * 0.1; // 每次点击0.1积分
                break;
            case TaskService::TYPE_DROPDOWN_WORDS:
                $baseCost = $task['daily_searches'] * 0.05; // 每次搜索0.05积分
                break;
        }
        
        return $baseCost * $task['executed_days'];
    }
}
