<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - SEO积分系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .header {
            background: #343a40;
            color: white;
            padding: 15px 0;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .admin-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .login-form {
            background: white;
            border-radius: 10px;
            padding: 40px;
            max-width: 400px;
            margin: 50px auto;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .dashboard {
            display: none;
        }
        
        .sidebar {
            position: fixed;
            left: 0;
            top: 70px;
            width: 250px;
            height: calc(100vh - 70px);
            background: white;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 20px 0;
        }
        
        .sidebar-menu li {
            margin-bottom: 5px;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: background 0.3s;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #007bff;
            color: white;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h3 {
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        .status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .status.active {
            background: #d4edda;
            color: #155724;
        }
        
        .status.inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .error {
            color: #dc3545;
            margin-top: 10px;
            font-size: 0.9em;
        }
        
        .success {
            color: #28a745;
            margin-top: 10px;
            font-size: 0.9em;
        }
        
        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .logout-btn:hover {
            background: #c82333;
        }
        
        .content-section {
            display: none;
        }
        
        .content-section.active {
            display: block;
        }
        
        .search-box {
            margin-bottom: 20px;
        }
        
        .search-box input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 300px;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 0.9em;
            margin: 0 2px;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">SEO积分系统 - 管理后台</div>
            <div class="admin-info" id="adminInfo" style="display: none;">
                <span id="adminName"></span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <!-- 登录表单 -->
    <div id="loginForm" class="login-form">
        <h2 style="text-align: center; margin-bottom: 30px; color: #333;">管理员登录</h2>
        <form onsubmit="login(event)">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required placeholder="请输入管理员用户名">
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required placeholder="请输入密码">
            </div>
            <button type="submit" class="btn" id="loginBtn">登录</button>
            <div id="loginError" class="error"></div>
        </form>
        <div style="text-align: center; margin-top: 20px; color: #666;">
            <p>默认账号：admin / admin123456</p>
            <p><a href="/" style="color: #007bff;">返回首页</a></p>
        </div>
    </div>

    <!-- 管理仪表盘 -->
    <div id="dashboard" class="dashboard">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <ul class="sidebar-menu">
                <li><a href="#" class="active" onclick="showSection('overview')">系统概览</a></li>
                <li><a href="#" onclick="showSection('users')">用户管理</a></li>
                <li><a href="#" onclick="showSection('tasks')">任务管理</a></li>
                <li><a href="#" onclick="showSection('scores')">积分管理</a></li>
                <li><a href="#" onclick="showSection('security')">安全日志</a></li>
                <li><a href="#" onclick="showSection('settings')">系统设置</a></li>
            </ul>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 系统概览 -->
            <div id="overviewSection" class="content-section active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="totalUsers">0</div>
                        <div class="stat-label">总用户数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="totalTasks">0</div>
                        <div class="stat-label">总任务数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="totalRevenue">0</div>
                        <div class="stat-label">总收入</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="todayRevenue">0</div>
                        <div class="stat-label">今日收入</div>
                    </div>
                </div>

                <div class="card">
                    <h3>最近活动</h3>
                    <div id="recentActivities">
                        <p style="text-align: center; color: #666; padding: 20px;">加载中...</p>
                    </div>
                </div>
            </div>

            <!-- 用户管理 -->
            <div id="usersSection" class="content-section">
                <div class="card">
                    <h3>用户管理</h3>
                    <div class="search-box">
                        <input type="text" placeholder="搜索用户..." id="userSearch">
                        <button class="btn btn-small" onclick="searchUsers()">搜索</button>
                    </div>
                    <div id="usersList">
                        <p style="text-align: center; color: #666; padding: 20px;">加载中...</p>
                    </div>
                </div>
            </div>

            <!-- 任务管理 -->
            <div id="tasksSection" class="content-section">
                <div class="card">
                    <h3>任务管理</h3>
                    <div class="search-box">
                        <input type="text" placeholder="搜索任务..." id="taskSearch">
                        <button class="btn btn-small" onclick="searchTasks()">搜索</button>
                    </div>
                    <div id="tasksList">
                        <p style="text-align: center; color: #666; padding: 20px;">加载中...</p>
                    </div>
                </div>
            </div>

            <!-- 积分管理 -->
            <div id="scoresSection" class="content-section">
                <div class="card">
                    <h3>积分记录</h3>
                    <div id="scoresList">
                        <p style="text-align: center; color: #666; padding: 20px;">加载中...</p>
                    </div>
                </div>
            </div>

            <!-- 安全日志 -->
            <div id="securitySection" class="content-section">
                <div class="card">
                    <h3>安全日志</h3>
                    <div id="securityLogs">
                        <p style="text-align: center; color: #666; padding: 20px;">加载中...</p>
                    </div>
                </div>
            </div>

            <!-- 系统设置 -->
            <div id="settingsSection" class="content-section">
                <div class="card">
                    <h3>系统设置</h3>
                    <div id="systemSettings">
                        <p style="text-align: center; color: #666; padding: 20px;">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        let authToken = localStorage.getItem('adminToken');

        // 页面加载时检查登录状态
        window.onload = function() {
            if (authToken) {
                checkAuth();
            }
        };

        // 登录
        async function login(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const errorDiv = document.getElementById('loginError');
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            errorDiv.textContent = '';
            
            try {
                const response = await fetch(`${API_BASE}/admin/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    authToken = result.data.token;
                    localStorage.setItem('adminToken', authToken);
                    showDashboard();
                    loadDashboardData();
                } else {
                    errorDiv.textContent = result.message || '登录失败';
                }
            } catch (error) {
                errorDiv.textContent = '网络错误，请重试';
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        }

        // 检查认证状态
        async function checkAuth() {
            try {
                const response = await fetch(`${API_BASE}/admin/auth/profile`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.status === 200) {
                    showDashboard();
                    loadDashboardData();
                } else {
                    logout();
                }
            } catch (error) {
                logout();
            }
        }

        // 显示仪表盘
        function showDashboard() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('dashboard').style.display = 'block';
            document.getElementById('adminInfo').style.display = 'flex';
        }

        // 加载仪表盘数据
        async function loadDashboardData() {
            try {
                const response = await fetch(`${API_BASE}/admin/dashboard`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const data = result.data;
                    
                    // 更新统计数据
                    document.getElementById('totalUsers').textContent = data.stats.total_users;
                    document.getElementById('totalTasks').textContent = data.stats.total_tasks;
                    document.getElementById('totalRevenue').textContent = data.stats.total_revenue;
                    document.getElementById('todayRevenue').textContent = data.stats.today_revenue;
                    document.getElementById('adminName').textContent = data.admin.username;
                    
                    // 加载最近活动
                    loadRecentActivities(data.recent_activities);
                }
            } catch (error) {
                console.error('加载数据失败:', error);
            }
        }

        // 加载最近活动
        function loadRecentActivities(activities) {
            const activitiesDiv = document.getElementById('recentActivities');
            
            if (activities && activities.length > 0) {
                activitiesDiv.innerHTML = `
                    <table class="table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>用户</th>
                                <th>操作</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${activities.map(activity => `
                                <tr>
                                    <td>${new Date(activity.create_time * 1000).toLocaleString()}</td>
                                    <td>${activity.username}</td>
                                    <td>${activity.description}</td>
                                    <td><span class="status ${activity.status}">${activity.status}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            } else {
                activitiesDiv.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无活动记录</p>';
            }
        }

        // 显示指定部分
        function showSection(sectionName) {
            // 移除所有活动状态
            document.querySelectorAll('.sidebar-menu a').forEach(link => link.classList.remove('active'));
            document.querySelectorAll('.content-section').forEach(section => section.classList.remove('active'));
            
            // 激活当前部分
            event.target.classList.add('active');
            document.getElementById(sectionName + 'Section').classList.add('active');
            
            // 根据部分加载相应数据
            switch(sectionName) {
                case 'users':
                    loadUsers();
                    break;
                case 'tasks':
                    loadTasks();
                    break;
                case 'scores':
                    loadScores();
                    break;
                case 'security':
                    loadSecurityLogs();
                    break;
                case 'settings':
                    loadSettings();
                    break;
            }
        }

        // 加载用户列表
        async function loadUsers() {
            try {
                const response = await fetch(`${API_BASE}/admin/users`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const users = result.data;
                    const usersList = document.getElementById('usersList');
                    
                    if (users && users.length > 0) {
                        usersList.innerHTML = `
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>用户名</th>
                                        <th>等级</th>
                                        <th>积分余额</th>
                                        <th>状态</th>
                                        <th>注册时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${users.map(user => `
                                        <tr>
                                            <td>${user.id}</td>
                                            <td>${user.username}</td>
                                            <td>${getUserGroupText(user.user_group)}</td>
                                            <td>${user.score_balance}</td>
                                            <td><span class="status ${user.status === 1 ? 'active' : 'inactive'}">${user.status === 1 ? '正常' : '禁用'}</span></td>
                                            <td>${new Date(user.create_time * 1000).toLocaleDateString()}</td>
                                            <td>
                                                <button class="btn btn-small btn-warning" onclick="editUser(${user.id})">编辑</button>
                                                <button class="btn btn-small ${user.status === 1 ? 'btn-danger' : 'btn-success'}" onclick="toggleUserStatus(${user.id}, ${user.status})">
                                                    ${user.status === 1 ? '禁用' : '启用'}
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        `;
                    } else {
                        usersList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无用户</p>';
                    }
                }
            } catch (error) {
                console.error('加载用户失败:', error);
            }
        }

        // 加载任务列表
        async function loadTasks() {
            // 类似的实现...
            document.getElementById('tasksList').innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">任务管理功能开发中...</p>';
        }

        // 加载积分记录
        async function loadScores() {
            // 类似的实现...
            document.getElementById('scoresList').innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">积分记录功能开发中...</p>';
        }

        // 加载安全日志
        async function loadSecurityLogs() {
            // 类似的实现...
            document.getElementById('securityLogs').innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">安全日志功能开发中...</p>';
        }

        // 加载系统设置
        async function loadSettings() {
            // 类似的实现...
            document.getElementById('systemSettings').innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">系统设置功能开发中...</p>';
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('adminToken');
            authToken = null;
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('dashboard').style.display = 'none';
            document.getElementById('adminInfo').style.display = 'none';
        }

        // 获取用户组文本
        function getUserGroupText(group) {
            const groupMap = {
                'new_user': '新手用户',
                'regular_user': '普通用户',
                'advanced_user': '高级用户',
                'senior_user': '资深用户',
                'vip_user': 'VIP用户'
            };
            return groupMap[group] || group;
        }

        // 编辑用户
        function editUser(userId) {
            alert('编辑用户功能开发中...');
        }

        // 切换用户状态
        function toggleUserStatus(userId, currentStatus) {
            alert('切换用户状态功能开发中...');
        }

        // 搜索用户
        function searchUsers() {
            alert('搜索用户功能开发中...');
        }

        // 搜索任务
        function searchTasks() {
            alert('搜索任务功能开发中...');
        }
    </script>
</body>
</html>
