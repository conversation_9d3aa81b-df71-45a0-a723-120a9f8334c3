# /etc/nginx/conf.d/seo-system.conf
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    root /var/www/seo-system/public;
    index index.php index.html;
    
    # SSL证书配置
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # 安全头设置
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 限制请求大小
    client_max_body_size 10M;
    client_body_buffer_size 128k;
    
    # 隐藏服务器信息
    server_tokens off;
    
    # 防止访问敏感文件
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(env|log|ini|sql)$ {
        deny all;
    }
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        fastcgi_hide_header X-Powered-By;
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # API路由
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # 主要路由
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # 日志配置
    access_log /var/log/nginx/seo_access.log;
    error_log /var/log/nginx/seo_error.log;
}