<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Cache;

/**
 * 系统优化命令
 */
class SystemOptimize extends Command
{
    protected function configure()
    {
        $this->setName('system:optimize')
            ->setDescription('系统性能优化');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始系统优化...');
        
        try {
            // 数据库优化
            $this->optimizeDatabase($output);
            
            // 缓存优化
            $this->optimizeCache($output);
            
            // 文件优化
            $this->optimizeFiles($output);
            
            // 配置优化
            $this->optimizeConfig($output);
            
            // 生成优化报告
            $this->generateOptimizeReport($output);
            
            $output->writeln('系统优化完成');
            
        } catch (\Exception $e) {
            $output->writeln('系统优化失败：' . $e->getMessage());
        }
    }
    
    /**
     * 数据库优化
     */
    private function optimizeDatabase(Output $output)
    {
        $output->writeln('优化数据库...');
        
        try {
            // 获取所有表
            $tables = Db::query('SHOW TABLES');
            $tableCount = 0;
            
            foreach ($tables as $table) {
                $tableName = array_values($table)[0];
                
                // 优化表
                Db::execute("OPTIMIZE TABLE `{$tableName}`");
                
                // 分析表
                Db::execute("ANALYZE TABLE `{$tableName}`");
                
                $tableCount++;
            }
            
            $output->writeln("已优化 {$tableCount} 个数据表");
            
            // 检查索引使用情况
            $this->checkIndexUsage($output);
            
            // 检查慢查询
            $this->checkSlowQueries($output);
            
        } catch (\Exception $e) {
            $output->writeln('数据库优化失败：' . $e->getMessage());
        }
    }
    
    /**
     * 检查索引使用情况
     */
    private function checkIndexUsage(Output $output)
    {
        try {
            // 检查未使用的索引
            $unusedIndexes = Db::query("
                SELECT DISTINCT 
                    s.table_schema,
                    s.table_name,
                    s.index_name
                FROM information_schema.statistics s
                LEFT JOIN information_schema.index_statistics i 
                    ON s.table_schema = i.table_schema 
                    AND s.table_name = i.table_name 
                    AND s.index_name = i.index_name
                WHERE s.table_schema = DATABASE()
                    AND s.index_name != 'PRIMARY'
                    AND i.index_name IS NULL
            ");
            
            if (!empty($unusedIndexes)) {
                $output->writeln('发现未使用的索引：');
                foreach ($unusedIndexes as $index) {
                    $output->writeln("  - {$index['table_name']}.{$index['index_name']}");
                }
            }
            
        } catch (\Exception $e) {
            $output->writeln('索引检查失败：' . $e->getMessage());
        }
    }
    
    /**
     * 检查慢查询
     */
    private function checkSlowQueries(Output $output)
    {
        try {
            // 检查慢查询日志是否开启
            $slowLogStatus = Db::query("SHOW VARIABLES LIKE 'slow_query_log'");
            
            if (!empty($slowLogStatus) && $slowLogStatus[0]['Value'] == 'ON') {
                $output->writeln('慢查询日志已开启');
                
                // 获取慢查询统计
                $slowQueryCount = Db::query("SHOW GLOBAL STATUS LIKE 'Slow_queries'");
                if (!empty($slowQueryCount)) {
                    $count = $slowQueryCount[0]['Value'];
                    $output->writeln("慢查询数量：{$count}");
                }
            } else {
                $output->writeln('建议开启慢查询日志以监控性能');
            }
            
        } catch (\Exception $e) {
            $output->writeln('慢查询检查失败：' . $e->getMessage());
        }
    }
    
    /**
     * 缓存优化
     */
    private function optimizeCache(Output $output)
    {
        $output->writeln('优化缓存...');
        
        try {
            // 清理过期缓存
            $this->clearExpiredCache($output);
            
            // 预热重要缓存
            $this->warmupCache($output);
            
            // 检查缓存命中率
            $this->checkCacheHitRate($output);
            
        } catch (\Exception $e) {
            $output->writeln('缓存优化失败：' . $e->getMessage());
        }
    }
    
    /**
     * 清理过期缓存
     */
    private function clearExpiredCache(Output $output)
    {
        try {
            // 清理应用缓存
            Cache::clear();
            $output->writeln('已清理应用缓存');
            
            // 如果使用Redis，清理过期键
            if (config('cache.default') == 'redis') {
                $redis = Cache::store('redis')->handler();
                if ($redis) {
                    $expiredKeys = $redis->eval("
                        local keys = redis.call('keys', ARGV[1])
                        local expired = {}
                        for i=1,#keys do
                            if redis.call('ttl', keys[i]) == -1 then
                                table.insert(expired, keys[i])
                            end
                        end
                        return expired
                    ", 0, '*');
                    
                    if (!empty($expiredKeys)) {
                        $redis->del($expiredKeys);
                        $count = count($expiredKeys);
                        $output->writeln("已清理 {$count} 个过期缓存键");
                    }
                }
            }
            
        } catch (\Exception $e) {
            $output->writeln('清理过期缓存失败：' . $e->getMessage());
        }
    }
    
    /**
     * 预热缓存
     */
    private function warmupCache(Output $output)
    {
        try {
            // 预热系统配置
            config('system');
            Cache::set('system_config', config('system'), 3600);
            
            // 预热用户等级配置
            Cache::set('user_groups', config('system.user_groups'), 3600);
            
            // 预热常用数据
            $activeUsers = Db::name('users')
                ->where('status', 1)
                ->where('last_login_time', '>', time() - 86400 * 7)
                ->count();
            Cache::set('active_users_count', $activeUsers, 1800);
            
            $output->writeln('已预热重要缓存');
            
        } catch (\Exception $e) {
            $output->writeln('缓存预热失败：' . $e->getMessage());
        }
    }
    
    /**
     * 检查缓存命中率
     */
    private function checkCacheHitRate(Output $output)
    {
        try {
            if (config('cache.default') == 'redis') {
                $redis = Cache::store('redis')->handler();
                if ($redis) {
                    $info = $redis->info('stats');
                    if (isset($info['keyspace_hits']) && isset($info['keyspace_misses'])) {
                        $hits = $info['keyspace_hits'];
                        $misses = $info['keyspace_misses'];
                        $total = $hits + $misses;
                        
                        if ($total > 0) {
                            $hitRate = round(($hits / $total) * 100, 2);
                            $output->writeln("Redis缓存命中率：{$hitRate}%");
                            
                            if ($hitRate < 80) {
                                $output->writeln('警告：缓存命中率较低，建议检查缓存策略');
                            }
                        }
                    }
                }
            }
            
        } catch (\Exception $e) {
            $output->writeln('缓存命中率检查失败：' . $e->getMessage());
        }
    }
    
    /**
     * 文件优化
     */
    private function optimizeFiles(Output $output)
    {
        $output->writeln('优化文件...');
        
        try {
            // 清理临时文件
            $this->cleanTempFiles($output);
            
            // 清理日志文件
            $this->cleanLogFiles($output);
            
            // 检查磁盘空间
            $this->checkDiskSpace($output);
            
        } catch (\Exception $e) {
            $output->writeln('文件优化失败：' . $e->getMessage());
        }
    }
    
    /**
     * 清理临时文件
     */
    private function cleanTempFiles(Output $output)
    {
        try {
            $tempPath = runtime_path() . 'temp/';
            if (is_dir($tempPath)) {
                $files = glob($tempPath . '*');
                $count = 0;
                
                foreach ($files as $file) {
                    if (is_file($file) && filemtime($file) < time() - 86400) {
                        unlink($file);
                        $count++;
                    }
                }
                
                $output->writeln("已清理 {$count} 个临时文件");
            }
            
        } catch (\Exception $e) {
            $output->writeln('清理临时文件失败：' . $e->getMessage());
        }
    }
    
    /**
     * 清理日志文件
     */
    private function cleanLogFiles(Output $output)
    {
        try {
            $logPath = runtime_path() . 'log/';
            if (is_dir($logPath)) {
                $files = glob($logPath . '*.log');
                $count = 0;
                $totalSize = 0;
                
                foreach ($files as $file) {
                    if (is_file($file) && filemtime($file) < time() - 86400 * 30) {
                        $totalSize += filesize($file);
                        unlink($file);
                        $count++;
                    }
                }
                
                $sizeInMB = round($totalSize / 1024 / 1024, 2);
                $output->writeln("已清理 {$count} 个日志文件，释放空间 {$sizeInMB}MB");
            }
            
        } catch (\Exception $e) {
            $output->writeln('清理日志文件失败：' . $e->getMessage());
        }
    }
    
    /**
     * 检查磁盘空间
     */
    private function checkDiskSpace(Output $output)
    {
        try {
            $totalSpace = disk_total_space('.');
            $freeSpace = disk_free_space('.');
            $usedSpace = $totalSpace - $freeSpace;
            
            $usagePercent = round(($usedSpace / $totalSpace) * 100, 2);
            $freeSpaceGB = round($freeSpace / 1024 / 1024 / 1024, 2);
            
            $output->writeln("磁盘使用率：{$usagePercent}%，剩余空间：{$freeSpaceGB}GB");
            
            if ($usagePercent > 90) {
                $output->writeln('警告：磁盘空间不足，建议清理或扩容');
            }
            
        } catch (\Exception $e) {
            $output->writeln('磁盘空间检查失败：' . $e->getMessage());
        }
    }
    
    /**
     * 配置优化
     */
    private function optimizeConfig(Output $output)
    {
        $output->writeln('检查配置优化...');
        
        try {
            $recommendations = [];
            
            // 检查PHP配置
            if (ini_get('memory_limit') < 256) {
                $recommendations[] = '建议将PHP内存限制调整为256M或更高';
            }
            
            if (ini_get('max_execution_time') < 60) {
                $recommendations[] = '建议将PHP最大执行时间调整为60秒或更高';
            }
            
            if (!extension_loaded('opcache')) {
                $recommendations[] = '建议启用OPcache以提高PHP性能';
            }
            
            // 检查数据库配置
            $maxConnections = Db::query("SHOW VARIABLES LIKE 'max_connections'");
            if (!empty($maxConnections) && $maxConnections[0]['Value'] < 200) {
                $recommendations[] = '建议增加MySQL最大连接数';
            }
            
            if (!empty($recommendations)) {
                $output->writeln('配置优化建议：');
                foreach ($recommendations as $recommendation) {
                    $output->writeln("  - {$recommendation}");
                }
            } else {
                $output->writeln('配置检查通过');
            }
            
        } catch (\Exception $e) {
            $output->writeln('配置检查失败：' . $e->getMessage());
        }
    }
    
    /**
     * 生成优化报告
     */
    private function generateOptimizeReport(Output $output)
    {
        try {
            $report = [
                'optimize_time' => date('Y-m-d H:i:s'),
                'database' => [
                    'tables_optimized' => true,
                    'indexes_analyzed' => true
                ],
                'cache' => [
                    'expired_cleared' => true,
                    'warmed_up' => true
                ],
                'files' => [
                    'temp_cleaned' => true,
                    'logs_cleaned' => true
                ],
                'system_info' => [
                    'php_version' => PHP_VERSION,
                    'memory_usage' => memory_get_peak_usage(true),
                    'disk_usage' => disk_total_space('.') - disk_free_space('.')
                ]
            ];
            
            $reportFile = runtime_path() . 'optimize_report_' . date('Ymd_His') . '.json';
            file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            
            $output->writeln("优化报告已生成：{$reportFile}");
            
        } catch (\Exception $e) {
            $output->writeln('生成优化报告失败：' . $e->getMessage());
        }
    }
}
